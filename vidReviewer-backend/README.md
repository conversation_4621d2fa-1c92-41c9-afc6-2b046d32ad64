# VidReviewer

## Overview

This project is a backend application built using Node.js and Express. It provides various API endpoints for authentication, user management, project handling, video processing, and more. The application is structured to handle different routes for different functionalities, making it modular and easy to maintain.

## Project Structure

The main routing file is `routes/index.js`, which imports and uses various route modules. Below is a brief description of each route:

- **/auth**: Handles authentication-related operations.
- **/oauth/callback**: Manages OAuth callback operations.
- **/auth/refresh**: Provides token refresh functionality.
- **/auth/refresh/agency**: Handles agency-specific token refresh.
- **/users**: Manages user-related operations.
- **/webhook**: Handles webhook events.
- **/projects**: Manages project-related operations.
- **/videos**: Handles video-related operations.
- **/comments**: <PERSON><PERSON> comments on videos or projects.
- **/decrypt-sso**: Handles single sign-on decryption.
- **/contacts**: Manages contact-related operations.
- **/notifications**: Handles notifications.
- **/invitations**: Manages invitation-related operations.

## Architecture

This application uses a hybrid architecture:
- **App Engine**: Main application handling API requests, authentication, and database operations
- **Cloud Run**: Dedicated service for video transcoding and thumbnail generation

```
Video Upload → Pub/Sub → App Engine → Cloud Run Service → Transcoder API
                                                       → Thumbnail Generation
```

## Getting Started

### Prerequisites

- Node.js (version >=20)
- npm (version >=10.8.2)
- Google Cloud SDK
- Docker (for Cloud Run deployment)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/Vidlead/vidReviewer-backend
   ```

2. Navigate to the project directory:
   ```bash
   cd vidReviewer-backend
   ```

3. Install the dependencies:
   ```bash
   npm install
   ```

### Quick Deployment

For a complete deployment including Cloud Run transcoding service:

```bash
# Deploy Cloud Run transcoding service
cd transcoding-service
./deploy.sh

# Update app.yaml with the Cloud Run service URL
# Then deploy main application
gcloud app deploy
```

See [QUICK_START.md](QUICK_START.md) for detailed instructions.

### Running Locally

To start the application locally:

```bash
npm start
```

The server will start on the configured port (default is 3000). You can access the API endpoints via `http://localhost:3000`.

### Environment Variables

Create a `.env` file in the root directory with the following variables:

- `GOOGLE_APPLICATION_CREDENTIALS`: Path to the Google application credentials JSON file.
- `CLIENT_ID`: OAuth client ID.
- `CLIENT_SECRET`: OAuth client secret.
- `BASE_URL`: Base URL for the application.
- `MONGO_URI`: MongoDB connection URI.
- `REDIRECT_URI`: OAuth redirect URI.
- `ENCRYPTION_KEY`: Key used for encryption.
- `ENCRYPTION_IV`: Initialization vector for encryption.
- `GHL_APP_SSO_KEY`: Single sign-on key for the application, from ghl.
- `PUBSUB_TOPIC`: Pub/Sub topic for video uploads.
- `PUBSUB_SUBSCRIPTION`: Pub/Sub subscription for video uploads.
- `STORAGE_BUCKET`: Cloud storage bucket name.
- `TRANSCODING_SERVICE_URL`: URL of the Cloud Run transcoding service.

## Cloud Run Transcoding Service

The video transcoding functionality has been offloaded to a dedicated Cloud Run service for better performance and scalability.

### Features
- **Independent Scaling**: Transcoding scales separately from the main application
- **Cost Optimization**: Pay only for transcoding compute time
- **Better Performance**: Dedicated resources for video processing
- **Improved Reliability**: Isolated failure domain

### Deployment
See the [transcoding-service/](transcoding-service/) directory for:
- Docker configuration
- Deployment scripts
- Service documentation
- Testing tools

### Migration Guide
- [CLOUD_RUN_MIGRATION.md](CLOUD_RUN_MIGRATION.md) - Complete migration guide
- [QUICK_START.md](QUICK_START.md) - 5-minute deployment guide
- [DEPLOYMENT_CHECKLIST.md](DEPLOYMENT_CHECKLIST.md) - Comprehensive checklist
