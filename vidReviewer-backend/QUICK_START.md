# Quick Start: Cloud Run Transcoding Migration

Get your video transcoding offloaded to Cloud Run in 5 minutes!

## 🚀 Quick Deployment (5 minutes)

### Step 1: Deploy Cloud Run Service (2 minutes)
```bash
cd transcoding-service
./deploy.sh
```

**Wait for output like:**
```
Service URL: https://transcoding-service-abc123-uc.a.run.app
```

### Step 2: Update Main App Configuration (1 minute)
Edit `app.yaml` and update the service URL:
```yaml
env_variables:
  # ... existing variables ...
  TRANSCODING_SERVICE_URL: 'https://transcoding-service-abc123-uc.a.run.app'
```

### Step 3: Deploy Main Application (2 minutes)
```bash
gcloud app deploy
```

### Step 4: Validate (30 seconds)
```bash
./validate-deployment.sh
```

**That's it!** Your video transcoding is now running on Cloud Run! 🎉

---

## 🧪 Quick Test

Upload a video through your application and check:
1. Cloud Run logs: `gcloud logs read --service=transcoding-service --limit=10`
2. Video processing completes successfully
3. HLS files appear in Cloud Storage under `hls/VIDEO_ID/`

---

## 🔧 Troubleshooting

### Issue: Service URL not working
**Solution:** Check the actual URL from deployment:
```bash
gcloud run services describe transcoding-service --region=us-central1 --format='value(status.url)'
```

### Issue: Permission errors
**Solution:** Ensure service account has required roles:
```bash
gcloud projects add-iam-policy-binding vidlead-prod \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/transcoder.admin"
```

### Issue: Transcoding fails
**Solution:** Check Cloud Run logs:
```bash
gcloud logs read --service=transcoding-service --severity=ERROR --limit=5
```

---

## 📊 What Changed

### Before (App Engine only):
```
Video Upload → Pub/Sub → App Engine → Transcoder API
```

### After (App Engine + Cloud Run):
```
Video Upload → Pub/Sub → App Engine → Cloud Run → Transcoder API
```

### Benefits:
- ✅ **Better Performance**: Dedicated resources for transcoding
- ✅ **Cost Savings**: Pay only for transcoding time
- ✅ **Scalability**: Independent scaling for video processing
- ✅ **Reliability**: Isolated failure domain

---

## 🔍 Monitoring

### Check Service Health:
```bash
curl https://your-transcoding-service-url/health
```

### View Recent Activity:
```bash
gcloud logs read --service=transcoding-service --limit=20
```

### Monitor Performance:
- Go to Cloud Console → Cloud Run → transcoding-service
- Check metrics for requests, latency, and errors

---

## 🆘 Need Help?

### Common Commands:
```bash
# Redeploy service
cd transcoding-service && ./deploy.sh

# Check service status
gcloud run services list --region=us-central1

# View detailed logs
gcloud logs read --service=transcoding-service --limit=50

# Test service endpoints
node transcoding-service/test-service.js https://your-service-url
```

### Rollback (if needed):
1. Remove `TRANSCODING_SERVICE_URL` from `app.yaml`
2. Restore original `services/transcoder.js` from git
3. Run `gcloud app deploy`

---

## 📈 Next Steps

1. **Monitor Performance**: Watch transcoding times and success rates
2. **Optimize Costs**: Adjust Cloud Run configuration based on usage
3. **Add Features**: Consider video analysis, quality checks, etc.
4. **Scale Further**: Implement queue-based processing for high volume

---

**🎯 Success!** Your video transcoding is now running efficiently on Cloud Run!
