import mongoose from 'mongoose';

const { Schema, model, Types } = mongoose;

// Enum for comment status
export const CommentStatus = Object.freeze({
    PUBLISHED: 'published',
    PENDING: 'pending',
    DELETED: 'deleted',
});

const commentSchema = new Schema(
    {
        videoId: { type: Types.ObjectId, required: true, ref: 'Video' },
        userId: { type: Types.ObjectId, required: true, ref: 'User' },
        content: { type: String, required: true },
        addedBy: { type: String, required: true, default: 'Anonymous' },
        status: {
            type: String,
            enum: Object.values(CommentStatus),
            default: CommentStatus.PUBLISHED,
        },
        parentCommentId: { type: Types.ObjectId, ref: 'Comment', default: null },
        time: { type: String, required: true }, 
        replies: [{ type: Types.ObjectId, ref: 'Comment' }],
    },
    {
        timestamps: true, 
    },
   
);

Object.assign(commentSchema.statics, { CommentStatus });

const Comment = model('Comment', commentSchema);

export default Comment;
