import mongoose from 'mongoose';
const videoSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: false,
        default: ''
    },
    projectId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Project',
        required: true
    },
    projectFolderId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Folder',
        default: null // null means video is in project root
    },
    status: {
        type: String,
        enum: ['draft', 'processing', 'transcoding', 'published', 'normal', 'archived', 'deleted', 'complete', 'failed'],
        default: 'normal'
    },
    uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    videoUrl: {
        type: String,
        required: true
    },
    hlsPlaylistUrl: {
        type: String,
    },
    videoThumbnail: {
        type: String,
    },
    customThumbnail: {
        type: String,
    },
    thumbnailType: {
        type: String,
        enum: ['auto', 'custom'],
        default: 'auto'
    },
    processingError: {
        type: String,
    },
    transcodingJobName: {
        type: String,
    },
    uploadedOn: {
        type: Date,
        default: Date.now
    },
    comments: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Comment'
    }],
    isDownloadable: {
        type: Boolean,
        default: false
    },
    isCommentsEnabled: {
        type: Boolean,
        default: true
    },
    height: {
        type: Number,
        default: 0,
        required: false
    },
    width: {
        type: Number,
        default: 0,
        required: false
    },
    size: {
        type: Number,
        default: 0,
        required: false
    },
    duration: {
        type: Number,
        default: 0, // Duration in seconds
        required: false
    },
    storageClass: {
        type: String,
        enum: ['normal', 'archived'],
        default: 'normal'
    }
}, {
    timestamps: true
});

// Index for efficient project folder queries
videoSchema.index({ projectId: 1, projectFolderId: 1 });

export default mongoose.model('Video', videoSchema, 'videos');