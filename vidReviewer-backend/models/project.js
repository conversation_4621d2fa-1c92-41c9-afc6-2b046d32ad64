import mongoose from 'mongoose';

const collaboratorSchema = new mongoose.Schema({
    addedOn: {
        type: Date,
        default: Date.now
    },
    addedBy: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'User'
    },
    permissions: {
        canEdit: {
            type: Boolean,
            default: false
        },
        canShare: {
            type: Boolean,
            default: false
        },
        canDelete: {
            type: Boolean,
            default: false
        }
    }
});

const projectSchema = new mongoose.Schema({
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'User'
    },
    name: {
        type: String,
        required: true,
        trim: true
    },
    email: {
        type: String,
        required: true,
        trim: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    title: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    videoCount: {
        type: Number,
        default: 0
    },
    folderId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Folder',
        default: null // null means project is in root folder
    },
    collaborators: [collaboratorSchema]
});

// Index for efficient folder-based queries
projectSchema.index({ createdBy: 1, folderId: 1 });

const Project = mongoose.model('Project', projectSchema, 'projects');

export default Project;