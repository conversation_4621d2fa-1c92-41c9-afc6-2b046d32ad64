import mongoose from 'mongoose';

const { Schema, model, Types } = mongoose;

const agencySchema = new Schema({
    access_token: { type: String, required: true },
    refresh_token: { type: String, required: true },
    companyId: { type: String, required: true, unique: true },
    userId: { type: String, required: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});

const Agency = model('Agency', agencySchema, 'agencies');

export default Agency;