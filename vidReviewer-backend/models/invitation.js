import mongoose from 'mongoose';

const invitationSchema = new mongoose.Schema({
    name: { type: String, required: true },
    email: { type: String, required: false},
    // Legacy field for backward compatibility
    projectId: { type: String, required: false },
    // New flexible resource sharing fields
    resourceType: {
        type: String,
        enum: ['project', 'folder', 'video'],
        required: true,
        default: 'project'
    },
    resourceId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        refPath: 'resourceModel'
    },
    resourceModel: {
        type: String,
        required: true,
        enum: ['Project', 'Folder', 'Video'],
        default: 'Project'
    },
    token: { type: String, required: true, unique: true },
    status: { type: String, enum: ['pending', 'accepted'], default: 'pending' },
    contactId: { type: String, required: false },
    expiresAt: { type: Date, required: true, default: Date.now() + 1000 * 60 * 60 * 24 * 90 },
    createdAt: { type: Date, default: Date.now }
});

// Pre-save middleware for backward compatibility
invitationSchema.pre('save', function(next) {
    // Handle backward compatibility: if projectId is provided but resourceId is not
    if (this.projectId && !this.resourceId) {
        this.resourceId = this.projectId;
        this.resourceType = 'project';
        this.resourceModel = 'Project';
    }

    // Ensure resourceModel matches resourceType
    if (this.resourceType === 'project') {
        this.resourceModel = 'Project';
    } else if (this.resourceType === 'folder') {
        this.resourceModel = 'Folder';
    } else if (this.resourceType === 'video') {
        this.resourceModel = 'Video';
    }

    next();
});

// Index for efficient queries
invitationSchema.index({ token: 1 });
invitationSchema.index({ resourceId: 1, resourceType: 1 });
invitationSchema.index({ email: 1, resourceId: 1, status: 1 });

const Invitation = mongoose.model('Invitation', invitationSchema, 'invitations');

export default Invitation;