import mongoose from 'mongoose';

const folderSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'User'
    },
    contextType: {
        type: String,
        enum: ['user', 'project'],
        required: true,
        default: 'user'
    },
    contextId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Project',
        default: null,
        validate: {
            validator: function(value) {
                // contextId is required when contextType is 'project'
                if (this.contextType === 'project') {
                    return value != null;
                }
                // contextId should be null when contextType is 'user'
                return value == null;
            },
            message: 'contextId is required for project folders and should be null for user folders'
        }
    },
    parentFolder: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Folder',
        default: null // null means it's a root folder
    },
    path: {
        type: String,
        required: true,
        index: true // For efficient path-based queries
    },
    level: {
        type: Number,
        required: true,
        default: 0 // Root folders have level 0
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Compound index for efficient queries by user and parent folder
folderSchema.index({ createdBy: 1, parentFolder: 1 });

// Compound index for path-based queries within user scope
folderSchema.index({ createdBy: 1, path: 1 });

// Compound index for context-based queries
folderSchema.index({ contextType: 1, contextId: 1, parentFolder: 1 });

// Compound index for project folder queries
folderSchema.index({ contextType: 1, contextId: 1, createdBy: 1 });

// Pre-validate middleware to update the path and level
folderSchema.pre('validate', async function(next) {
    try {
        // Removed 3 sub-project limit validation

        // Always ensure path and level are set for new documents or when relevant fields change
        if (this.isNew || this.isModified('parentFolder') || this.isModified('name') || !this.path) {
            if (this.parentFolder) {
                // Get parent folder to build path and level
                const Folder = this.constructor;
                const parent = await Folder.findById(this.parentFolder);
                if (parent) {
                    // Validate that parent is in the same context
                    if (parent.contextType !== this.contextType ||
                        parent.contextId?.toString() !== this.contextId?.toString()) {
                        return next(new Error('Parent folder must be in the same context'));
                    }
                    this.path = `${parent.path}/${this.name}`;
                    this.level = parent.level + 1;

                } else {
                    return next(new Error('Parent folder not found'));
                }
            } else {
                // Root folder
                this.path = `/${this.name}`;
                this.level = 0;

            }
        }

        // Ensure level is set if not already
        if (this.level === undefined || this.level === null) {
            this.level = this.parentFolder ? 1 : 0; // Default fallback
        }

        this.updatedAt = new Date();
        next();
    } catch (error) {
        console.error('Folder pre-save middleware error:', error);
        next(error);
    }
});

// Instance method to get all descendant folders
folderSchema.methods.getDescendants = async function() {
    return await this.constructor.find({
        createdBy: this.createdBy,
        path: { $regex: `^${this.path}/` }
    }).sort({ path: 1 });
};

// Instance method to get direct children folders
folderSchema.methods.getChildren = async function() {
    return await this.constructor.find({
        createdBy: this.createdBy,
        parentFolder: this._id
    }).sort({ name: 1 });
};

// Static method to get folder tree for a user or project
folderSchema.statics.getFolderTree = async function(contextType, contextId, createdBy, parentFolderId = null) {
    const query = {
        contextType,
        createdBy,
        parentFolder: parentFolderId
    };

    if (contextType === 'project') {
        query.contextId = contextId;
    } else {
        query.contextId = null;
    }

    const folders = await this.find(query).sort({ name: 1 });

    const folderTree = [];
    for (const folder of folders) {
        const children = await this.getFolderTree(contextType, contextId, createdBy, folder._id);
        folderTree.push({
            ...folder.toObject(),
            children
        });
    }

    return folderTree;
};

// Static method to validate folder move operation (prevent circular references)
folderSchema.statics.canMoveFolder = async function(folderId, newParentId) {
    if (!newParentId) return true; // Moving to root is always allowed
    
    const folder = await this.findById(folderId);
    if (!folder) return false;
    
    // Check if newParentId is a descendant of folderId
    const newParent = await this.findById(newParentId);
    if (!newParent) return false;
    
    // If new parent's path starts with current folder's path, it's a circular reference
    return !newParent.path.startsWith(folder.path + '/') && newParent._id.toString() !== folderId;
};

const Folder = mongoose.model('Folder', folderSchema, 'folders');

export default Folder;
