import mongoose from 'mongoose';

const userSchema = new mongoose.Schema({
    storageType: { type: { normal: { type: Number, default: 0 }, archived: { type: Number, default: 0 } }, required: true, default: { normal: 0, archived: 0 } },
    appId: { type: String, required: false },
    locationId: { type: String, required: true, unique: true },
    companyId: { type: String, required: true },
    userId: { type: String, required: true },
    accessToken: { type: String, required: false },
    refreshToken: { type: String, required: false },
    createdAt: { type: Date, default: Date.now },
    email: { type: String, required: false },
    storageData:{type: Number, default: 0},
    userName: { type: String, required: false },
    updatedAt: { type: Date, default: Date.now },
});

const User = mongoose.model('User', userSchema,'users');

export default User;
