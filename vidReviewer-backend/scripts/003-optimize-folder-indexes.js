#!/usr/bin/env node

/**
 * Index Optimization Script: Folder Performance Indexes
 * 
 * This script creates and optimizes database indexes for folder functionality:
 * 1. Creates compound indexes for efficient folder queries
 * 2. Analyzes query performance
 * 3. Creates additional indexes based on usage patterns
 * 4. Provides performance recommendations
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';

// Load environment variables
dotenv.config();

// Import models
import Project from '../models/project.js';
import Folder from '../models/folder.js';

const MIGRATION_VERSION = '003-optimize-folder-indexes';

async function runIndexOptimization() {
    console.log(`🚀 Starting index optimization: ${MIGRATION_VERSION}`);
    console.log('===============================================');

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Connected to database');

        // Check if optimization has already been run
        const migrationRecord = await mongoose.connection.db
            .collection('migrations')
            .findOne({ version: MIGRATION_VERSION });

        if (migrationRecord) {
            console.log('⚠️  Index optimization already applied. Skipping...');
            return;
        }

        // Step 1: Analyze current index usage
        console.log('\n📊 Step 1: Analyzing current index usage...');

        // Get existing indexes using getIndexes() method
        const projectIndexes = await Project.collection.getIndexes();
        const folderIndexes = await Folder.collection.getIndexes();

        console.log(`Projects collection has ${Object.keys(projectIndexes).length} indexes`);
        console.log(`Folders collection has ${Object.keys(folderIndexes).length} indexes`);

        // List existing indexes
        console.log('Existing project indexes:', Object.keys(projectIndexes).join(', '));
        console.log('Existing folder indexes:', Object.keys(folderIndexes).join(', '));

        // Step 2: Create optimized indexes for projects
        console.log('\n📋 Step 2: Creating optimized indexes for projects...');

        const newProjectIndexes = [
            {
                name: 'createdBy_1_folderId_1_createdAt_-1',
                spec: { createdBy: 1, folderId: 1, createdAt: -1 },
                description: 'Efficient folder-based project queries with sorting'
            },
            {
                name: 'folderId_1_createdAt_-1',
                spec: { folderId: 1, createdAt: -1 },
                description: 'Folder contents with date sorting'
            },
            {
                name: 'createdBy_1_title_text',
                spec: { createdBy: 1, title: 'text', description: 'text' },
                description: 'Text search within user projects'
            }
        ];

        for (const index of newProjectIndexes) {
            try {
                await Project.collection.createIndex(index.spec, { 
                    name: index.name,
                    background: true 
                });
                console.log(`✅ Created index: projects.${index.name}`);
                console.log(`   Description: ${index.description}`);
            } catch (error) {
                if (error.code === 85) { // Index already exists
                    console.log(`✅ Index already exists: projects.${index.name}`);
                } else {
                    console.log(`⚠️  Could not create index projects.${index.name}: ${error.message}`);
                }
            }
        }

        // Step 3: Create optimized indexes for folders
        console.log('\n📁 Step 3: Creating optimized indexes for folders...');

        const newFolderIndexes = [
            {
                name: 'createdBy_1_parentFolder_1_name_1',
                spec: { createdBy: 1, parentFolder: 1, name: 1 },
                description: 'Efficient folder tree queries with name sorting'
            },
            {
                name: 'path_1_createdBy_1',
                spec: { path: 1, createdBy: 1 },
                description: 'Path-based folder lookups'
            },
            {
                name: 'createdBy_1_level_1_name_1',
                spec: { createdBy: 1, level: 1, name: 1 },
                description: 'Level-based folder queries'
            },
            {
                name: 'parentFolder_1_name_1',
                spec: { parentFolder: 1, name: 1 },
                description: 'Subfolder queries with name sorting'
            }
        ];

        for (const index of newFolderIndexes) {
            try {
                await Folder.collection.createIndex(index.spec, { 
                    name: index.name,
                    background: true 
                });
                console.log(`✅ Created index: folders.${index.name}`);
                console.log(`   Description: ${index.description}`);
            } catch (error) {
                if (error.code === 85) { // Index already exists
                    console.log(`✅ Index already exists: folders.${index.name}`);
                } else {
                    console.log(`⚠️  Could not create index folders.${index.name}: ${error.message}`);
                }
            }
        }

        // Step 4: Analyze query performance
        console.log('\n🔍 Step 4: Analyzing query performance...');
        
        // Test common queries and their performance (using sample ObjectIds)
        const sampleUserId = new mongoose.Types.ObjectId();
        const sampleFolderId = new mongoose.Types.ObjectId();

        const testQueries = [
            {
                name: 'Get user projects in root folder',
                collection: 'projects',
                query: { createdBy: sampleUserId, folderId: null }
            },
            {
                name: 'Get user projects in specific folder',
                collection: 'projects',
                query: { createdBy: sampleUserId, folderId: sampleFolderId }
            },
            {
                name: 'Get user root folders',
                collection: 'folders',
                query: { createdBy: sampleUserId, parentFolder: null }
            },
            {
                name: 'Get folder by path',
                collection: 'folders',
                query: { path: '/test-folder' }
            }
        ];

        for (const testQuery of testQueries) {
            try {
                const collection = mongoose.connection.db.collection(testQuery.collection);
                const explainResult = await collection.find(testQuery.query).explain('executionStats');

                const stats = explainResult.executionStats;
                console.log(`\n📈 Query: ${testQuery.name}`);
                console.log(`   Execution time: ${stats.executionTimeMillis || 0}ms`);
                console.log(`   Documents examined: ${stats.totalDocsExamined || 0}`);
                console.log(`   Documents returned: ${stats.totalDocsReturned || 0}`);

                // Handle different MongoDB versions and plan structures
                let indexUsed = 'COLLSCAN';
                if (stats.winningPlan) {
                    if (stats.winningPlan.inputStage?.indexName) {
                        indexUsed = stats.winningPlan.inputStage.indexName;
                    } else if (stats.winningPlan.indexName) {
                        indexUsed = stats.winningPlan.indexName;
                    }
                }
                console.log(`   Index used: ${indexUsed}`);

                if (stats.totalDocsExamined > (stats.totalDocsReturned || 0) * 10) {
                    console.log(`   ⚠️  Warning: High document examination ratio`);
                }
            } catch (error) {
                console.log(`   ❌ Could not analyze query: ${error.message}`);
            }
        }

        // Step 5: Create sparse indexes for optional fields
        console.log('\n🎯 Step 5: Creating sparse indexes for optional fields...');
        
        try {
            await Project.collection.createIndex(
                { folderId: 1 },
                { 
                    name: 'folderId_1_sparse',
                    sparse: true,
                    background: true
                }
            );
            console.log('✅ Created sparse index: projects.folderId_1_sparse');
        } catch (error) {
            if (error.code === 85) {
                console.log('✅ Sparse index already exists: projects.folderId_1_sparse');
            } else {
                console.log(`⚠️  Could not create sparse index: ${error.message}`);
            }
        }

        // Step 6: Performance recommendations
        console.log('\n💡 Step 6: Performance recommendations...');
        
        const projectCount = await Project.countDocuments();
        const folderCount = await Folder.countDocuments();
        const avgProjectsPerUser = await Project.aggregate([
            { $group: { _id: '$createdBy', count: { $sum: 1 } } },
            { $group: { _id: null, avgCount: { $avg: '$count' } } }
        ]);

        console.log(`\n📊 Collection statistics:`);
        console.log(`   Total projects: ${projectCount}`);
        console.log(`   Total folders: ${folderCount}`);
        console.log(`   Average projects per user: ${avgProjectsPerUser[0]?.avgCount?.toFixed(2) || 0}`);

        if (projectCount > 10000) {
            console.log('\n💡 Recommendations for large dataset:');
            console.log('   - Consider partitioning by user or date');
            console.log('   - Monitor index usage with db.collection.indexStats()');
            console.log('   - Consider archiving old projects');
        }

        if (folderCount > 1000) {
            console.log('\n💡 Recommendations for many folders:');
            console.log('   - Consider limiting folder depth');
            console.log('   - Monitor path-based queries performance');
            console.log('   - Consider folder name length limits');
        }

        // Step 7: Record optimization completion
        await mongoose.connection.db.collection('migrations').insertOne({
            version: MIGRATION_VERSION,
            appliedAt: new Date(),
            description: 'Optimize database indexes for folder functionality',
            stats: {
                projectCount,
                folderCount,
                avgProjectsPerUser: avgProjectsPerUser[0]?.avgCount || 0,
                indexesCreated: newProjectIndexes.length + newFolderIndexes.length + 1
            }
        });

        console.log('\n🎉 Index optimization completed successfully!');
        console.log('===============================================');

    } catch (error) {
        console.error('\n❌ Index optimization failed:', error);
        process.exit(1);
    } finally {
        await mongoose.connection.close();
        console.log('📡 Database connection closed');
    }
}

// Run optimization if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runIndexOptimization();
}

export default runIndexOptimization;
