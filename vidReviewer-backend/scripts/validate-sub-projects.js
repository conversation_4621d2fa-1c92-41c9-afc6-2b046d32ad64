#!/usr/bin/env node

/**
 * Sub-Project Validation Script
 * 
 * This script validates the current database state to ensure compliance with
 * the 3 sub-project limit and identifies any issues that need to be resolved.
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';

// Load environment variables
dotenv.config();

// Import models
import Project from '../models/project.js';
import Folder from '../models/folder.js';

async function validateSubProjects() {
    console.log('🔍 Sub-Project Validation Report');
    console.log('=================================');

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Connected to database');

        // Get all projects
        const projects = await Project.find({});
        console.log(`📊 Found ${projects.length} projects to validate`);

        let totalIssues = 0;
        const issueReports = [];

        for (const project of projects) {
            console.log(`\n📁 Validating project: ${project.title}`);
            
            // Count root-level sub-projects (contextType='project', parentFolder=null)
            const rootSubProjects = await Folder.find({
                contextType: 'project',
                contextId: project._id,
                parentFolder: null
            }).sort({ createdAt: 1 });

            const rootCount = rootSubProjects.length;
            console.log(`   Root sub-projects: ${rootCount}/3`);

            if (rootCount > 3) {
                totalIssues++;
                const issue = {
                    projectId: project._id,
                    projectTitle: project.title,
                    rootSubProjectCount: rootCount,
                    excessCount: rootCount - 3,
                    excessSubProjects: rootSubProjects.slice(3).map(sp => ({
                        id: sp._id,
                        name: sp.name,
                        createdAt: sp.createdAt
                    }))
                };
                issueReports.push(issue);
                console.log(`   ⚠️  ISSUE: ${rootCount} root sub-projects (${rootCount - 3} excess)`);
                
                // List excess sub-projects
                rootSubProjects.slice(3).forEach((sp, index) => {
                    console.log(`      ${index + 4}. ${sp.name} (Created: ${sp.createdAt.toISOString()})`);
                });
            } else {
                console.log(`   ✅ Compliant (${rootCount} ≤ 3)`);
            }

            // Count total sub-projects in this project
            const allSubProjects = await Folder.countDocuments({
                contextType: 'project',
                contextId: project._id
            });
            console.log(`   Total sub-projects: ${allSubProjects}`);

            // Check for orphaned sub-projects (parent doesn't exist)
            const subProjectsWithParent = await Folder.find({
                contextType: 'project',
                contextId: project._id,
                parentFolder: { $ne: null }
            });

            for (const sp of subProjectsWithParent) {
                const parentExists = await Folder.findById(sp.parentFolder);
                if (!parentExists) {
                    console.log(`   ⚠️  ORPHANED: ${sp.name} has non-existent parent ${sp.parentFolder}`);
                    totalIssues++;
                }
            }
        }

        // Summary Report
        console.log('\n📋 VALIDATION SUMMARY');
        console.log('=====================');
        console.log(`Total projects validated: ${projects.length}`);
        console.log(`Projects with issues: ${issueReports.length}`);
        console.log(`Total issues found: ${totalIssues}`);

        if (issueReports.length > 0) {
            console.log('\n⚠️  PROJECTS EXCEEDING 3 SUB-PROJECT LIMIT:');
            console.log('===========================================');
            
            issueReports.forEach((issue, index) => {
                console.log(`\n${index + 1}. Project: ${issue.projectTitle}`);
                console.log(`   Project ID: ${issue.projectId}`);
                console.log(`   Root sub-projects: ${issue.rootSubProjectCount}/3`);
                console.log(`   Excess sub-projects: ${issue.excessCount}`);
                console.log(`   Excess sub-projects to handle:`);
                
                issue.excessSubProjects.forEach((sp, spIndex) => {
                    console.log(`      ${spIndex + 1}. ${sp.name} (ID: ${sp.id})`);
                });
            });

            console.log('\n🔧 RECOMMENDED ACTIONS:');
            console.log('======================');
            console.log('1. Run the sub-project migration script to handle excess sub-projects');
            console.log('2. Or manually review and reorganize the excess sub-projects');
            console.log('3. Consider converting excess sub-projects to nested sub-projects');
            console.log('\nCommands:');
            console.log('  npm run sub-projects:migrate    # Auto-migrate excess sub-projects');
            console.log('  npm run sub-projects:report     # Generate detailed report');
        } else {
            console.log('\n✅ ALL PROJECTS COMPLIANT');
            console.log('=========================');
            console.log('All projects comply with the 3 sub-project limit.');
            console.log('No action required.');
        }

        // Database health check
        console.log('\n🏥 DATABASE HEALTH CHECK');
        console.log('========================');
        
        // Check for invalid contextType values
        const invalidContextTypes = await Folder.find({
            contextType: { $nin: ['user', 'project'] }
        });
        
        if (invalidContextTypes.length > 0) {
            console.log(`⚠️  Found ${invalidContextTypes.length} folders with invalid contextType`);
            totalIssues += invalidContextTypes.length;
        } else {
            console.log('✅ All folders have valid contextType values');
        }

        // Check for missing contextId for project folders
        const missingContextId = await Folder.find({
            contextType: 'project',
            $or: [
                { contextId: null },
                { contextId: { $exists: false } }
            ]
        });
        
        if (missingContextId.length > 0) {
            console.log(`⚠️  Found ${missingContextId.length} project folders with missing contextId`);
            totalIssues += missingContextId.length;
        } else {
            console.log('✅ All project folders have valid contextId');
        }

        console.log(`\n📊 Final Status: ${totalIssues === 0 ? '✅ HEALTHY' : `⚠️  ${totalIssues} ISSUES FOUND`}`);

        return {
            totalProjects: projects.length,
            projectsWithIssues: issueReports.length,
            totalIssues,
            issueReports,
            isHealthy: totalIssues === 0
        };

    } catch (error) {
        console.error('\n❌ Validation failed:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    validateSubProjects().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

export default validateSubProjects;
