#!/usr/bin/env node

/**
 * Sub-Project Migration Script
 * 
 * This script handles projects that have more than 3 root-level sub-projects
 * by converting excess sub-projects into nested sub-projects or providing
 * options for manual handling.
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';
import readline from 'readline';

// Load environment variables
dotenv.config();

// Import models
import Project from '../models/project.js';
import Folder from '../models/folder.js';
import Video from '../models/video.js';

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

async function migrateExcessSubProjects() {
    console.log('🔄 Sub-Project Migration Tool');
    console.log('=============================');
    console.log('This tool will help you handle projects with more than 3 root-level sub-projects.');
    console.log('');

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Connected to database');

        // Find projects with excess sub-projects
        const projects = await Project.find({});
        const projectsWithIssues = [];

        for (const project of projects) {
            const rootSubProjects = await Folder.find({
                contextType: 'project',
                contextId: project._id,
                parentFolder: null
            }).sort({ createdAt: 1 });

            if (rootSubProjects.length > 3) {
                projectsWithIssues.push({
                    project,
                    rootSubProjects,
                    excessCount: rootSubProjects.length - 3,
                    excessSubProjects: rootSubProjects.slice(3)
                });
            }
        }

        if (projectsWithIssues.length === 0) {
            console.log('✅ No projects found with excess sub-projects.');
            console.log('All projects comply with the 3 sub-project limit.');
            return;
        }

        console.log(`\n📋 Found ${projectsWithIssues.length} projects with excess sub-projects:`);
        projectsWithIssues.forEach((item, index) => {
            console.log(`${index + 1}. ${item.project.title} (${item.rootSubProjects.length} sub-projects, ${item.excessCount} excess)`);
        });

        console.log('\n🔧 MIGRATION OPTIONS:');
        console.log('1. Auto-nest: Move excess sub-projects under the first sub-project');
        console.log('2. Manual review: Show details for manual handling');
        console.log('3. Convert to user folders: Move excess sub-projects to user context');
        console.log('4. Cancel: Exit without changes');

        const choice = await askQuestion('\nSelect migration option (1-4): ');

        switch (choice) {
            case '1':
                await autoNestExcessSubProjects(projectsWithIssues);
                break;
            case '2':
                await manualReviewMode(projectsWithIssues);
                break;
            case '3':
                await convertToUserFolders(projectsWithIssues);
                break;
            case '4':
                console.log('Migration cancelled.');
                return;
            default:
                console.log('Invalid choice. Migration cancelled.');
                return;
        }

    } catch (error) {
        console.error('\n❌ Migration failed:', error);
        throw error;
    } finally {
        rl.close();
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

async function autoNestExcessSubProjects(projectsWithIssues) {
    console.log('\n🔄 Auto-nesting excess sub-projects...');
    
    for (const item of projectsWithIssues) {
        console.log(`\n📁 Processing project: ${item.project.title}`);
        
        const firstSubProject = item.rootSubProjects[0];
        console.log(`   Target parent: ${firstSubProject.name}`);
        
        for (const excessSubProject of item.excessSubProjects) {
            console.log(`   Moving: ${excessSubProject.name} -> ${firstSubProject.name}`);
            
            // Update the excess sub-project to be nested under the first sub-project
            await Folder.findByIdAndUpdate(excessSubProject._id, {
                parentFolder: firstSubProject._id,
                level: firstSubProject.level + 1,
                path: `${firstSubProject.path}/${excessSubProject.name}`
            });
            
            // Update any videos that were in this sub-project
            const videosInSubProject = await Video.find({
                projectId: item.project._id,
                projectFolderId: excessSubProject._id
            });
            
            console.log(`   Updated ${videosInSubProject.length} videos`);
        }
        
        console.log(`   ✅ Migrated ${item.excessCount} excess sub-projects`);
    }
    
    console.log('\n🎉 Auto-nesting completed successfully!');
    console.log('All projects now comply with the 3 sub-project limit.');
}

async function manualReviewMode(projectsWithIssues) {
    console.log('\n📋 Manual Review Mode');
    console.log('=====================');
    
    for (const item of projectsWithIssues) {
        console.log(`\n📁 Project: ${item.project.title}`);
        console.log(`   Project ID: ${item.project._id}`);
        console.log(`   Root sub-projects: ${item.rootSubProjects.length}/3`);
        console.log(`   Excess sub-projects: ${item.excessCount}`);
        
        console.log('\n   Current sub-projects:');
        item.rootSubProjects.forEach((sp, index) => {
            const status = index < 3 ? '✅ Keep' : '⚠️  Excess';
            console.log(`   ${index + 1}. ${sp.name} (${status})`);
            console.log(`      ID: ${sp._id}`);
            console.log(`      Created: ${sp.createdAt.toISOString()}`);
            if (sp.description) {
                console.log(`      Description: ${sp.description}`);
            }
        });
        
        console.log('\n   📊 Sub-project contents:');
        for (const sp of item.excessSubProjects) {
            const videoCount = await Video.countDocuments({
                projectId: item.project._id,
                projectFolderId: sp._id
            });
            
            const nestedSubProjects = await Folder.countDocuments({
                contextType: 'project',
                contextId: item.project._id,
                parentFolder: sp._id
            });
            
            console.log(`      ${sp.name}: ${videoCount} videos, ${nestedSubProjects} nested sub-projects`);
        }
        
        console.log('\n   🔧 Recommended actions:');
        console.log('   1. Move excess sub-projects under existing ones');
        console.log('   2. Merge similar sub-projects');
        console.log('   3. Convert to user-level folders if not project-specific');
        console.log('   4. Delete if empty and unnecessary');
    }
    
    console.log('\n📝 Manual actions required:');
    console.log('Use the database or admin interface to reorganize these sub-projects.');
    console.log('Re-run validation after making changes: npm run sub-projects:validate');
}

async function convertToUserFolders(projectsWithIssues) {
    console.log('\n🔄 Converting excess sub-projects to user folders...');
    
    const confirm = await askQuestion('This will move excess sub-projects to user context. Continue? (yes/no): ');
    if (confirm.toLowerCase() !== 'yes') {
        console.log('Conversion cancelled.');
        return;
    }
    
    for (const item of projectsWithIssues) {
        console.log(`\n📁 Processing project: ${item.project.title}`);
        
        for (const excessSubProject of item.excessSubProjects) {
            console.log(`   Converting: ${excessSubProject.name} to user folder`);
            
            // Update the excess sub-project to be a user folder
            await Folder.findByIdAndUpdate(excessSubProject._id, {
                contextType: 'user',
                contextId: item.project.createdBy,
                parentFolder: null,
                level: 0,
                path: excessSubProject.name
            });
            
            // Update any videos that were in this sub-project
            await Video.updateMany(
                {
                    projectId: item.project._id,
                    projectFolderId: excessSubProject._id
                },
                {
                    $unset: { projectFolderId: "" }
                }
            );
            
            console.log(`   ✅ Converted ${excessSubProject.name} to user folder`);
        }
        
        console.log(`   ✅ Converted ${item.excessCount} excess sub-projects`);
    }
    
    console.log('\n🎉 Conversion completed successfully!');
    console.log('Excess sub-projects have been converted to user folders.');
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    migrateExcessSubProjects().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

export default migrateExcessSubProjects;
