#!/usr/bin/env node

/**
 * Sub-Project Deployment Script
 * 
 * This script runs all necessary checks and migrations when deploying
 * the sub-project functionality to production.
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';
import validateSubProjects from './validate-sub-projects.js';
import performHealthCheck from './sub-project-health-check.js';
import readline from 'readline';

// Load environment variables
dotenv.config();

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

async function deploySubProjects() {
    console.log('🚀 Sub-Project Deployment Script');
    console.log('=================================');
    console.log('This script will prepare your system for sub-project functionality.');
    console.log('');

    try {
        // Step 1: Pre-deployment validation
        console.log('📋 Step 1: Pre-deployment validation');
        console.log('====================================');
        
        const validationResult = await validateSubProjects();
        
        if (!validationResult.isHealthy) {
            console.log('\n⚠️  Issues detected during validation!');
            console.log(`Found ${validationResult.totalIssues} issues across ${validationResult.projectsWithIssues} projects.`);
            
            const proceed = await askQuestion('\nDo you want to continue with deployment? (yes/no): ');
            if (proceed.toLowerCase() !== 'yes') {
                console.log('Deployment cancelled. Please resolve issues first.');
                console.log('Run: npm run sub-projects:migrate');
                return;
            }
        } else {
            console.log('✅ Pre-deployment validation passed!');
        }

        // Step 2: Health check
        console.log('\n🏥 Step 2: System health check');
        console.log('==============================');
        
        const healthResult = await performHealthCheck();
        
        if (healthResult.summary.failedChecks > 0) {
            console.log('\n⚠️  Health check detected issues!');
            console.log(`${healthResult.summary.failedChecks} checks failed.`);
            
            const proceed = await askQuestion('\nDo you want to continue with deployment? (yes/no): ');
            if (proceed.toLowerCase() !== 'yes') {
                console.log('Deployment cancelled. Please resolve health check issues first.');
                return;
            }
        } else {
            console.log('✅ System health check passed!');
        }

        // Step 3: Deployment summary
        console.log('\n📊 Step 3: Deployment summary');
        console.log('=============================');
        
        console.log('✅ Sub-project functionality is ready for deployment!');
        console.log('');
        console.log('🎯 Features deployed:');
        console.log('• Maximum 3 sub-projects per project');
        console.log('• Sub-project terminology (instead of "project folders")');
        console.log('• Enhanced validation and error handling');
        console.log('• Nested sub-project support');
        console.log('• Comprehensive management UI');
        console.log('');
        console.log('🔧 Available maintenance commands:');
        console.log('• npm run sub-projects:validate     - Validate system compliance');
        console.log('• npm run sub-projects:health-check - Comprehensive health check');
        console.log('• npm run sub-projects:migrate      - Handle excess sub-projects');
        console.log('• npm run sub-projects:test         - Test functionality');
        console.log('');
        console.log('📝 Post-deployment recommendations:');
        console.log('1. Monitor system logs for any validation errors');
        console.log('2. Run health checks periodically: npm run sub-projects:health-check');
        console.log('3. Educate users about the 3 sub-project limit');
        console.log('4. Set up monitoring for sub-project creation failures');

        // Step 4: Final confirmation
        console.log('\n🎉 Deployment completed successfully!');
        console.log('====================================');
        console.log('Your sub-project system is now active and ready for use.');
        
        const runFinalTest = await askQuestion('\nRun final functionality test? (yes/no): ');
        if (runFinalTest.toLowerCase() === 'yes') {
            console.log('\n🧪 Running final functionality test...');
            
            // Import and run the test
            const { exec } = await import('child_process');
            const { promisify } = await import('util');
            const execAsync = promisify(exec);
            
            try {
                const { stdout, stderr } = await execAsync('node scripts/test-sub-projects.js');
                console.log(stdout);
                if (stderr) {
                    console.error('Test warnings:', stderr);
                }
                console.log('✅ Final functionality test passed!');
            } catch (error) {
                console.error('❌ Final functionality test failed:', error.message);
                console.log('Please investigate and resolve before going live.');
            }
        }

    } catch (error) {
        console.error('\n❌ Deployment failed:', error);
        throw error;
    } finally {
        rl.close();
        console.log('\n🔌 Deployment script completed');
    }
}

// Run deployment if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    deploySubProjects().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

export default deploySubProjects;
