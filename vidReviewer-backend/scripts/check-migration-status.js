#!/usr/bin/env node

/**
 * Migration Status Checker
 * 
 * This script checks the current migration status and determines what needs to be run.
 * Useful for CI/CD pipelines and deployment verification.
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';

// Load environment variables
dotenv.config();

// Import models to check schema
import Project from '../models/project.js';
import Folder from '../models/folder.js';
import Video from '../models/video.js';

const REQUIRED_MIGRATIONS = [
    '001-add-folder-support',
    '002-migrate-project-data',
    '003-optimize-folder-indexes'
];

const ROLLED_BACK_MIGRATIONS = [
    '004-add-project-folder-support',
    '005-migrate-video-organization',
    '006-add-video-duration-field',
    '007-extract-existing-video-durations',
    '008-optimize-folder-performance'
];

const SUB_PROJECT_SYSTEM_ACTIVE = true;

async function checkMigrationStatus() {
    console.log('🔍 Checking Migration Status');
    console.log('============================');

    try {
        // Connect to database
        await connectToDB();

        // Get applied migrations
        const appliedMigrations = await mongoose.connection.db
            .collection('migrations')
            .find({})
            .sort({ appliedAt: 1 })
            .toArray();

        const appliedVersions = appliedMigrations.map(m => m.version || m.name).filter(Boolean);

        console.log('\n📋 Applied Migrations:');
        if (appliedMigrations.length === 0) {
            console.log('   None');
        } else {
            appliedMigrations.forEach(migration => {
                const date = new Date(migration.appliedAt).toLocaleString();
                const name = migration.version || migration.name || 'Unknown';
                const status = migration.rolledBack ? '🔄 (Rolled Back)' : '✅';
                console.log(`   ${status} ${name} (${date})`);
            });
        }

        // Check required migrations
        console.log('\n🚨 Required Migrations:');
        const missingRequired = REQUIRED_MIGRATIONS.filter(m => !appliedVersions.includes(m));

        if (missingRequired.length === 0) {
            console.log('   ✅ All required migrations applied');
        } else {
            console.log('   ❌ Missing required migrations:');
            missingRequired.forEach(migration => {
                console.log(`      - ${migration}`);
            });
        }

        // Check rolled back migrations
        console.log('\n� Rolled Back Migrations (Sub-Project System):');
        const rolledBackInDb = appliedMigrations.filter(m => m.rolledBack).map(m => m.version || m.name);
        const expectedRolledBack = ROLLED_BACK_MIGRATIONS.filter(m => appliedVersions.includes(m));

        if (expectedRolledBack.length > 0) {
            console.log('   ✅ Successfully rolled back for sub-project system:');
            expectedRolledBack.forEach(migration => {
                const isRolledBack = rolledBackInDb.includes(migration);
                const status = isRolledBack ? '✅' : '⚠️';
                console.log(`      ${status} ${migration}`);
            });
        } else {
            console.log('   ✅ No migrations needed rollback');
        }

        // Check schema compatibility for sub-project system
        console.log('\n🔧 Sub-Project System Compatibility:');

        // Check folders collection for sub-project support
        try {
            const folderSample = await Folder.findOne({});
            if (folderSample) {
                const hasContextType = folderSample.contextType !== undefined;
                const hasContextId = folderSample.contextId !== undefined;

                if (hasContextType && hasContextId) {
                    console.log('   ✅ Folders schema: Ready for sub-projects');

                    // Check for sub-projects
                    const subProjectCount = await Folder.countDocuments({ contextType: 'project' });
                    console.log(`   📊 Current sub-projects: ${subProjectCount}`);
                } else {
                    console.log('   ❌ Folders schema: Missing sub-project support fields');
                    console.log('      Run: npm run migrate');
                }
            } else {
                console.log('   ℹ️  Folders schema: No folders found (new installation)');
            }
        } catch (error) {
            console.log('   ❌ Folders schema: Collection not found or error');
        }

        // Check videos collection for sub-project compatibility
        try {
            const videoSample = await Video.findOne({});
            if (videoSample) {
                // After rollback, videos should NOT have duration or projectFolderId
                const hasProjectFolderId = videoSample.projectFolderId !== undefined;
                const hasDuration = videoSample.duration !== undefined;

                if (!hasProjectFolderId && !hasDuration) {
                    console.log('   ✅ Videos schema: Clean (post-rollback state)');
                } else {
                    console.log('   ⚠️  Videos schema: Contains rolled-back fields');
                    if (hasProjectFolderId) {
                        console.log('      - projectFolderId field present (should be removed)');
                    }
                    if (hasDuration) {
                        console.log('      - duration field present (should be removed)');
                    }
                    console.log('      Note: These fields were removed during migration rollback');
                }

                const videoCount = await Video.countDocuments({});
                console.log(`   📊 Total videos: ${videoCount}`);
            } else {
                console.log('   ℹ️  Videos schema: No videos found (new installation)');
            }
        } catch (error) {
            console.log('   ❌ Videos schema: Collection not found or error');
        }

        // Check projects collection
        try {
            const projectSample = await Project.findOne({});
            if (projectSample) {
                const hasFolderId = projectSample.folderId !== undefined;

                if (hasFolderId) {
                    console.log('   ✅ Projects schema: Ready for sub-projects');

                    const projectCount = await Project.countDocuments({});
                    console.log(`   📊 Total projects: ${projectCount}`);
                } else {
                    console.log('   ❌ Projects schema: Missing folderId field');
                    console.log('      Run: npm run migrate');
                }
            } else {
                console.log('   ℹ️  Projects schema: No projects found (new installation)');
            }
        } catch (error) {
            console.log('   ❌ Projects schema: Collection not found or error');
        }

        // Check indexes
        console.log('\n📊 Index Status:');
        
        try {
            const folderIndexes = await Folder.collection.getIndexes();
            const hasContextIndex = Object.keys(folderIndexes).some(name => 
                name.includes('context') || folderIndexes[name].key?.contextType
            );
            
            if (hasContextIndex) {
                console.log('   ✅ Folder indexes: Project folder indexes present');
            } else {
                console.log('   ⚠️  Folder indexes: Project folder indexes missing');
                console.log('      Run: npm run migrate:project-folders');
            }
        } catch (error) {
            console.log('   ❌ Folder indexes: Error checking indexes');
        }

        try {
            const videoIndexes = await Video.collection.getIndexes();
            const indexNames = Object.keys(videoIndexes);

            const hasProjectFolderIndex = indexNames.some(name =>
                name.includes('projectId_1_projectFolderId_1')
            );
            const hasDurationIndex = indexNames.some(name =>
                name.includes('duration_1')
            );
            const hasTextSearchIndex = indexNames.some(name =>
                name.includes('text')
            );

            if (hasProjectFolderIndex && hasDurationIndex && hasTextSearchIndex) {
                console.log('   ✅ Video indexes: All enhanced indexes present');
            } else {
                const missing = [];
                if (!hasProjectFolderIndex) missing.push('project folder');
                if (!hasDurationIndex) missing.push('duration');
                if (!hasTextSearchIndex) missing.push('text search');

                console.log(`   ⚠️  Video indexes: Missing ${missing.join(', ')} indexes`);
                if (!hasProjectFolderIndex) console.log('      Run: npm run migrate:project-folders');
                if (!hasDurationIndex || !hasTextSearchIndex) console.log('      Run: npm run migrate:video-duration');
            }
        } catch (error) {
            console.log('   ❌ Video indexes: Error checking indexes');
        }

        // Summary and recommendations for sub-project system
        console.log('\n📝 Sub-Project System Status:');

        if (missingRequired.length > 0) {
            console.log('   🚨 ACTION REQUIRED: Missing required migrations');
            console.log('   Run: npm run migrate');
            process.exit(1);
        } else {
            console.log('   ✅ Sub-project system ready!');
            console.log('   ✅ Required migrations: Complete');
            console.log('   ✅ Schema: Compatible');
            console.log('   ✅ Rollbacks: Applied');
            console.log('');
            console.log('🎯 Available sub-project commands:');
            console.log('   npm run sub-projects:validate     - Validate system');
            console.log('   npm run sub-projects:health-check - Health check');
            console.log('   npm run sub-projects:test         - Test functionality');
            console.log('   npm run sub-projects:deploy       - Deploy system');
            process.exit(0);
        }

    } catch (error) {
        console.error('\n❌ Error checking migration status:', error);
        console.error('\n🔧 Troubleshooting:');
        console.error('   1. Check database connection');
        console.error('   2. Verify environment variables');
        console.error('   3. Ensure database is running');
        process.exit(1);
    } finally {
        await mongoose.connection.close();
    }
}

// Show help
function showHelp() {
    console.log('Migration Status Checker');
    console.log('========================');
    console.log('');
    console.log('Usage:');
    console.log('  node scripts/check-migration-status.js');
    console.log('  npm run migration:status');
    console.log('');
    console.log('This script checks:');
    console.log('  - Which migrations have been applied');
    console.log('  - Which migrations are missing');
    console.log('  - Schema compatibility');
    console.log('  - Index status');
    console.log('');
    console.log('Exit codes:');
    console.log('  0 - All good or optional migrations available');
    console.log('  1 - Required migrations missing or error');
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
}

// Run check if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    checkMigrationStatus();
}
