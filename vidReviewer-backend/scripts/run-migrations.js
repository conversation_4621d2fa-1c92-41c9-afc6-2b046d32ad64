#!/usr/bin/env node

/**
 * Migration Runner Script
 * 
 * This script runs all folder-related migrations in the correct order:
 * 1. Database schema migration
 * 2. Data migration
 * 3. Index optimization
 * 
 * Usage:
 *   node scripts/run-migrations.js
 *   node scripts/run-migrations.js --dry-run
 *   node scripts/run-migrations.js --skip-data-migration
 */

import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';
import mongoose from 'mongoose';

// Load environment variables
dotenv.config();

// Import migration scripts
import schemaMigration from './001-add-folder-support.js';
import dataMigration from './002-migrate-project-data.js';
import indexOptimization from './003-optimize-folder-indexes.js';
import projectFolderSupport from './004-add-project-folder-support.js';
import videoOrganization from './005-migrate-video-organization.js';
import { runMigration as videoDurationMigration } from './006-add-video-duration-field.js';
import { runDurationExtraction } from './007-extract-existing-video-durations.js';
import { runOptimization } from './008-optimize-folder-performance.js';

const migrations = [
    {
        name: '001-add-folder-support',
        description: 'Add folder support to database schema',
        run: schemaMigration,
        required: true
    },
    {
        name: '002-migrate-project-data',
        description: 'Migrate existing project data for folder support',
        run: dataMigration,
        required: false
    },
    {
        name: '003-optimize-folder-indexes',
        description: 'Optimize database indexes for folder queries',
        run: indexOptimization,
        required: false
    },
    {
        name: '004-add-project-folder-support',
        description: 'Add project folder support to folders and videos',
        run: projectFolderSupport,
        required: true
    },
    {
        name: '005-migrate-video-organization',
        description: 'Organize existing videos into project folders',
        run: videoOrganization,
        required: false
    },
    {
        name: '006-add-video-duration-field',
        description: 'Add duration field to video model and create indexes',
        run: videoDurationMigration,
        required: true
    },
    {
        name: '007-extract-existing-video-durations',
        description: 'Extract durations for existing videos using ffmpeg',
        run: runDurationExtraction,
        required: false
    },
    {
        name: '008-optimize-folder-performance',
        description: 'Optimize database indexes and validate folder structure',
        run: runOptimization,
        required: false
    }
];

async function checkMigrationStatus(migrationName) {
    try {
        const migrationRecord = await mongoose.connection.db
            .collection('migrations')
            .findOne({ version: migrationName });
        
        return {
            applied: !!migrationRecord,
            appliedAt: migrationRecord?.appliedAt,
            rolledBack: migrationRecord?.rolledBack || false
        };
    } catch (error) {
        return { applied: false, appliedAt: null, rolledBack: false };
    }
}

async function runMigrations() {
    const args = process.argv.slice(2);
    const isDryRun = args.includes('--dry-run');
    const skipDataMigration = args.includes('--skip-data-migration');

    console.log('🚀 VidLead Folder Migration Runner');
    console.log('==================================');
    
    if (isDryRun) {
        console.log('🔍 DRY RUN MODE - No changes will be made');
    }
    
    if (skipDataMigration) {
        console.log('⏭️  SKIPPING DATA MIGRATION');
    }
    
    console.log('');

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Connected to database');

        // Check current migration status
        console.log('\n📊 Checking migration status...');
        
        const migrationStatuses = [];
        for (const migration of migrations) {
            const status = await checkMigrationStatus(migration.name);
            migrationStatuses.push({ ...migration, ...status });
            
            const statusIcon = status.applied ? '✅' : '⏳';
            const rolledBackText = status.rolledBack ? ' (ROLLED BACK)' : '';
            console.log(`${statusIcon} ${migration.name}: ${status.applied ? 'Applied' : 'Pending'}${rolledBackText}`);
            if (status.appliedAt) {
                console.log(`   Applied at: ${status.appliedAt.toISOString()}`);
            }
        }

        // Determine which migrations need to run
        const migrationsToRun = migrationStatuses.filter(m => {
            if (m.applied && !m.rolledBack) return false;
            if (m.name === '002-migrate-project-data' && skipDataMigration) return false;
            return true;
        });

        if (migrationsToRun.length === 0) {
            console.log('\n✅ All migrations are up to date!');
            return;
        }

        console.log(`\n🔄 ${migrationsToRun.length} migration(s) to run:`);
        migrationsToRun.forEach(m => {
            console.log(`   - ${m.name}: ${m.description}`);
        });

        if (isDryRun) {
            console.log('\n🔍 DRY RUN: Would run the above migrations');
            return;
        }

        // Run migrations in order
        console.log('\n🚀 Running migrations...');
        
        for (const migration of migrationsToRun) {
            console.log(`\n▶️  Running: ${migration.name}`);
            console.log(`   Description: ${migration.description}`);
            
            try {
                await migration.run();
                console.log(`✅ Completed: ${migration.name}`);
            } catch (error) {
                console.error(`❌ Failed: ${migration.name}`);
                console.error(`   Error: ${error.message}`);
                
                if (migration.required) {
                    console.error('\n💥 Required migration failed. Stopping execution.');
                    throw error;
                } else {
                    console.log('\n⚠️  Optional migration failed. Continuing...');
                }
            }
        }

        // Final status check
        console.log('\n📊 Final migration status:');
        for (const migration of migrations) {
            const status = await checkMigrationStatus(migration.name);
            const statusIcon = status.applied ? '✅' : '❌';
            console.log(`${statusIcon} ${migration.name}: ${status.applied ? 'Applied' : 'Failed/Pending'}`);
        }

        console.log('\n🎉 Migration process completed!');
        console.log('==================================');

        // Post-migration recommendations
        console.log('\n💡 Post-migration recommendations:');
        console.log('   1. Test folder functionality in the frontend');
        console.log('   2. Monitor database performance with new indexes');
        console.log('   3. Update API documentation if needed');
        console.log('   4. Consider running backup before production deployment');

    } catch (error) {
        console.error('\n❌ Migration process failed:', error);
        console.error('\n🔧 Troubleshooting:');
        console.error('   1. Check database connection');
        console.error('   2. Verify environment variables');
        console.error('   3. Check database permissions');
        console.error('   4. Review error logs above');
        process.exit(1);
    } finally {
        await mongoose.connection.close();
        console.log('\n📡 Database connection closed');
    }
}

// Show help
function showHelp() {
    console.log('VidLead Folder Migration Runner');
    console.log('===============================');
    console.log('');
    console.log('Usage:');
    console.log('  node scripts/run-migrations.js                 Run all migrations');
    console.log('  node scripts/run-migrations.js --dry-run       Show what would be done');
    console.log('  node scripts/run-migrations.js --skip-data-migration  Skip data migration');
    console.log('');
    console.log('Environment Variables:');
    console.log('  CREATE_YEAR_FOLDERS=true    Create folders by year during data migration');
    console.log('');
    console.log('Examples:');
    console.log('  # Run all migrations');
    console.log('  node scripts/run-migrations.js');
    console.log('');
    console.log('  # Check what would be done without making changes');
    console.log('  node scripts/run-migrations.js --dry-run');
    console.log('');
    console.log('  # Run only schema and index migrations, skip data migration');
    console.log('  node scripts/run-migrations.js --skip-data-migration');
    console.log('');
    console.log('  # Run with year-based folder organization');
    console.log('  CREATE_YEAR_FOLDERS=true node scripts/run-migrations.js');
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
}

// Run migrations if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runMigrations();
}
