#!/usr/bin/env node

/**
 * Sub-Project Health Check Script
 * 
 * This script performs comprehensive health checks on the sub-project system
 * to ensure everything is working correctly after deployment.
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';

// Load environment variables
dotenv.config();

// Import models
import Project from '../models/project.js';
import Folder from '../models/folder.js';
import Video from '../models/video.js';

async function performHealthCheck() {
    console.log('🏥 Sub-Project System Health Check');
    console.log('==================================');

    const healthReport = {
        timestamp: new Date().toISOString(),
        checks: [],
        issues: [],
        summary: {
            totalChecks: 0,
            passedChecks: 0,
            failedChecks: 0,
            warnings: 0
        }
    };

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Database connection successful');

        // Check 1: Validate 3 sub-project limit enforcement
        console.log('\n🔍 Check 1: Sub-project limit enforcement');
        const projectsExceedingLimit = await Project.aggregate([
            {
                $lookup: {
                    from: 'folders',
                    let: { projectId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$contextType', 'project'] },
                                        { $eq: ['$contextId', '$$projectId'] },
                                        { $eq: ['$parentFolder', null] }
                                    ]
                                }
                            }
                        }
                    ],
                    as: 'rootSubProjects'
                }
            },
            {
                $match: {
                    $expr: { $gt: [{ $size: '$rootSubProjects' }, 3] }
                }
            }
        ]);

        healthReport.checks.push({
            name: 'Sub-project limit enforcement',
            status: projectsExceedingLimit.length === 0 ? 'PASS' : 'FAIL',
            details: `${projectsExceedingLimit.length} projects exceed 3 sub-project limit`
        });

        if (projectsExceedingLimit.length === 0) {
            console.log('   ✅ All projects comply with 3 sub-project limit');
            healthReport.summary.passedChecks++;
        } else {
            console.log(`   ❌ ${projectsExceedingLimit.length} projects exceed limit`);
            healthReport.summary.failedChecks++;
            healthReport.issues.push({
                type: 'LIMIT_VIOLATION',
                count: projectsExceedingLimit.length,
                projects: projectsExceedingLimit.map(p => ({ id: p._id, title: p.title }))
            });
        }

        // Check 2: Validate folder hierarchy integrity
        console.log('\n🔍 Check 2: Folder hierarchy integrity');
        const orphanedFolders = await Folder.find({
            contextType: 'project',
            parentFolder: { $ne: null }
        });

        let orphanCount = 0;
        for (const folder of orphanedFolders) {
            const parentExists = await Folder.findById(folder.parentFolder);
            if (!parentExists) {
                orphanCount++;
            }
        }

        healthReport.checks.push({
            name: 'Folder hierarchy integrity',
            status: orphanCount === 0 ? 'PASS' : 'FAIL',
            details: `${orphanCount} orphaned folders found`
        });

        if (orphanCount === 0) {
            console.log('   ✅ All folder hierarchies are intact');
            healthReport.summary.passedChecks++;
        } else {
            console.log(`   ❌ ${orphanCount} orphaned folders found`);
            healthReport.summary.failedChecks++;
            healthReport.issues.push({
                type: 'ORPHANED_FOLDERS',
                count: orphanCount
            });
        }

        // Check 3: Validate contextType values
        console.log('\n🔍 Check 3: Context type validation');
        const invalidContextTypes = await Folder.find({
            contextType: { $nin: ['user', 'project'] }
        });

        healthReport.checks.push({
            name: 'Context type validation',
            status: invalidContextTypes.length === 0 ? 'PASS' : 'FAIL',
            details: `${invalidContextTypes.length} folders with invalid contextType`
        });

        if (invalidContextTypes.length === 0) {
            console.log('   ✅ All folders have valid contextType');
            healthReport.summary.passedChecks++;
        } else {
            console.log(`   ❌ ${invalidContextTypes.length} folders with invalid contextType`);
            healthReport.summary.failedChecks++;
            healthReport.issues.push({
                type: 'INVALID_CONTEXT_TYPE',
                count: invalidContextTypes.length
            });
        }

        // Check 4: Validate project folder contextId
        console.log('\n🔍 Check 4: Project folder contextId validation');
        const missingContextId = await Folder.find({
            contextType: 'project',
            $or: [
                { contextId: null },
                { contextId: { $exists: false } }
            ]
        });

        healthReport.checks.push({
            name: 'Project folder contextId validation',
            status: missingContextId.length === 0 ? 'PASS' : 'FAIL',
            details: `${missingContextId.length} project folders missing contextId`
        });

        if (missingContextId.length === 0) {
            console.log('   ✅ All project folders have valid contextId');
            healthReport.summary.passedChecks++;
        } else {
            console.log(`   ❌ ${missingContextId.length} project folders missing contextId`);
            healthReport.summary.failedChecks++;
            healthReport.issues.push({
                type: 'MISSING_CONTEXT_ID',
                count: missingContextId.length
            });
        }

        // Check 5: Validate video-folder relationships
        console.log('\n🔍 Check 5: Video-folder relationship validation');
        const videosWithInvalidFolders = await Video.find({
            projectFolderId: { $ne: null }
        });

        let invalidVideoFolderCount = 0;
        for (const video of videosWithInvalidFolders) {
            const folderExists = await Folder.findById(video.projectFolderId);
            if (!folderExists) {
                invalidVideoFolderCount++;
            }
        }

        healthReport.checks.push({
            name: 'Video-folder relationship validation',
            status: invalidVideoFolderCount === 0 ? 'PASS' : 'FAIL',
            details: `${invalidVideoFolderCount} videos reference non-existent folders`
        });

        if (invalidVideoFolderCount === 0) {
            console.log('   ✅ All video-folder relationships are valid');
            healthReport.summary.passedChecks++;
        } else {
            console.log(`   ❌ ${invalidVideoFolderCount} videos reference non-existent folders`);
            healthReport.summary.failedChecks++;
            healthReport.issues.push({
                type: 'INVALID_VIDEO_FOLDER_REF',
                count: invalidVideoFolderCount
            });
        }

        // Check 6: Test sub-project creation validation
        console.log('\n🔍 Check 6: Sub-project creation validation test');
        try {
            // Find a project with less than 3 sub-projects for testing
            const testProject = await Project.findOne({});
            if (testProject) {
                const rootSubProjectCount = await Folder.countDocuments({
                    contextType: 'project',
                    contextId: testProject._id,
                    parentFolder: null
                });

                if (rootSubProjectCount < 3) {
                    // Try to create a test sub-project
                    const testSubProject = new Folder({
                        name: 'Health Check Test Sub-Project',
                        description: 'Temporary test sub-project for health check',
                        createdBy: testProject.createdBy,
                        contextType: 'project',
                        contextId: testProject._id
                    });

                    await testSubProject.save();
                    console.log('   ✅ Sub-project creation validation working');
                    
                    // Clean up test sub-project
                    await Folder.findByIdAndDelete(testSubProject._id);
                    console.log('   ✅ Test sub-project cleaned up');
                    
                    healthReport.checks.push({
                        name: 'Sub-project creation validation test',
                        status: 'PASS',
                        details: 'Successfully created and deleted test sub-project'
                    });
                    healthReport.summary.passedChecks++;
                } else {
                    console.log('   ⚠️  Skipped (test project has 3 sub-projects)');
                    healthReport.checks.push({
                        name: 'Sub-project creation validation test',
                        status: 'SKIP',
                        details: 'Test project already has maximum sub-projects'
                    });
                    healthReport.summary.warnings++;
                }
            } else {
                console.log('   ⚠️  Skipped (no projects found for testing)');
                healthReport.checks.push({
                    name: 'Sub-project creation validation test',
                    status: 'SKIP',
                    details: 'No projects available for testing'
                });
                healthReport.summary.warnings++;
            }
        } catch (error) {
            console.log(`   ❌ Sub-project creation validation failed: ${error.message}`);
            healthReport.checks.push({
                name: 'Sub-project creation validation test',
                status: 'FAIL',
                details: error.message
            });
            healthReport.summary.failedChecks++;
            healthReport.issues.push({
                type: 'VALIDATION_TEST_FAILED',
                error: error.message
            });
        }

        // Calculate summary
        healthReport.summary.totalChecks = healthReport.checks.length;

        // Generate final report
        console.log('\n📊 HEALTH CHECK SUMMARY');
        console.log('=======================');
        console.log(`Total checks: ${healthReport.summary.totalChecks}`);
        console.log(`Passed: ${healthReport.summary.passedChecks}`);
        console.log(`Failed: ${healthReport.summary.failedChecks}`);
        console.log(`Warnings: ${healthReport.summary.warnings}`);

        const overallStatus = healthReport.summary.failedChecks === 0 ? 'HEALTHY' : 'ISSUES DETECTED';
        console.log(`\nOverall Status: ${overallStatus === 'HEALTHY' ? '✅' : '⚠️'} ${overallStatus}`);

        if (healthReport.issues.length > 0) {
            console.log('\n🔧 RECOMMENDED ACTIONS:');
            console.log('======================');
            
            healthReport.issues.forEach(issue => {
                switch (issue.type) {
                    case 'LIMIT_VIOLATION':
                        console.log('• Run migration script: npm run sub-projects:migrate');
                        break;
                    case 'ORPHANED_FOLDERS':
                        console.log('• Clean up orphaned folders or restore missing parents');
                        break;
                    case 'INVALID_CONTEXT_TYPE':
                        console.log('• Fix invalid contextType values in folders');
                        break;
                    case 'MISSING_CONTEXT_ID':
                        console.log('• Add missing contextId values to project folders');
                        break;
                    case 'INVALID_VIDEO_FOLDER_REF':
                        console.log('• Clean up video references to non-existent folders');
                        break;
                }
            });
        }

        return healthReport;

    } catch (error) {
        console.error('\n❌ Health check failed:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from database');
    }
}

// Run health check if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    performHealthCheck().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

export default performHealthCheck;
