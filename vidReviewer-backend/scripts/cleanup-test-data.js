#!/usr/bin/env node

/**
 * Test Data Cleanup Script
 * 
 * This script removes test projects and sub-projects created during testing
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';

// Load environment variables
dotenv.config();

// Import models
import Project from '../models/project.js';
import Folder from '../models/folder.js';

async function cleanupTestData() {
    console.log('🧹 Cleaning up test data...');

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Connected to database');

        // Find and clean up test project
        const testProject = await Project.findOne({ title: 'Sub-Project Test Project' });
        if (testProject) {
            const deleted = await Folder.deleteMany({
                contextType: 'project',
                contextId: testProject._id
            });
            console.log(`   ✅ Cleaned up ${deleted.deletedCount} test sub-projects`);
            
            await Project.findByIdAndDelete(testProject._id);
            console.log('   ✅ Deleted test project');
        } else {
            console.log('   ℹ️  No test project found');
        }

        console.log('🎉 Test data cleanup completed!');

    } catch (error) {
        console.error('❌ Cleanup failed:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run cleanup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    cleanupTestData().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

export default cleanupTestData;
