#!/usr/bin/env node

/**
 * Data Migration Script: Migrate Project Data for Folder Support
 * 
 * This script handles data migration for existing projects:
 * 1. Validates existing project data
 * 2. Creates default folder structure if needed
 * 3. Optionally organizes projects by creation date or user preference
 * 4. Updates project references and relationships
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';

// Load environment variables
dotenv.config();

// Import models
import Project from '../models/project.js';
import Folder from '../models/folder.js';
import User from '../models/user.js';

const MIGRATION_VERSION = '002-migrate-project-data';

async function runDataMigration() {
    console.log(`🚀 Starting data migration: ${MIGRATION_VERSION}`);
    console.log('==========================================');

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Connected to database');

        // Check if migration has already been run
        const migrationRecord = await mongoose.connection.db
            .collection('migrations')
            .findOne({ version: MIGRATION_VERSION });

        if (migrationRecord) {
            console.log('⚠️  Data migration already applied. Skipping...');
            return;
        }

        // Step 1: Validate existing project data
        console.log('\n📊 Step 1: Validating existing project data...');
        
        const totalProjects = await Project.countDocuments();
        const projectsInRoot = await Project.countDocuments({ folderId: null });
        const projectsInFolders = await Project.countDocuments({ folderId: { $ne: null } });
        
        console.log(`Total projects: ${totalProjects}`);
        console.log(`Projects in root: ${projectsInRoot}`);
        console.log(`Projects in folders: ${projectsInFolders}`);

        // Step 2: Get all users to process their projects
        console.log('\n👥 Step 2: Processing projects by user...');
        
        const users = await User.find({}, { _id: 1, locationId: 1 }).lean();
        console.log(`Found ${users.length} users to process`);

        let processedUsers = 0;
        let totalFoldersCreated = 0;
        let totalProjectsMoved = 0;

        for (const user of users) {
            console.log(`\n📁 Processing user: ${user._id}`);
            
            // Get user's projects that are in root (folderId: null)
            const userRootProjects = await Project.find({
                createdBy: user._id,
                folderId: null
            }).lean();

            if (userRootProjects.length === 0) {
                console.log(`  ✅ User has no projects in root folder`);
                continue;
            }

            console.log(`  📋 Found ${userRootProjects.length} projects in root folder`);

            // Option 1: Create folders by year (if you want to organize by creation date)
            const CREATE_YEAR_FOLDERS = process.env.CREATE_YEAR_FOLDERS === 'true';
            
            if (CREATE_YEAR_FOLDERS) {
                // Group projects by year
                const projectsByYear = {};
                
                userRootProjects.forEach(project => {
                    const year = new Date(project.createdAt).getFullYear();
                    if (!projectsByYear[year]) {
                        projectsByYear[year] = [];
                    }
                    projectsByYear[year].push(project);
                });

                // Create year folders and move projects
                for (const [year, projects] of Object.entries(projectsByYear)) {
                    // Check if year folder already exists
                    let yearFolder = await Folder.findOne({
                        name: year.toString(),
                        createdBy: user._id,
                        parentFolder: null
                    });

                    if (!yearFolder) {
                        // Create year folder
                        yearFolder = new Folder({
                            name: year.toString(),
                            description: `Projects from ${year}`,
                            createdBy: user._id,
                            parentFolder: null
                        });
                        await yearFolder.save();
                        totalFoldersCreated++;
                        console.log(`  📁 Created folder: ${year}`);
                    }

                    // Move projects to year folder
                    const projectIds = projects.map(p => p._id);
                    const updateResult = await Project.updateMany(
                        { _id: { $in: projectIds } },
                        { $set: { folderId: yearFolder._id } }
                    );

                    totalProjectsMoved += updateResult.modifiedCount;
                    console.log(`  📋 Moved ${updateResult.modifiedCount} projects to ${year} folder`);
                }
            } else {
                // Option 2: Leave projects in root (default behavior)
                console.log(`  ✅ Keeping ${userRootProjects.length} projects in root folder`);
            }

            processedUsers++;
        }

        // Step 3: Validate folder structure
        console.log('\n🔍 Step 3: Validating folder structure...');
        
        const totalFolders = await Folder.countDocuments();
        console.log(`Total folders created: ${totalFolders}`);

        // Check for any orphaned folders (folders with invalid parent references)
        const orphanedFolders = await Folder.aggregate([
            {
                $lookup: {
                    from: 'folders',
                    localField: 'parentFolder',
                    foreignField: '_id',
                    as: 'parent'
                }
            },
            {
                $match: {
                    parentFolder: { $ne: null },
                    parent: { $size: 0 }
                }
            }
        ]);

        if (orphanedFolders.length > 0) {
            console.log(`⚠️  Found ${orphanedFolders.length} orphaned folders`);
            // Fix orphaned folders by setting their parent to null
            const orphanedIds = orphanedFolders.map(f => f._id);
            await Folder.updateMany(
                { _id: { $in: orphanedIds } },
                { $set: { parentFolder: null } }
            );
            console.log(`✅ Fixed ${orphanedFolders.length} orphaned folders`);
        }

        // Step 4: Final validation
        console.log('\n✅ Step 4: Final validation...');
        
        const finalProjectCount = await Project.countDocuments();
        const finalProjectsInRoot = await Project.countDocuments({ folderId: null });
        const finalProjectsInFolders = await Project.countDocuments({ folderId: { $ne: null } });
        
        console.log(`Final project count: ${finalProjectCount}`);
        console.log(`Final projects in root: ${finalProjectsInRoot}`);
        console.log(`Final projects in folders: ${finalProjectsInFolders}`);

        // Verify all projects have valid folder references
        const invalidFolderRefs = await Project.aggregate([
            {
                $match: { folderId: { $ne: null } }
            },
            {
                $lookup: {
                    from: 'folders',
                    localField: 'folderId',
                    foreignField: '_id',
                    as: 'folder'
                }
            },
            {
                $match: { folder: { $size: 0 } }
            }
        ]);

        if (invalidFolderRefs.length > 0) {
            console.log(`⚠️  Found ${invalidFolderRefs.length} projects with invalid folder references`);
            // Fix by setting folderId to null
            const invalidIds = invalidFolderRefs.map(p => p._id);
            await Project.updateMany(
                { _id: { $in: invalidIds } },
                { $set: { folderId: null } }
            );
            console.log(`✅ Fixed ${invalidFolderRefs.length} invalid folder references`);
        }

        // Record successful migration
        await mongoose.connection.db.collection('migrations').insertOne({
            version: MIGRATION_VERSION,
            appliedAt: new Date(),
            description: 'Migrate project data for folder support',
            stats: {
                processedUsers,
                totalFoldersCreated,
                totalProjectsMoved,
                finalProjectCount,
                finalProjectsInRoot,
                finalProjectsInFolders
            }
        });

        console.log('\n🎉 Data migration completed successfully!');
        console.log(`📊 Summary:`);
        console.log(`   - Processed users: ${processedUsers}`);
        console.log(`   - Folders created: ${totalFoldersCreated}`);
        console.log(`   - Projects moved: ${totalProjectsMoved}`);
        console.log('==========================================');

    } catch (error) {
        console.error('\n❌ Data migration failed:', error);
        process.exit(1);
    } finally {
        await mongoose.connection.close();
        console.log('📡 Database connection closed');
    }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runDataMigration();
}

export default runDataMigration;
