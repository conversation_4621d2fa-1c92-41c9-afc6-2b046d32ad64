# VidLead Folder Migration Scripts

This directory contains migration scripts to add folder functionality to the VidLead application.

## Overview

The folder functionality allows users to organize their video projects in a hierarchical folder structure. These migration scripts handle the database changes needed to support this feature.

## Migration Scripts

### 1. `001-add-folder-support.js`
**Purpose**: Database schema migration
- Creates the folders collection if it doesn't exist
- Adds `folderId` field to existing projects (set to `null` for root folder)
- Creates necessary database indexes for performance
- Validates the migration

**Status**: Required migration

### 2. `002-migrate-project-data.js`
**Purpose**: Data migration for existing projects
- Validates existing project data
- Optionally organizes projects into year-based folders
- Fixes any orphaned folder references
- Updates project-folder relationships

**Status**: Optional migration
**Environment Variable**: Set `CREATE_YEAR_FOLDERS=true` to organize projects by creation year

### 3. `003-optimize-folder-indexes.js`
**Purpose**: Database performance optimization
- Creates compound indexes for efficient folder queries
- Analyzes query performance
- Creates sparse indexes for optional fields
- Provides performance recommendations

**Status**: Optional but recommended

### 4. `004-add-project-folder-support.js`
**Purpose**: Adds project-specific folder functionality
- Enables folders within projects
- Sets up project-folder relationships
- Adds project folder permissions

**Status**: Required migration

### 5. `005-migrate-video-organization.js`
**Purpose**: Organizes existing videos into folder structures
- Optionally creates year-based folders
- Moves videos into appropriate folders
- Maintains video metadata and relationships

**Status**: Optional migration

### 6. `006-add-video-duration-field.js` ⭐ **NEW**
**Purpose**: Adds duration field to video model for enhanced UI features
- Adds `duration` field to all existing videos (default: 0)
- Ensures `projectFolderId` field exists
- Creates indexes for duration-based queries
- Sets up text search indexes

**Status**: Required migration for new UI features

### 7. `007-extract-existing-video-durations.js` ⭐ **NEW**
**Purpose**: Extracts video durations for existing videos using ffmpeg
- Processes videos in batches to avoid system overload
- Extracts duration from GCS video files
- Updates database with extracted durations
- Provides detailed progress reporting
- **Note**: This is a background process that can take time for large video collections

**Status**: Optional but recommended for complete UI functionality

### 8. `008-optimize-folder-performance.js` ⭐ **NEW**
**Purpose**: Optimizes database for enhanced folder features and bulk operations
- Creates optimized indexes for folder statistics
- Sets up indexes for advanced search functionality
- Validates folder structure integrity
- Fixes orphaned videos and circular references
- Generates performance reports

**Status**: Optional but recommended for optimal performance

### 4. `run-migrations.js`
**Purpose**: Master migration runner
- Runs all migrations in the correct order
- Checks migration status
- Supports dry-run mode
- Handles errors gracefully

### 5. `rollback-folder-support.js`
**Purpose**: Rollback script (use with caution)
- Moves all projects back to root folder
- Removes folder-related indexes
- Optionally removes the folders collection
- Records rollback in migration history

**⚠️ WARNING**: This permanently deletes all folder data!

## Usage

### Quick Start
```bash
# Run all migrations
node scripts/run-migrations.js

# Check what would be done (dry run)
node scripts/run-migrations.js --dry-run

# Skip data migration (only schema and indexes)
node scripts/run-migrations.js --skip-data-migration
```

### Individual Scripts
```bash
# Run schema migration only
node scripts/001-add-folder-support.js

# Run data migration with year folders
CREATE_YEAR_FOLDERS=true node scripts/002-migrate-project-data.js

# Run index optimization
node scripts/003-optimize-folder-indexes.js

# Rollback (DESTRUCTIVE - use with caution)
node scripts/rollback-folder-support.js
```

### Environment Variables

- `CREATE_YEAR_FOLDERS=true`: During data migration, create folders organized by year
- `MONGODB_URI`: Database connection string (required)

## Migration Process

1. **Backup your database** before running migrations in production
2. Run migrations in a staging environment first
3. Test the folder functionality thoroughly
4. Monitor database performance after migration

### Recommended Order

1. **Development/Testing**:
   ```bash
   node scripts/run-migrations.js --dry-run
   node scripts/run-migrations.js
   ```

2. **Production**:
   ```bash
   # Backup database first!
   mongodump --uri="your-mongodb-uri" --out=backup-before-folder-migration
   
   # Run migrations
   node scripts/run-migrations.js --dry-run  # Verify first
   node scripts/run-migrations.js
   ```

## Migration Status

The scripts track migration status in a `migrations` collection:

```javascript
{
  version: "001-add-folder-support",
  appliedAt: ISODate("2024-01-15T10:30:00Z"),
  description: "Add folder support to projects",
  stats: { /* migration statistics */ }
}
```

## Rollback Process

If you need to rollback the folder functionality:

```bash
# This will permanently delete all folder data!
node scripts/rollback-folder-support.js
```

The rollback script will:
1. Move all projects back to root folder
2. Remove folder-related indexes
3. Optionally remove the folders collection
4. Record the rollback in migration history

## Troubleshooting

### Common Issues

1. **Connection Error**:
   - Check `MONGODB_URI` environment variable
   - Verify database is running and accessible

2. **Permission Error**:
   - Ensure database user has read/write permissions
   - Check if user can create indexes

3. **Migration Already Applied**:
   - Migrations are idempotent and safe to re-run
   - Check migration status in the `migrations` collection

4. **Index Creation Failed**:
   - May indicate existing data conflicts
   - Check database logs for specific errors

### Performance Monitoring

After migration, monitor these queries:
- User's root projects: `{ createdBy: userId, folderId: null }`
- Folder contents: `{ createdBy: userId, folderId: folderId }`
- Folder tree: `{ createdBy: userId, parentFolder: parentId }`

### Validation Queries

```javascript
// Check all projects have folderId field
db.projects.countDocuments({ folderId: { $exists: false } })

// Check for orphaned folder references
db.projects.aggregate([
  { $match: { folderId: { $ne: null } } },
  { $lookup: { from: 'folders', localField: 'folderId', foreignField: '_id', as: 'folder' } },
  { $match: { folder: { $size: 0 } } }
])

// Check folder tree integrity
db.folders.aggregate([
  { $match: { parentFolder: { $ne: null } } },
  { $lookup: { from: 'folders', localField: 'parentFolder', foreignField: '_id', as: 'parent' } },
  { $match: { parent: { $size: 0 } } }
])
```

## Support

If you encounter issues:
1. Check the migration logs for specific error messages
2. Verify your database connection and permissions
3. Ensure you have a recent database backup
4. Test in a development environment first

## Files Created/Modified

### Database Collections
- `folders` (new collection)
- `projects` (modified: added `folderId` field)
- `migrations` (tracks migration status)

### Indexes Created
- `projects.createdBy_1_folderId_1`
- `projects.createdBy_1_folderId_1_createdAt_-1`
- `projects.folderId_1_createdAt_-1`
- `folders.createdBy_1_parentFolder_1`
- `folders.createdBy_1_parentFolder_1_name_1`
- `folders.path_1`
- `folders.path_1_createdBy_1`

### New Indexes (Enhanced UI Features)
- `videos.projectId_1_projectFolderId_1_status_1` (folder tree queries)
- `videos.duration_1` (duration-based filtering)
- `videos.size_1` (size-based filtering)
- `videos.status_1` (status filtering)
- `videos.storageClass_1` (storage class filtering)
- `videos.uploadedOn_-1` (date-based sorting)
- `videos.title_text_description_text` (text search)
- `folders.name_text_description_text` (folder text search)

## New Migration Features (Enhanced UI)

### Video Duration Extraction
Migration 007 extracts video durations for enhanced UI features:

```bash
# Run duration extraction (can take time for large collections)
node scripts/007-extract-existing-video-durations.js

# Configuration options (edit script):
# - BATCH_SIZE: Videos processed at once (default: 10)
# - DELAY_BETWEEN_BATCHES: Delay between batches (default: 5000ms)
```

**Important Notes:**
- This process can take significant time for large video collections
- Videos are processed in batches to avoid overwhelming the system
- Existing videos will have duration = 0 until processed
- New video uploads will automatically extract duration

### Performance Optimization
Migration 008 optimizes database performance:

```bash
# Run performance optimization
node scripts/008-optimize-folder-performance.js
```

**Features:**
- Creates optimized indexes for folder statistics
- Sets up advanced search indexes
- Validates and fixes folder structure integrity
- Generates detailed performance reports
- Fixes orphaned videos and circular folder references

### Advanced Search Support
The new migrations enable:
- Text search across video titles and descriptions
- Folder name and description search
- Date range filtering (today, week, month, year)
- File size filtering (small, medium, large)
- Status filtering (processing, ready, archived)
- Bulk operations optimization

### Validation Queries (Enhanced)

```javascript
// Check video duration extraction progress
db.videos.aggregate([
  { $group: {
    _id: null,
    total: { $sum: 1 },
    withDuration: { $sum: { $cond: [{ $gt: ["$duration", 0] }, 1, 0] } }
  }}
])

// Check folder statistics integrity
db.videos.aggregate([
  { $match: { projectFolderId: { $ne: null } } },
  { $lookup: { from: 'folders', localField: 'projectFolderId', foreignField: '_id', as: 'folder' } },
  { $match: { folder: { $size: 0 } } },
  { $count: "orphanedVideos" }
])

// Performance check - average query times
db.videos.explain("executionStats").find({
  projectId: ObjectId("..."),
  projectFolderId: null
})
```
