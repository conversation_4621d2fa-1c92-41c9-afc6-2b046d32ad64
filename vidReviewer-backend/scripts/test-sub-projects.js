#!/usr/bin/env node

/**
 * Sub-Project Functionality Test Script
 * 
 * This script comprehensively tests the sub-project functionality including:
 * 1. Creating sub-projects within a project
 * 2. Testing the 3 sub-project limit
 * 3. Creating nested sub-projects
 * 4. Verifying error handling and validation
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';

// Load environment variables
dotenv.config();

// Import models
import Project from '../models/project.js';
import Folder from '../models/folder.js';

async function testSubProjectFunctionality() {
    console.log('🧪 Sub-Project Functionality Test Suite');
    console.log('=======================================');

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Connected to database');

        // Find or create a test project
        let testProject = await Project.findOne({ title: 'Sub-Project Test Project' });
        
        if (!testProject) {
            testProject = new Project({
                title: 'Sub-Project Test Project',
                description: 'A test project for sub-project functionality',
                createdBy: new mongoose.Types.ObjectId(),
                name: 'Test User',
                email: '<EMAIL>'
            });
            await testProject.save();
            console.log('✅ Created test project:', testProject.title);
        } else {
            console.log('✅ Using existing test project:', testProject.title);
        }

        // Clean up existing test sub-projects
        await Folder.deleteMany({
            contextType: 'project',
            contextId: testProject._id,
            name: { $regex: /^Test Sub-Project/ }
        });
        console.log('✅ Cleaned up existing test sub-projects');

        // Test 1: Create 3 sub-projects (should all succeed)
        console.log('\n📝 Test 1: Creating 3 sub-projects (limit test)...');
        const subProjects = [];
        
        for (let i = 1; i <= 3; i++) {
            const subProject = new Folder({
                name: `Test Sub-Project ${i}`,
                description: `Test sub-project number ${i}`,
                createdBy: new mongoose.Types.ObjectId(),
                contextType: 'project',
                contextId: testProject._id
            });
            
            await subProject.save();
            subProjects.push(subProject);
            console.log(`   ✅ Created sub-project ${i}: ${subProject.name}`);
        }

        // Test 2: Try to create 4th sub-project (should fail)
        console.log('\n📝 Test 2: Attempting to create 4th sub-project (should fail)...');
        try {
            const subProject4 = new Folder({
                name: 'Test Sub-Project 4',
                description: 'Fourth test sub-project (should fail)',
                createdBy: new mongoose.Types.ObjectId(),
                contextType: 'project',
                contextId: testProject._id
            });
            
            await subProject4.save();
            console.log('❌ ERROR: Fourth sub-project was created (should have failed)');
        } catch (error) {
            if (error.message === 'Maximum of 3 sub-projects allowed per project') {
                console.log('   ✅ Correctly prevented fourth sub-project:', error.message);
            } else {
                console.log('   ⚠️  Unexpected error:', error.message);
            }
        }

        // Test 3: Create nested sub-projects (should work)
        console.log('\n📝 Test 3: Creating nested sub-projects...');
        const nestedSubProject = new Folder({
            name: 'Test Nested Sub-Project',
            description: 'A sub-project within a sub-project',
            createdBy: new mongoose.Types.ObjectId(),
            contextType: 'project',
            contextId: testProject._id,
            parentFolder: subProjects[0]._id
        });
        
        await nestedSubProject.save();
        console.log('   ✅ Created nested sub-project:', nestedSubProject.name);

        // Test 4: Verify sub-project count and structure
        console.log('\n📝 Test 4: Verifying sub-project structure...');
        const rootSubProjectCount = await Folder.countDocuments({
            contextType: 'project',
            contextId: testProject._id,
            parentFolder: null
        });
        
        const totalSubProjectCount = await Folder.countDocuments({
            contextType: 'project',
            contextId: testProject._id
        });
        
        console.log(`   Root sub-projects: ${rootSubProjectCount}/3`);
        console.log(`   Total sub-projects: ${totalSubProjectCount}`);
        
        if (rootSubProjectCount === 3) {
            console.log('   ✅ Root sub-project limit working correctly');
        } else {
            console.log('   ❌ ERROR: Unexpected root sub-project count');
        }

        // Test 5: Test sub-project hierarchy
        console.log('\n📝 Test 5: Testing sub-project hierarchy...');
        const allSubProjects = await Folder.find({
            contextType: 'project',
            contextId: testProject._id
        }).sort({ createdAt: 1 });

        console.log('   📋 Sub-project structure:');
        allSubProjects.forEach((sp) => {
            const indent = sp.parentFolder ? '      └─ ' : '   ├─ ';
            console.log(`${indent}${sp.name} (Level: ${sp.level})`);
        });

        // Test 6: Test contextType validation
        console.log('\n📝 Test 6: Testing contextType validation...');
        try {
            const invalidSubProject = new Folder({
                name: 'Invalid Context Sub-Project',
                description: 'Should fail due to invalid contextType',
                createdBy: new mongoose.Types.ObjectId(),
                contextType: 'invalid',
                contextId: testProject._id
            });
            
            await invalidSubProject.save();
            console.log('   ❌ ERROR: Invalid contextType was accepted');
        } catch (error) {
            if (error.message.includes('enum')) {
                console.log('   ✅ Correctly rejected invalid contextType');
            } else {
                console.log('   ⚠️  Unexpected validation error:', error.message);
            }
        }

        // Test 7: Test missing contextId validation
        console.log('\n📝 Test 7: Testing contextId validation...');
        try {
            const noContextSubProject = new Folder({
                name: 'No Context Sub-Project',
                description: 'Should fail due to missing contextId',
                createdBy: new mongoose.Types.ObjectId(),
                contextType: 'project'
                // Missing contextId
            });
            
            await noContextSubProject.save();
            console.log('   ❌ ERROR: Missing contextId was accepted');
        } catch (error) {
            console.log('   ✅ Correctly rejected missing contextId');
        }

        // Final Summary
        console.log('\n🎉 All tests completed!');
        console.log('======================');
        console.log('📊 Test Results Summary:');
        console.log(`   - Project: ${testProject.title}`);
        console.log(`   - Root sub-projects created: ${rootSubProjectCount}/3`);
        console.log(`   - Total sub-projects: ${totalSubProjectCount}`);
        console.log(`   - Nested sub-projects: ${totalSubProjectCount - rootSubProjectCount}`);
        console.log('');
        console.log('✅ Sub-project functionality working correctly:');
        console.log('   • 3 sub-project limit enforced');
        console.log('   • Nested sub-projects supported');
        console.log('   • Validation working properly');
        console.log('   • Error handling functional');

        return {
            success: true,
            projectId: testProject._id,
            rootSubProjects: rootSubProjectCount,
            totalSubProjects: totalSubProjectCount,
            testsCompleted: 7
        };

    } catch (error) {
        console.error('\n❌ Test suite failed:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from database');
    }
}

// Run test if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testSubProjectFunctionality().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

export default testSubProjectFunctionality;
