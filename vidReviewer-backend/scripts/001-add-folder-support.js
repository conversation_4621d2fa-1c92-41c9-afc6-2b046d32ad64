#!/usr/bin/env node

/**
 * Migration Script: Add Folder Support
 * 
 * This script migrates the database to support the new folder functionality:
 * 1. Ensures the folders collection exists
 * 2. Adds folderId field to existing projects (if not already present)
 * 3. Creates necessary indexes for performance
 * 4. Validates the migration
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';

// Load environment variables
dotenv.config();

// Import models to ensure schemas are registered
import Project from '../models/project.js';
import Folder from '../models/folder.js';

const MIGRATION_VERSION = '001-add-folder-support';

async function runMigration() {
    console.log(`🚀 Starting migration: ${MIGRATION_VERSION}`);
    console.log('=====================================');

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Connected to database');

        // Check if migration has already been run
        const migrationRecord = await mongoose.connection.db
            .collection('migrations')
            .findOne({ version: MIGRATION_VERSION });

        if (migrationRecord) {
            console.log('⚠️  Migration already applied. Skipping...');
            return;
        }

        // Step 1: Ensure folders collection exists
        console.log('\n📁 Step 1: Ensuring folders collection exists...');
        const collections = await mongoose.connection.db.listCollections().toArray();
        const foldersCollectionExists = collections.some(col => col.name === 'folders');
        
        if (!foldersCollectionExists) {
            await mongoose.connection.db.createCollection('folders');
            console.log('✅ Created folders collection');
        } else {
            console.log('✅ Folders collection already exists');
        }

        // Step 2: Add folderId field to existing projects
        console.log('\n📋 Step 2: Adding folderId field to existing projects...');
        
        // Count projects without folderId field
        const projectsWithoutFolderId = await Project.countDocuments({
            folderId: { $exists: false }
        });

        console.log(`Found ${projectsWithoutFolderId} projects without folderId field`);

        if (projectsWithoutFolderId > 0) {
            // Add folderId field with null value (root folder) to existing projects
            const updateResult = await Project.updateMany(
                { folderId: { $exists: false } },
                { $set: { folderId: null } }
            );

            console.log(`✅ Updated ${updateResult.modifiedCount} projects with folderId field`);
        } else {
            console.log('✅ All projects already have folderId field');
        }

        // Step 3: Create indexes for performance
        console.log('\n🔍 Step 3: Creating database indexes...');
        
        // Index for projects by user and folder
        try {
            await Project.collection.createIndex(
                { createdBy: 1, folderId: 1 },
                { name: 'createdBy_1_folderId_1' }
            );
            console.log('✅ Created index: projects.createdBy_1_folderId_1');
        } catch (error) {
            if (error.code === 85) { // Index already exists
                console.log('✅ Index already exists: projects.createdBy_1_folderId_1');
            } else {
                throw error;
            }
        }

        // Index for folders by user and parent
        try {
            await Folder.collection.createIndex(
                { createdBy: 1, parentFolder: 1 },
                { name: 'createdBy_1_parentFolder_1' }
            );
            console.log('✅ Created index: folders.createdBy_1_parentFolder_1');
        } catch (error) {
            if (error.code === 85) { // Index already exists
                console.log('✅ Index already exists: folders.createdBy_1_parentFolder_1');
            } else {
                throw error;
            }
        }

        // Index for folders by path (for efficient path queries)
        try {
            await Folder.collection.createIndex(
                { path: 1 },
                { name: 'path_1' }
            );
            console.log('✅ Created index: folders.path_1');
        } catch (error) {
            if (error.code === 85) { // Index already exists
                console.log('✅ Index already exists: folders.path_1');
            } else {
                throw error;
            }
        }

        // Step 4: Validate migration
        console.log('\n✅ Step 4: Validating migration...');
        
        const totalProjects = await Project.countDocuments();
        const projectsWithFolderId = await Project.countDocuments({
            folderId: { $exists: true }
        });

        console.log(`Total projects: ${totalProjects}`);
        console.log(`Projects with folderId: ${projectsWithFolderId}`);

        if (totalProjects === projectsWithFolderId) {
            console.log('✅ All projects have folderId field');
        } else {
            throw new Error('Migration validation failed: Some projects missing folderId field');
        }

        // Record successful migration
        await mongoose.connection.db.collection('migrations').insertOne({
            version: MIGRATION_VERSION,
            appliedAt: new Date(),
            description: 'Add folder support to projects'
        });

        console.log('\n🎉 Migration completed successfully!');
        console.log('=====================================');

    } catch (error) {
        console.error('\n❌ Migration failed:', error);
        process.exit(1);
    } finally {
        await mongoose.connection.close();
        console.log('📡 Database connection closed');
    }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runMigration();
}

export default runMigration;
