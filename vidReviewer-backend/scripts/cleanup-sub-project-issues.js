#!/usr/bin/env node

/**
 * Sub-Project Issues Cleanup Script
 * 
 * This script fixes the issues detected by the health check:
 * 1. Orphaned folders (folders with non-existent parents)
 * 2. Invalid contextType values
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectToDB } from '../config/db.js';

// Load environment variables
dotenv.config();

// Import models
import Folder from '../models/folder.js';

async function cleanupSubProjectIssues() {
    console.log('🧹 Sub-Project Issues Cleanup');
    console.log('=============================');

    try {
        // Connect to database
        await connectToDB();
        console.log('✅ Connected to database');

        let totalFixed = 0;

        // Fix 1: Clean up orphaned folders
        console.log('\n🔍 Fixing orphaned folders...');
        const foldersWithParent = await Folder.find({
            parentFolder: { $ne: null }
        });

        let orphanedCount = 0;
        for (const folder of foldersWithParent) {
            const parentExists = await Folder.findById(folder.parentFolder);
            if (!parentExists) {
                console.log(`   🔧 Fixing orphaned folder: ${folder.name} (removing parent reference)`);
                
                // Remove the parent reference and reset to root level
                await Folder.findByIdAndUpdate(folder._id, {
                    parentFolder: null,
                    level: 0,
                    path: folder.name
                });
                
                orphanedCount++;
                totalFixed++;
            }
        }

        if (orphanedCount === 0) {
            console.log('   ✅ No orphaned folders found');
        } else {
            console.log(`   ✅ Fixed ${orphanedCount} orphaned folders`);
        }

        // Fix 2: Fix invalid contextType values
        console.log('\n🔍 Fixing invalid contextType values...');
        const invalidContextFolders = await Folder.find({
            contextType: { $nin: ['user', 'project'] }
        });

        if (invalidContextFolders.length === 0) {
            console.log('   ✅ No invalid contextType values found');
        } else {
            console.log(`   Found ${invalidContextFolders.length} folders with invalid contextType`);
            
            for (const folder of invalidContextFolders) {
                console.log(`   🔧 Fixing folder: ${folder.name} (contextType: "${folder.contextType}")`);
                
                // Determine correct contextType based on contextId
                let newContextType = 'user'; // default
                
                if (folder.contextId) {
                    // If it has a contextId, it's likely a project folder
                    // Let's check if the contextId matches any project
                    try {
                        const Project = mongoose.model('Project');
                        const projectExists = await Project.findById(folder.contextId);
                        if (projectExists) {
                            newContextType = 'project';
                            console.log(`     → Setting to 'project' (found matching project: ${projectExists.title})`);
                        } else {
                            console.log(`     → Setting to 'user' (no matching project found)`);
                        }
                    } catch (error) {
                        console.log(`     → Setting to 'user' (error checking project: ${error.message})`);
                    }
                } else {
                    console.log(`     → Setting to 'user' (no contextId)`);
                }
                
                await Folder.findByIdAndUpdate(folder._id, {
                    contextType: newContextType
                });
                
                totalFixed++;
            }
            
            console.log(`   ✅ Fixed ${invalidContextFolders.length} invalid contextType values`);
        }

        // Fix 3: Clean up any remaining inconsistencies
        console.log('\n🔍 Checking for additional inconsistencies...');
        
        // Check for project folders without contextId
        const projectFoldersWithoutContextId = await Folder.find({
            contextType: 'project',
            $or: [
                { contextId: null },
                { contextId: { $exists: false } }
            ]
        });

        if (projectFoldersWithoutContextId.length > 0) {
            console.log(`   Found ${projectFoldersWithoutContextId.length} project folders without contextId`);
            
            for (const folder of projectFoldersWithoutContextId) {
                console.log(`   🔧 Converting project folder to user folder: ${folder.name}`);
                
                await Folder.findByIdAndUpdate(folder._id, {
                    contextType: 'user',
                    $unset: { contextId: "" }
                });
                
                totalFixed++;
            }
            
            console.log(`   ✅ Fixed ${projectFoldersWithoutContextId.length} project folders without contextId`);
        } else {
            console.log('   ✅ All project folders have valid contextId');
        }

        // Summary
        console.log('\n🎉 Cleanup completed!');
        console.log('====================');
        console.log(`Total issues fixed: ${totalFixed}`);
        
        if (totalFixed > 0) {
            console.log('\n📝 Changes made:');
            console.log(`• Fixed ${orphanedCount} orphaned folders`);
            console.log(`• Fixed ${invalidContextFolders.length} invalid contextType values`);
            console.log(`• Fixed ${projectFoldersWithoutContextId.length} project folders without contextId`);
            console.log('\n🔄 Recommended next steps:');
            console.log('1. Run health check again: npm run sub-projects:health-check');
            console.log('2. Verify system functionality: npm run sub-projects:test');
        } else {
            console.log('✅ No issues found - system is clean!');
        }

        return {
            totalFixed,
            orphanedFixed: orphanedCount,
            invalidContextTypeFixed: invalidContextFolders.length,
            projectFoldersFixed: projectFoldersWithoutContextId.length
        };

    } catch (error) {
        console.error('\n❌ Cleanup failed:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from database');
    }
}

// Run cleanup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    cleanupSubProjectIssues().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

export default cleanupSubProjectIssues;
