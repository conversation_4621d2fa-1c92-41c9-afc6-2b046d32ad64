import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

const uri = process.env.MONGO_URI
let isConnected = false;

async function connectToDB() {
    if (isConnected) {
        console.log("Already connected to the database");
        return;
    }
    try {
        await mongoose.connect(uri, {
            dbName: 'vidlead-users'
        });

        console.log("uri", uri);
        isConnected = true;
        console.log("Connected to the 'vidlead' database");
    } catch (error) {
        console.error('Error connecting to MongoDB:', error);
        throw error;
    }
}
async function closeDatabaseConnections() {
    if (isConnected) {
        await mongoose.connection.close();
        isConnected = false;
        console.log("Database connection closed");
    }
}


export { connectToDB, closeDatabaseConnections };