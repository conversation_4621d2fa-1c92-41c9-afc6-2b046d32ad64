import { describe, it, expect, beforeEach } from 'vitest';
import mongoose from 'mongoose';
import Folder from '../../models/folder.js';
import Project from '../../models/project.js';
import Video from '../../models/video.js';

describe('Folder Edge Cases and Error Handling', () => {
  let userId, projectId;

  beforeEach(() => {
    userId = new mongoose.Types.ObjectId();
    projectId = new mongoose.Types.ObjectId();
  });

  describe('Path Generation Edge Cases', () => {
    it('should handle special characters in folder names', async () => {
      const folder = new Folder({
        name: 'Folder with "quotes" & symbols!',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      expect(folder.path).toBe('/Folder with "quotes" & symbols!');
    });

    it('should handle very long folder names', async () => {
      const longName = 'A'.repeat(200);
      const folder = new Folder({
        name: longName,
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      expect(folder.path).toBe(`/${longName}`);
    });

    it('should handle deeply nested folder structures', async () => {
      let currentParent = null;
      const folderNames = [];

      // Create 10 levels of nesting
      for (let i = 0; i < 10; i++) {
        const name = `Level${i}`;
        folderNames.push(name);
        
        const folder = new Folder({
          name,
          createdBy: userId,
          contextType: 'user',
          parentFolder: currentParent
        });
        await folder.save();
        
        currentParent = folder._id;
        expect(folder.level).toBe(i);
      }

      const deepestFolder = await Folder.findById(currentParent);
      expect(deepestFolder.path).toBe('/' + folderNames.join('/'));
      expect(deepestFolder.level).toBe(9);
    });

    it('should handle folder name changes in nested structures', async () => {
      // Create parent
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      // Create child
      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await child.save();

      // Create grandchild
      const grandchild = new Folder({
        name: 'Grandchild',
        createdBy: userId,
        contextType: 'user',
        parentFolder: child._id
      });
      await grandchild.save();

      // Rename parent
      parent.name = 'NewParent';
      await parent.save();

      // Check that child and grandchild paths are updated
      const updatedChild = await Folder.findById(child._id);
      const updatedGrandchild = await Folder.findById(grandchild._id);

      expect(parent.path).toBe('/NewParent');
      expect(updatedChild.path).toBe('/NewParent/Child');
      expect(updatedGrandchild.path).toBe('/NewParent/Child/Grandchild');
    });
  });

  describe('Circular Reference Prevention', () => {
    it('should prevent direct circular reference', async () => {
      const folder = new Folder({
        name: 'SelfReference',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      // Try to set folder as its own parent
      const canMove = await Folder.canMoveFolder(folder._id, folder._id);
      expect(canMove).toBe(false);
    });

    it('should prevent indirect circular reference', async () => {
      // Create A -> B -> C structure
      const folderA = new Folder({
        name: 'A',
        createdBy: userId,
        contextType: 'user'
      });
      await folderA.save();

      const folderB = new Folder({
        name: 'B',
        createdBy: userId,
        contextType: 'user',
        parentFolder: folderA._id
      });
      await folderB.save();

      const folderC = new Folder({
        name: 'C',
        createdBy: userId,
        contextType: 'user',
        parentFolder: folderB._id
      });
      await folderC.save();

      // Try to move A under C (would create A -> B -> C -> A)
      const canMove = await Folder.canMoveFolder(folderA._id, folderC._id);
      expect(canMove).toBe(false);
    });

    it('should allow valid folder moves', async () => {
      // Create A -> B and C -> D structures
      const folderA = new Folder({
        name: 'A',
        createdBy: userId,
        contextType: 'user'
      });
      await folderA.save();

      const folderB = new Folder({
        name: 'B',
        createdBy: userId,
        contextType: 'user',
        parentFolder: folderA._id
      });
      await folderB.save();

      const folderC = new Folder({
        name: 'C',
        createdBy: userId,
        contextType: 'user'
      });
      await folderC.save();

      const folderD = new Folder({
        name: 'D',
        createdBy: userId,
        contextType: 'user',
        parentFolder: folderC._id
      });
      await folderD.save();

      // Should allow moving B under C (no circular reference)
      const canMove = await Folder.canMoveFolder(folderB._id, folderC._id);
      expect(canMove).toBe(true);
    });
  });

  describe('Context Validation Edge Cases', () => {
    it('should handle null contextId for user folders', async () => {
      const folder = new Folder({
        name: 'User Folder',
        createdBy: userId,
        contextType: 'user',
        contextId: null
      });
      await folder.save();

      expect(folder.contextId).toBeNull();
    });

    it('should reject undefined contextId for project folders', async () => {
      const folder = new Folder({
        name: 'Project Folder',
        createdBy: userId,
        contextType: 'project'
        // contextId is undefined
      });

      await expect(folder.save()).rejects.toThrow();
    });

    it('should handle contextId changes', async () => {
      const folder = new Folder({
        name: 'Project Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await folder.save();

      // Try to change contextId (should trigger validation)
      const newProjectId = new mongoose.Types.ObjectId();
      folder.contextId = newProjectId;
      
      // This should work as it's a valid project folder
      await folder.save();
      expect(folder.contextId.toString()).toBe(newProjectId.toString());
    });
  });

  describe('Database Constraint Edge Cases', () => {
    it('should handle concurrent folder creation with same name', async () => {
      const folderData = {
        name: 'Concurrent Folder',
        createdBy: userId,
        contextType: 'user'
      };

      // Create two folders simultaneously
      const folder1Promise = new Folder(folderData).save();
      const folder2Promise = new Folder(folderData).save();

      // Both should succeed as there's no unique constraint on name
      const [folder1, folder2] = await Promise.all([folder1Promise, folder2Promise]);
      
      expect(folder1.name).toBe('Concurrent Folder');
      expect(folder2.name).toBe('Concurrent Folder');
      expect(folder1._id.toString()).not.toBe(folder2._id.toString());
    });

    it('should handle orphaned folder references', async () => {
      // Create parent folder
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      // Create child folder
      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await child.save();

      // Delete parent directly from database (simulating orphaned reference)
      await Folder.findByIdAndDelete(parent._id);

      // Try to save child (should fail due to parent not found)
      child.name = 'Updated Child';
      await expect(child.save()).rejects.toThrow('Parent folder not found');
    });

    it('should handle invalid ObjectId references', async () => {
      const folder = new Folder({
        name: 'Test Folder',
        createdBy: userId,
        contextType: 'user',
        parentFolder: 'invalid-object-id'
      });

      await expect(folder.save()).rejects.toThrow();
    });
  });

  describe('Performance Edge Cases', () => {
    it('should handle large number of sibling folders', async () => {
      const folderPromises = [];
      
      // Create 100 sibling folders
      for (let i = 0; i < 100; i++) {
        folderPromises.push(
          new Folder({
            name: `Folder ${i}`,
            createdBy: userId,
            contextType: 'user'
          }).save()
        );
      }

      const folders = await Promise.all(folderPromises);
      expect(folders).toHaveLength(100);

      // Test getting folder tree (should handle large number of siblings)
      const tree = await Folder.getFolderTree('user', null, userId);
      expect(tree).toHaveLength(100);
    });

    it('should handle folder tree with mixed contexts', async () => {
      // Create user folders
      const userFolder = new Folder({
        name: 'User Folder',
        createdBy: userId,
        contextType: 'user'
      });
      await userFolder.save();

      // Create project folders
      const projectFolder = new Folder({
        name: 'Project Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await projectFolder.save();

      // Get user folder tree (should only return user folders)
      const userTree = await Folder.getFolderTree('user', null, userId);
      expect(userTree).toHaveLength(1);
      expect(userTree[0].name).toBe('User Folder');

      // Get project folder tree (should only return project folders)
      const projectTree = await Folder.getFolderTree('project', projectId, userId);
      expect(projectTree).toHaveLength(1);
      expect(projectTree[0].name).toBe('Project Folder');
    });
  });

  describe('Data Integrity Edge Cases', () => {
    it('should maintain referential integrity when deleting folders', async () => {
      // Create folder with project
      const folder = new Folder({
        name: 'Folder with Project',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      const project = new Project({
        title: 'Test Project',
        description: 'Test Description',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>',
        folderId: folder._id
      });
      await project.save();

      // Delete folder
      await Folder.findByIdAndDelete(folder._id);

      // Project should still exist but with null folderId
      const updatedProject = await Project.findById(project._id);
      expect(updatedProject).toBeTruthy();
      // Note: In a real application, you might want to set folderId to null
      // or handle this case differently
    });

    it('should handle concurrent folder operations', async () => {
      const folder = new Folder({
        name: 'Concurrent Test',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      // Simulate concurrent updates
      const update1Promise = Folder.findByIdAndUpdate(
        folder._id,
        { name: 'Updated Name 1' },
        { new: true }
      );

      const update2Promise = Folder.findByIdAndUpdate(
        folder._id,
        { description: 'Updated Description' },
        { new: true }
      );

      const [result1, result2] = await Promise.all([update1Promise, update2Promise]);
      
      // Both updates should succeed
      expect(result1).toBeTruthy();
      expect(result2).toBeTruthy();
    });
  });
});
