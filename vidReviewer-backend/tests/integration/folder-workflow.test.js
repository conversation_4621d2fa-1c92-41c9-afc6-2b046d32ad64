import { describe, it, expect, beforeEach } from 'vitest';
import request from 'supertest';
import express from 'express';
import mongoose from 'mongoose';
import Folder from '../../models/folder.js';
import Project from '../../models/project.js';
import Video from '../../models/video.js';
import * as folderController from '../../controllers/folder.js';
import * as projectController from '../../controllers/project.js';
import * as projectFolderController from '../../controllers/projectFolder.js';

// Create test app with all routes
const app = express();
app.use(express.json());

// User folder routes
app.get('/folders/:userId', folderController.getFolders);
app.get('/folders/tree/:userId', folderController.getFolderTree);
app.post('/folders', folderController.createFolder);
app.put('/folders/:folderId', folderController.updateFolder);
app.delete('/folders/:folderId', folderController.deleteFolder);
app.patch('/folders/:folderId/move', folderController.moveFolder);
app.get('/folders/:folderId/contents', folderController.getFolderContents);

// Project routes
app.post('/projects', projectController.createProject);
app.patch('/projects/:projectId/move', projectController.moveProject);

// Project folder routes
app.get('/projects/:projectId/folders/tree', projectFolderController.getProjectFolderTree);
app.post('/projects/:projectId/folders', projectFolderController.createProjectFolder);
app.delete('/projects/:projectId/folders/:folderId', projectFolderController.deleteProjectFolder);

describe('Folder Workflow Integration Tests', () => {
  let userId;

  beforeEach(() => {
    userId = new mongoose.Types.ObjectId();
  });

  describe('Complete User Folder Workflow', () => {
    it('should handle complete folder organization workflow', async () => {
      // 1. Create root folders
      const clientsFolder = await request(app)
        .post('/folders')
        .send({
          name: 'Clients',
          description: 'Client projects',
          createdBy: userId
        })
        .expect(201);

      const personalFolder = await request(app)
        .post('/folders')
        .send({
          name: 'Personal',
          description: 'Personal projects',
          createdBy: userId
        })
        .expect(201);

      // 2. Create nested folders
      const client1Folder = await request(app)
        .post('/folders')
        .send({
          name: 'Client 1',
          createdBy: userId,
          parentFolder: clientsFolder.body._id
        })
        .expect(201);

      const client2Folder = await request(app)
        .post('/folders')
        .send({
          name: 'Client 2',
          createdBy: userId,
          parentFolder: clientsFolder.body._id
        })
        .expect(201);

      // 3. Create projects in folders
      const project1 = await request(app)
        .post('/projects')
        .send({
          title: 'Client 1 Project',
          description: 'Project for client 1',
          createdBy: userId,
          name: 'Test User',
          email: '<EMAIL>',
          folderId: client1Folder.body._id
        })
        .expect(201);

      const project2 = await request(app)
        .post('/projects')
        .send({
          title: 'Personal Project',
          description: 'My personal project',
          createdBy: userId,
          name: 'Test User',
          email: '<EMAIL>',
          folderId: personalFolder.body._id
        })
        .expect(201);

      // 4. Get folder tree with projects
      const treeResponse = await request(app)
        .get(`/folders/tree/${userId}?includeProjects=true`)
        .expect(200);

      expect(treeResponse.body.folderTree).toHaveLength(2);
      
      // Find clients folder in tree
      const clientsInTree = treeResponse.body.folderTree.find(f => f.name === 'Clients');
      expect(clientsInTree.children).toHaveLength(2);
      expect(clientsInTree.children.find(c => c.name === 'Client 1').projects).toHaveLength(1);
      
      // Find personal folder in tree
      const personalInTree = treeResponse.body.folderTree.find(f => f.name === 'Personal');
      expect(personalInTree.projects).toHaveLength(1);

      // 5. Move project between folders
      await request(app)
        .patch(`/projects/${project1.body._id}/move`)
        .send({ folderId: personalFolder.body._id })
        .expect(200);

      // 6. Verify project moved
      const updatedTreeResponse = await request(app)
        .get(`/folders/tree/${userId}?includeProjects=true`)
        .expect(200);

      const updatedPersonalInTree = updatedTreeResponse.body.folderTree.find(f => f.name === 'Personal');
      expect(updatedPersonalInTree.projects).toHaveLength(2);

      const updatedClientsInTree = updatedTreeResponse.body.folderTree.find(f => f.name === 'Clients');
      const updatedClient1InTree = updatedClientsInTree.children.find(c => c.name === 'Client 1');
      expect(updatedClient1InTree.projects).toHaveLength(0);

      // 7. Reorganize folder structure
      await request(app)
        .patch(`/folders/${client2Folder.body._id}/move`)
        .send({ parentFolder: null })
        .expect(200);

      // 8. Verify folder moved to root
      const finalTreeResponse = await request(app)
        .get(`/folders/tree/${userId}`)
        .expect(200);

      expect(finalTreeResponse.body.folderTree).toHaveLength(3);
      expect(finalTreeResponse.body.folderTree.map(f => f.name).sort()).toEqual(['Client 2', 'Clients', 'Personal']);
    });

    it('should handle folder deletion workflow', async () => {
      // 1. Create folder structure
      const parentFolder = await request(app)
        .post('/folders')
        .send({
          name: 'Parent',
          createdBy: userId
        })
        .expect(201);

      const childFolder = await request(app)
        .post('/folders')
        .send({
          name: 'Child',
          createdBy: userId,
          parentFolder: parentFolder.body._id
        })
        .expect(201);

      // 2. Try to delete parent with child (should fail)
      await request(app)
        .delete(`/folders/${parentFolder.body._id}`)
        .expect(400);

      // 3. Delete child first
      await request(app)
        .delete(`/folders/${childFolder.body._id}`)
        .expect(200);

      // 4. Now delete parent (should succeed)
      await request(app)
        .delete(`/folders/${parentFolder.body._id}`)
        .expect(200);

      // 5. Verify both folders are deleted
      const folders = await Folder.find({ createdBy: userId });
      expect(folders).toHaveLength(0);
    });
  });

  describe('Complete Project Folder Workflow', () => {
    let projectId;

    beforeEach(async () => {
      // Create test project
      const project = new Project({
        title: 'Test Project',
        description: 'Test Description',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>'
      });
      await project.save();
      projectId = project._id;
    });

    it('should handle complete project folder organization', async () => {
      // 1. Create project folder structure
      const rawFootageFolder = await request(app)
        .post(`/projects/${projectId}/folders`)
        .send({
          name: 'Raw Footage',
          description: 'Unedited video files',
          createdBy: userId
        })
        .expect(201);

      const editedFolder = await request(app)
        .post(`/projects/${projectId}/folders`)
        .send({
          name: 'Edited',
          description: 'Edited video files',
          createdBy: userId
        })
        .expect(201);

      // 2. Create nested folders
      const finalCutsFolder = await request(app)
        .post(`/projects/${projectId}/folders`)
        .send({
          name: 'Final Cuts',
          createdBy: userId,
          parentFolder: editedFolder.body._id
        })
        .expect(201);

      const draftsFolder = await request(app)
        .post(`/projects/${projectId}/folders`)
        .send({
          name: 'Drafts',
          createdBy: userId,
          parentFolder: editedFolder.body._id
        })
        .expect(201);

      // 3. Create videos in folders
      const rawVideo = new Video({
        title: 'Raw Video 1',
        projectId: projectId,
        projectFolderId: rawFootageFolder.body._id,
        videoUrl: 'https://example.com/raw1.mp4',
        status: 'complete',
        uploadedBy: userId
      });
      await rawVideo.save();

      const finalVideo = new Video({
        title: 'Final Video',
        projectId: projectId,
        projectFolderId: finalCutsFolder.body._id,
        videoUrl: 'https://example.com/final.mp4',
        status: 'complete',
        uploadedBy: userId
      });
      await finalVideo.save();

      const rootVideo = new Video({
        title: 'Root Video',
        projectId: projectId,
        projectFolderId: null,
        videoUrl: 'https://example.com/root.mp4',
        status: 'complete',
        uploadedBy: userId
      });
      await rootVideo.save();

      // 4. Get project folder tree with videos
      const treeResponse = await request(app)
        .get(`/projects/${projectId}/folders/tree?includeVideos=true`)
        .expect(200);

      expect(treeResponse.body.folderTree).toHaveLength(2);
      expect(treeResponse.body.rootVideos).toHaveLength(1);

      // Find folders in tree
      const rawInTree = treeResponse.body.folderTree.find(f => f.name === 'Raw Footage');
      expect(rawInTree.videos).toHaveLength(1);

      const editedInTree = treeResponse.body.folderTree.find(f => f.name === 'Edited');
      expect(editedInTree.children).toHaveLength(2);
      
      const finalCutsInTree = editedInTree.children.find(c => c.name === 'Final Cuts');
      expect(finalCutsInTree.videos).toHaveLength(1);

      // 5. Try to delete folder with videos (should fail)
      await request(app)
        .delete(`/projects/${projectId}/folders/${rawFootageFolder.body._id}`)
        .expect(400);

      // 6. Delete video first, then folder
      await Video.findByIdAndDelete(rawVideo._id);
      
      await request(app)
        .delete(`/projects/${projectId}/folders/${rawFootageFolder.body._id}`)
        .expect(200);

      // 7. Verify folder structure updated
      const updatedTreeResponse = await request(app)
        .get(`/projects/${projectId}/folders/tree`)
        .expect(200);

      expect(updatedTreeResponse.body.folderTree).toHaveLength(1);
      expect(updatedTreeResponse.body.folderTree[0].name).toBe('Edited');
    });
  });

  describe('Cross-Context Validation', () => {
    it('should prevent mixing user and project folder contexts', async () => {
      // Create user folder
      const userFolder = await request(app)
        .post('/folders')
        .send({
          name: 'User Folder',
          createdBy: userId
        })
        .expect(201);

      // Create project
      const project = new Project({
        title: 'Test Project',
        description: 'Test Description',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>'
      });
      await project.save();

      // Try to create project folder with user folder as parent (should fail)
      await request(app)
        .post(`/projects/${project._id}/folders`)
        .send({
          name: 'Project Folder',
          createdBy: userId,
          parentFolder: userFolder.body._id
        })
        .expect(400);
    });

    it('should prevent cross-project folder operations', async () => {
      // Create two projects
      const project1 = new Project({
        title: 'Project 1',
        description: 'First project',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>'
      });
      await project1.save();

      const project2 = new Project({
        title: 'Project 2',
        description: 'Second project',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>'
      });
      await project2.save();

      // Create folder in project1
      const project1Folder = await request(app)
        .post(`/projects/${project1._id}/folders`)
        .send({
          name: 'Project 1 Folder',
          createdBy: userId
        })
        .expect(201);

      // Try to create folder in project2 with project1 folder as parent (should fail)
      await request(app)
        .post(`/projects/${project2._id}/folders`)
        .send({
          name: 'Project 2 Folder',
          createdBy: userId,
          parentFolder: project1Folder.body._id
        })
        .expect(400);
    });
  });
});
