import { describe, it, expect, beforeEach } from 'vitest';
import request from 'supertest';
import express from 'express';
import mongoose from 'mongoose';
import Folder from '../../models/folder.js';
import Project from '../../models/project.js';
import Video from '../../models/video.js';
import * as projectFolderController from '../../controllers/projectFolder.js';

// Create test app
const app = express();
app.use(express.json());

// Setup routes
app.get('/projects/:projectId/folders/tree', projectFolderController.getProjectFolderTree);
app.post('/projects/:projectId/folders', projectFolderController.createProjectFolder);
app.put('/projects/:projectId/folders/:folderId', projectFolderController.updateProjectFolder);
app.delete('/projects/:projectId/folders/:folderId', projectFolderController.deleteProjectFolder);
app.patch('/projects/:projectId/folders/:folderId/move', projectFolderController.moveProjectFolder);
app.get('/projects/:projectId/folders/:folderId/contents', projectFolderController.getProjectFolderContents);

describe('Project Folder Controller', () => {
  let userId, projectId;

  beforeEach(async () => {
    userId = new mongoose.Types.ObjectId();
    projectId = new mongoose.Types.ObjectId();

    // Create test project
    const project = new Project({
      _id: projectId,
      title: 'Test Project',
      description: 'Test Description',
      createdBy: userId,
      name: 'Test User',
      email: '<EMAIL>'
    });
    await project.save();
  });

  describe('GET /projects/:projectId/folders/tree', () => {
    it('should get project folder tree', async () => {
      // Create project folders
      const parentFolder = new Folder({
        name: 'Parent Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await parentFolder.save();

      const childFolder = new Folder({
        name: 'Child Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId,
        parentFolder: parentFolder._id
      });
      await childFolder.save();

      const response = await request(app)
        .get(`/projects/${projectId}/folders/tree`)
        .expect(200);

      expect(response.body.folderTree).toHaveLength(1);
      expect(response.body.folderTree[0].name).toBe('Parent Folder');
      expect(response.body.folderTree[0].children).toHaveLength(1);
      expect(response.body.folderTree[0].children[0].name).toBe('Child Folder');
    });

    it('should include videos when requested', async () => {
      // Create project folder
      const folder = new Folder({
        name: 'Video Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await folder.save();

      // Create video in folder
      const video = new Video({
        title: 'Test Video',
        projectId: projectId,
        projectFolderId: folder._id,
        videoUrl: 'https://example.com/video.mp4',
        status: 'complete',
        uploadedBy: userId
      });
      await video.save();

      // Create root video
      const rootVideo = new Video({
        title: 'Root Video',
        projectId: projectId,
        projectFolderId: null,
        videoUrl: 'https://example.com/root-video.mp4',
        status: 'complete',
        uploadedBy: userId
      });
      await rootVideo.save();

      const response = await request(app)
        .get(`/projects/${projectId}/folders/tree?includeVideos=true`)
        .expect(200);

      expect(response.body.folderTree[0].videos).toHaveLength(1);
      expect(response.body.folderTree[0].videos[0].title).toBe('Test Video');
      expect(response.body.rootVideos).toHaveLength(1);
      expect(response.body.rootVideos[0].title).toBe('Root Video');
    });

    it('should return 404 for non-existent project', async () => {
      const nonExistentProjectId = new mongoose.Types.ObjectId();
      
      const response = await request(app)
        .get(`/projects/${nonExistentProjectId}/folders/tree`)
        .expect(404);

      expect(response.body.error).toBe('Project not found');
    });
  });

  describe('POST /projects/:projectId/folders', () => {
    it('should create project folder', async () => {
      const folderData = {
        name: 'New Project Folder',
        description: 'Project folder description',
        createdBy: userId
      };

      const response = await request(app)
        .post(`/projects/${projectId}/folders`)
        .send(folderData)
        .expect(201);

      expect(response.body.name).toBe('New Project Folder');
      expect(response.body.contextType).toBe('project');
      expect(response.body.contextId).toBe(projectId.toString());
      expect(response.body.path).toBe('/New Project Folder');
    });

    it('should create nested project folder', async () => {
      // Create parent folder
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await parent.save();

      const folderData = {
        name: 'Child',
        createdBy: userId,
        parentFolder: parent._id
      };

      const response = await request(app)
        .post(`/projects/${projectId}/folders`)
        .send(folderData)
        .expect(201);

      expect(response.body.name).toBe('Child');
      expect(response.body.path).toBe('/Parent/Child');
      expect(response.body.level).toBe(1);
    });

    it('should require name and createdBy', async () => {
      const response = await request(app)
        .post(`/projects/${projectId}/folders`)
        .send({})
        .expect(400);

      expect(response.body.error).toBe('Name and createdBy are required');
    });

    it('should prevent duplicate folder names in same location', async () => {
      // Create first folder
      const folder1 = new Folder({
        name: 'Duplicate Name',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await folder1.save();

      // Try to create second folder with same name
      const folderData = {
        name: 'Duplicate Name',
        createdBy: userId
      };

      const response = await request(app)
        .post(`/projects/${projectId}/folders`)
        .send(folderData)
        .expect(400);

      expect(response.body.error).toBe('Folder with this name already exists in the same location');
    });

    it('should validate parent folder belongs to same project', async () => {
      const otherProjectId = new mongoose.Types.ObjectId();
      
      // Create folder for different project
      const otherProjectFolder = new Folder({
        name: 'Other Project Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: otherProjectId
      });
      await otherProjectFolder.save();

      const folderData = {
        name: 'Test',
        createdBy: userId,
        parentFolder: otherProjectFolder._id
      };

      const response = await request(app)
        .post(`/projects/${projectId}/folders`)
        .send(folderData)
        .expect(400);

      expect(response.body.error).toBe('Parent folder not found or does not belong to this project');
    });
  });

  describe('PUT /projects/:projectId/folders/:folderId', () => {
    it('should update project folder', async () => {
      const folder = new Folder({
        name: 'Original Name',
        description: 'Original Description',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await folder.save();

      const updateData = {
        name: 'Updated Name',
        description: 'Updated Description'
      };

      const response = await request(app)
        .put(`/projects/${projectId}/folders/${folder._id}`)
        .send(updateData)
        .expect(200);

      expect(response.body.name).toBe('Updated Name');
      expect(response.body.description).toBe('Updated Description');
      expect(response.body.path).toBe('/Updated Name');
    });

    it('should return 404 for non-existent folder', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      
      const response = await request(app)
        .put(`/projects/${projectId}/folders/${nonExistentId}`)
        .send({ name: 'New Name' })
        .expect(404);

      expect(response.body.error).toBe('Folder not found');
    });

    it('should validate folder belongs to project', async () => {
      const otherProjectId = new mongoose.Types.ObjectId();
      
      // Create folder for different project
      const otherProjectFolder = new Folder({
        name: 'Other Project Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: otherProjectId
      });
      await otherProjectFolder.save();

      const response = await request(app)
        .put(`/projects/${projectId}/folders/${otherProjectFolder._id}`)
        .send({ name: 'New Name' })
        .expect(404);

      expect(response.body.error).toBe('Folder not found');
    });
  });

  describe('DELETE /projects/:projectId/folders/:folderId', () => {
    it('should delete empty project folder', async () => {
      const folder = new Folder({
        name: 'To Delete',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await folder.save();

      const response = await request(app)
        .delete(`/projects/${projectId}/folders/${folder._id}`)
        .expect(200);

      expect(response.body.message).toBe('Project folder deleted successfully');
      
      // Verify folder is deleted
      const deletedFolder = await Folder.findById(folder._id);
      expect(deletedFolder).toBeNull();
    });

    it('should prevent deleting folder with videos', async () => {
      // Create folder
      const folder = new Folder({
        name: 'With Videos',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await folder.save();

      // Create video in folder
      const video = new Video({
        title: 'Test Video',
        projectId: projectId,
        projectFolderId: folder._id,
        videoUrl: 'https://example.com/video.mp4',
        status: 'complete',
        uploadedBy: userId
      });
      await video.save();

      const response = await request(app)
        .delete(`/projects/${projectId}/folders/${folder._id}`)
        .expect(400);

      expect(response.body.error).toBe('Cannot delete folder that contains videos');
    });

    it('should prevent deleting folder with subfolders', async () => {
      // Create parent folder
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await parent.save();

      // Create child folder
      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId,
        parentFolder: parent._id
      });
      await child.save();

      const response = await request(app)
        .delete(`/projects/${projectId}/folders/${parent._id}`)
        .expect(400);

      expect(response.body.error).toBe('Cannot delete folder that contains subfolders');
    });
  });

  describe('GET /projects/:projectId/folders/:folderId/contents', () => {
    it('should get project folder contents', async () => {
      // Create parent folder
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await parent.save();

      // Create subfolder
      const subfolder = new Folder({
        name: 'Subfolder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId,
        parentFolder: parent._id
      });
      await subfolder.save();

      // Create video in parent folder
      const video = new Video({
        title: 'Test Video',
        projectId: projectId,
        projectFolderId: parent._id,
        videoUrl: 'https://example.com/video.mp4',
        status: 'complete',
        uploadedBy: userId
      });
      await video.save();

      const response = await request(app)
        .get(`/projects/${projectId}/folders/${parent._id}/contents`)
        .expect(200);

      expect(response.body.subfolders).toHaveLength(1);
      expect(response.body.subfolders[0].name).toBe('Subfolder');
      expect(response.body.videos).toHaveLength(1);
      expect(response.body.videos[0].title).toBe('Test Video');
    });
  });
});
