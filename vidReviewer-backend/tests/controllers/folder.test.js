import { describe, it, expect, beforeEach } from 'vitest';
import request from 'supertest';
import express from 'express';
import mongoose from 'mongoose';
import Folder from '../../models/folder.js';
import Project from '../../models/project.js';
import * as folderController from '../../controllers/folder.js';

// Create test app
const app = express();
app.use(express.json());

// Setup routes
app.get('/folders/:userId', folderController.getFolders);
app.get('/folders/tree/:userId', folderController.getFolderTree);
app.get('/folders/:folderId', folderController.getFolderById);
app.get('/folders/:folderId/contents', folderController.getFolderContents);
app.get('/folders/root/contents', folderController.getFolderContents);
app.post('/folders', folderController.createFolder);
app.put('/folders/:folderId', folderController.updateFolder);
app.delete('/folders/:folderId', folderController.deleteFolder);
app.patch('/folders/:folderId/move', folderController.moveFolder);

describe('Folder Controller', () => {
  let userId, projectId;

  beforeEach(() => {
    userId = new mongoose.Types.ObjectId();
    projectId = new mongoose.Types.ObjectId();
  });

  describe('GET /folders/:userId', () => {
    it('should get all folders for a user', async () => {
      // Create test folders
      const folder1 = new Folder({
        name: 'Folder 1',
        createdBy: userId,
        contextType: 'user'
      });
      await folder1.save();

      const folder2 = new Folder({
        name: 'Folder 2',
        createdBy: userId,
        contextType: 'user'
      });
      await folder2.save();

      const response = await request(app)
        .get(`/folders/${userId}`)
        .expect(200);

      expect(response.body.folders).toHaveLength(2);
      expect(response.body.folders.map(f => f.name).sort()).toEqual(['Folder 1', 'Folder 2']);
    });

    it('should return empty array for user with no folders', async () => {
      const response = await request(app)
        .get(`/folders/${userId}`)
        .expect(200);

      expect(response.body.folders).toHaveLength(0);
    });
  });

  describe('GET /folders/tree/:userId', () => {
    it('should get folder tree structure', async () => {
      // Create parent folder
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      // Create child folder
      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await child.save();

      const response = await request(app)
        .get(`/folders/tree/${userId}`)
        .expect(200);

      expect(response.body.folderTree).toHaveLength(1);
      expect(response.body.folderTree[0].name).toBe('Parent');
      expect(response.body.folderTree[0].children).toHaveLength(1);
      expect(response.body.folderTree[0].children[0].name).toBe('Child');
    });

    it('should include projects when requested', async () => {
      // Create folder
      const folder = new Folder({
        name: 'Test Folder',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      // Create project in folder
      const project = new Project({
        title: 'Test Project',
        description: 'Test Description',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>',
        folderId: folder._id
      });
      await project.save();

      // Create root project
      const rootProject = new Project({
        title: 'Root Project',
        description: 'Root Description',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>',
        folderId: null
      });
      await rootProject.save();

      const response = await request(app)
        .get(`/folders/tree/${userId}?includeProjects=true`)
        .expect(200);

      expect(response.body.folderTree[0].projects).toHaveLength(1);
      expect(response.body.folderTree[0].projects[0].title).toBe('Test Project');
      expect(response.body.rootProjects).toHaveLength(1);
      expect(response.body.rootProjects[0].title).toBe('Root Project');
    });
  });

  describe('GET /folders/:folderId', () => {
    it('should get folder by ID', async () => {
      const folder = new Folder({
        name: 'Test Folder',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      const response = await request(app)
        .get(`/folders/${folder._id}`)
        .expect(200);

      expect(response.body.name).toBe('Test Folder');
      expect(response.body._id).toBe(folder._id.toString());
    });

    it('should handle root folder request', async () => {
      const response = await request(app)
        .get('/folders/root')
        .expect(200);

      expect(response.body.name).toBe('Root');
      expect(response.body._id).toBeNull();
      expect(response.body.path).toBe('/');
    });

    it('should return 404 for non-existent folder', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      
      const response = await request(app)
        .get(`/folders/${nonExistentId}`)
        .expect(404);

      expect(response.body.error).toBe('Folder not found');
    });
  });

  describe('GET /folders/:folderId/contents', () => {
    it('should get folder contents', async () => {
      // Create parent folder
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      // Create subfolder
      const subfolder = new Folder({
        name: 'Subfolder',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await subfolder.save();

      // Create project in parent folder
      const project = new Project({
        title: 'Test Project',
        description: 'Test Description',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>',
        folderId: parent._id
      });
      await project.save();

      const response = await request(app)
        .get(`/folders/${parent._id}/contents?userId=${userId}`)
        .expect(200);

      expect(response.body.subfolders).toHaveLength(1);
      expect(response.body.subfolders[0].name).toBe('Subfolder');
      expect(response.body.projects).toHaveLength(1);
      expect(response.body.projects[0].title).toBe('Test Project');
    });

    it('should get root folder contents', async () => {
      // Create root folder
      const rootFolder = new Folder({
        name: 'Root Folder',
        createdBy: userId,
        contextType: 'user'
      });
      await rootFolder.save();

      // Create root project
      const rootProject = new Project({
        title: 'Root Project',
        description: 'Root Description',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>',
        folderId: null
      });
      await rootProject.save();

      const response = await request(app)
        .get(`/folders/root/contents?userId=${userId}`)
        .expect(200);

      expect(response.body.subfolders).toHaveLength(1);
      expect(response.body.subfolders[0].name).toBe('Root Folder');
      expect(response.body.projects).toHaveLength(1);
      expect(response.body.projects[0].title).toBe('Root Project');
    });

    it('should require userId parameter', async () => {
      const response = await request(app)
        .get('/folders/root/contents')
        .expect(400);

      expect(response.body.error).toBe('userId is required');
    });
  });

  describe('POST /folders', () => {
    it('should create a new folder', async () => {
      const folderData = {
        name: 'New Folder',
        description: 'New Description',
        createdBy: userId
      };

      const response = await request(app)
        .post('/folders')
        .send(folderData)
        .expect(201);

      expect(response.body.name).toBe('New Folder');
      expect(response.body.description).toBe('New Description');
      expect(response.body.path).toBe('/New Folder');
      expect(response.body.level).toBe(0);
    });

    it('should create a nested folder', async () => {
      // Create parent folder
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      const folderData = {
        name: 'Child',
        createdBy: userId,
        parentFolder: parent._id
      };

      const response = await request(app)
        .post('/folders')
        .send(folderData)
        .expect(201);

      expect(response.body.name).toBe('Child');
      expect(response.body.path).toBe('/Parent/Child');
      expect(response.body.level).toBe(1);
    });

    it('should require name and createdBy', async () => {
      const response = await request(app)
        .post('/folders')
        .send({})
        .expect(400);

      expect(response.body.error).toBe('Name and createdBy are required');
    });

    it('should prevent duplicate folder names in same location', async () => {
      // Create first folder
      const folder1 = new Folder({
        name: 'Duplicate Name',
        createdBy: userId,
        contextType: 'user'
      });
      await folder1.save();

      // Try to create second folder with same name
      const folderData = {
        name: 'Duplicate Name',
        createdBy: userId
      };

      const response = await request(app)
        .post('/folders')
        .send(folderData)
        .expect(400);

      expect(response.body.error).toBe('Folder with this name already exists in the same location');
    });

    it('should validate parent folder exists and belongs to user', async () => {
      const otherUserId = new mongoose.Types.ObjectId();
      
      // Create folder for different user
      const otherUserFolder = new Folder({
        name: 'Other User Folder',
        createdBy: otherUserId,
        contextType: 'user'
      });
      await otherUserFolder.save();

      const folderData = {
        name: 'Test',
        createdBy: userId,
        parentFolder: otherUserFolder._id
      };

      const response = await request(app)
        .post('/folders')
        .send(folderData)
        .expect(400);

      expect(response.body.error).toBe('Parent folder not found or access denied');
    });
  });

  describe('PUT /folders/:folderId', () => {
    it('should update folder name and description', async () => {
      const folder = new Folder({
        name: 'Original Name',
        description: 'Original Description',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      const updateData = {
        name: 'Updated Name',
        description: 'Updated Description'
      };

      const response = await request(app)
        .put(`/folders/${folder._id}`)
        .send(updateData)
        .expect(200);

      expect(response.body.name).toBe('Updated Name');
      expect(response.body.description).toBe('Updated Description');
      expect(response.body.path).toBe('/Updated Name');
    });

    it('should prevent updating root folder', async () => {
      const updateData = {
        name: 'New Root Name'
      };

      const response = await request(app)
        .put('/folders/root')
        .send(updateData)
        .expect(400);

      expect(response.body.error).toBe('Cannot update root folder');
    });

    it('should return 404 for non-existent folder', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();

      const response = await request(app)
        .put(`/folders/${nonExistentId}`)
        .send({ name: 'New Name' })
        .expect(404);

      expect(response.body.error).toBe('Folder not found');
    });

    it('should prevent duplicate names in same location', async () => {
      // Create two folders
      const folder1 = new Folder({
        name: 'Folder 1',
        createdBy: userId,
        contextType: 'user'
      });
      await folder1.save();

      const folder2 = new Folder({
        name: 'Folder 2',
        createdBy: userId,
        contextType: 'user'
      });
      await folder2.save();

      // Try to rename folder2 to folder1's name
      const response = await request(app)
        .put(`/folders/${folder2._id}`)
        .send({ name: 'Folder 1' })
        .expect(400);

      expect(response.body.error).toBe('Folder with this name already exists in the same location');
    });
  });

  describe('DELETE /folders/:folderId', () => {
    it('should delete empty folder', async () => {
      const folder = new Folder({
        name: 'To Delete',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      const response = await request(app)
        .delete(`/folders/${folder._id}`)
        .expect(200);

      expect(response.body.message).toBe('Folder deleted successfully');

      // Verify folder is deleted
      const deletedFolder = await Folder.findById(folder._id);
      expect(deletedFolder).toBeNull();
    });

    it('should prevent deleting root folder', async () => {
      const response = await request(app)
        .delete('/folders/root')
        .expect(400);

      expect(response.body.error).toBe('Cannot delete root folder');
    });

    it('should return 404 for non-existent folder', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();

      const response = await request(app)
        .delete(`/folders/${nonExistentId}`)
        .expect(404);

      expect(response.body.error).toBe('Folder not found');
    });

    it('should prevent deleting folder with subfolders', async () => {
      // Create parent folder
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      // Create child folder
      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await child.save();

      const response = await request(app)
        .delete(`/folders/${parent._id}`)
        .expect(400);

      expect(response.body.error).toBe('Cannot delete folder that contains subfolders');
    });

    it('should prevent deleting folder with projects', async () => {
      // Create folder
      const folder = new Folder({
        name: 'With Projects',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      // Create project in folder
      const project = new Project({
        title: 'Test Project',
        description: 'Test Description',
        createdBy: userId,
        name: 'Test User',
        email: '<EMAIL>',
        folderId: folder._id
      });
      await project.save();

      const response = await request(app)
        .delete(`/folders/${folder._id}`)
        .expect(400);

      expect(response.body.error).toBe('Cannot delete folder that contains projects');
    });
  });

  describe('PATCH /folders/:folderId/move', () => {
    it('should move folder to new parent', async () => {
      // Create folders
      const folder1 = new Folder({
        name: 'Folder 1',
        createdBy: userId,
        contextType: 'user'
      });
      await folder1.save();

      const folder2 = new Folder({
        name: 'Folder 2',
        createdBy: userId,
        contextType: 'user'
      });
      await folder2.save();

      // Move folder2 into folder1
      const response = await request(app)
        .patch(`/folders/${folder2._id}/move`)
        .send({ parentFolder: folder1._id })
        .expect(200);

      expect(response.body.parentFolder).toBe(folder1._id.toString());
      expect(response.body.path).toBe('/Folder 1/Folder 2');
      expect(response.body.level).toBe(1);
    });

    it('should move folder to root', async () => {
      // Create parent and child
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await child.save();

      // Move child to root
      const response = await request(app)
        .patch(`/folders/${child._id}/move`)
        .send({ parentFolder: null })
        .expect(200);

      expect(response.body.parentFolder).toBeNull();
      expect(response.body.path).toBe('/Child');
      expect(response.body.level).toBe(0);
    });

    it('should prevent circular references', async () => {
      // Create parent and child
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await child.save();

      // Try to move parent into child (circular reference)
      const response = await request(app)
        .patch(`/folders/${parent._id}/move`)
        .send({ parentFolder: child._id })
        .expect(400);

      expect(response.body.error).toBe('Cannot move folder: would create circular reference');
    });

    it('should return 404 for non-existent folder', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();

      const response = await request(app)
        .patch(`/folders/${nonExistentId}/move`)
        .send({ parentFolder: null })
        .expect(404);

      expect(response.body.error).toBe('Folder not found');
    });
  });
});
