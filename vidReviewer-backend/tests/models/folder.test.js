import { describe, it, expect, beforeEach } from 'vitest';
import mongoose from 'mongoose';
import Folder from '../../models/folder.js';

describe('Folder Model', () => {
  let userId;

  beforeEach(() => {
    userId = new mongoose.Types.ObjectId();
  });

  describe('Schema Validation', () => {
    it('should create a valid user folder', async () => {
      const folderData = {
        name: 'Test Folder',
        description: 'Test Description',
        createdBy: userId,
        contextType: 'user'
      };

      const folder = new Folder(folderData);
      await folder.save();

      expect(folder.name).toBe('Test Folder');
      expect(folder.description).toBe('Test Description');
      expect(folder.createdBy.toString()).toBe(userId.toString());
      expect(folder.contextType).toBe('user');
      expect(folder.contextId).toBeNull();
      expect(folder.path).toBe('/Test Folder');
      expect(folder.level).toBe(0);
    });

    it('should create a valid project folder', async () => {
      const projectId = new mongoose.Types.ObjectId();
      const folderData = {
        name: 'Project Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      };

      const folder = new Folder(folderData);
      await folder.save();

      expect(folder.contextType).toBe('project');
      expect(folder.contextId.toString()).toBe(projectId.toString());
      expect(folder.path).toBe('/Project Folder');
    });

    it('should require name and createdBy', async () => {
      const folder = new Folder({});
      
      await expect(folder.save()).rejects.toThrow();
    });

    it('should validate contextId for project folders', async () => {
      const folder = new Folder({
        name: 'Test',
        createdBy: userId,
        contextType: 'project'
        // Missing contextId
      });

      await expect(folder.save()).rejects.toThrow();
    });

    it('should reject contextId for user folders', async () => {
      const folder = new Folder({
        name: 'Test',
        createdBy: userId,
        contextType: 'user',
        contextId: new mongoose.Types.ObjectId()
      });

      await expect(folder.save()).rejects.toThrow();
    });
  });

  describe('Path and Level Generation', () => {
    it('should generate correct path for root folder', async () => {
      const folder = new Folder({
        name: 'Root Folder',
        createdBy: userId,
        contextType: 'user'
      });

      await folder.save();
      expect(folder.path).toBe('/Root Folder');
      expect(folder.level).toBe(0);
    });

    it('should generate correct path for nested folder', async () => {
      // Create parent folder
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      // Create child folder
      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await child.save();

      expect(child.path).toBe('/Parent/Child');
      expect(child.level).toBe(1);
    });

    it('should generate correct path for deeply nested folder', async () => {
      // Create grandparent
      const grandparent = new Folder({
        name: 'Grandparent',
        createdBy: userId,
        contextType: 'user'
      });
      await grandparent.save();

      // Create parent
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user',
        parentFolder: grandparent._id
      });
      await parent.save();

      // Create child
      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await child.save();

      expect(child.path).toBe('/Grandparent/Parent/Child');
      expect(child.level).toBe(2);
    });

    it('should update path when folder is renamed', async () => {
      const folder = new Folder({
        name: 'Original Name',
        createdBy: userId,
        contextType: 'user'
      });
      await folder.save();

      folder.name = 'New Name';
      await folder.save();

      expect(folder.path).toBe('/New Name');
    });
  });

  describe('Context Validation', () => {
    it('should prevent parent folder from different context', async () => {
      const projectId = new mongoose.Types.ObjectId();
      
      // Create user folder
      const userFolder = new Folder({
        name: 'User Folder',
        createdBy: userId,
        contextType: 'user'
      });
      await userFolder.save();

      // Try to create project folder with user folder as parent
      const projectFolder = new Folder({
        name: 'Project Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId,
        parentFolder: userFolder._id
      });

      await expect(projectFolder.save()).rejects.toThrow('Parent folder must be in the same context');
    });

    it('should allow same context parent folder', async () => {
      const projectId = new mongoose.Types.ObjectId();
      
      // Create project parent folder
      const parentFolder = new Folder({
        name: 'Parent Project Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId
      });
      await parentFolder.save();

      // Create child project folder
      const childFolder = new Folder({
        name: 'Child Project Folder',
        createdBy: userId,
        contextType: 'project',
        contextId: projectId,
        parentFolder: parentFolder._id
      });

      await childFolder.save();
      expect(childFolder.path).toBe('/Parent Project Folder/Child Project Folder');
    });
  });

  describe('Instance Methods', () => {
    let parentFolder, childFolder1, childFolder2, grandchildFolder;

    beforeEach(async () => {
      // Create folder hierarchy
      parentFolder = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parentFolder.save();

      childFolder1 = new Folder({
        name: 'Child1',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parentFolder._id
      });
      await childFolder1.save();

      childFolder2 = new Folder({
        name: 'Child2',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parentFolder._id
      });
      await childFolder2.save();

      grandchildFolder = new Folder({
        name: 'Grandchild',
        createdBy: userId,
        contextType: 'user',
        parentFolder: childFolder1._id
      });
      await grandchildFolder.save();
    });

    it('should get direct children', async () => {
      const children = await parentFolder.getChildren();
      expect(children).toHaveLength(2);
      expect(children.map(c => c.name).sort()).toEqual(['Child1', 'Child2']);
    });

    it('should get all descendants', async () => {
      const descendants = await parentFolder.getDescendants();
      expect(descendants).toHaveLength(3);
      expect(descendants.map(d => d.name).sort()).toEqual(['Child1', 'Child2', 'Grandchild']);
    });
  });

  describe('Static Methods', () => {
    it('should get folder tree for user context', async () => {
      // Create folder structure
      const root1 = new Folder({
        name: 'Root1',
        createdBy: userId,
        contextType: 'user'
      });
      await root1.save();

      const child1 = new Folder({
        name: 'Child1',
        createdBy: userId,
        contextType: 'user',
        parentFolder: root1._id
      });
      await child1.save();

      const tree = await Folder.getFolderTree('user', null, userId);
      expect(tree).toHaveLength(1);
      expect(tree[0].name).toBe('Root1');
      expect(tree[0].children).toHaveLength(1);
      expect(tree[0].children[0].name).toBe('Child1');
    });

    it('should validate folder move operations', async () => {
      // Create parent and child
      const parent = new Folder({
        name: 'Parent',
        createdBy: userId,
        contextType: 'user'
      });
      await parent.save();

      const child = new Folder({
        name: 'Child',
        createdBy: userId,
        contextType: 'user',
        parentFolder: parent._id
      });
      await child.save();

      // Should not allow moving parent into child (circular reference)
      const canMove = await Folder.canMoveFolder(parent._id, child._id);
      expect(canMove).toBe(false);

      // Should allow moving child to root
      const canMoveToRoot = await Folder.canMoveFolder(child._id, null);
      expect(canMoveToRoot).toBe(true);
    });
  });
});
