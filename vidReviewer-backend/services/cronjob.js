import cron from 'node-cron';
import { Storage } from '@google-cloud/storage';
import Video from '../models/video.js';
import User from "../models/user.js";

const storage = new Storage({
  keyFilename: './key.json'
});

const bucketName = 'vid-reviewer-bucket';

// cron.schedule("0 0 * * *", async () => {
//   console.log('Starting archival check...');
//   const bucket = storage.bucket(bucketName);

//   const [files] = await bucket.getFiles({
//     prefix: 'uploads/'
//   });
//   const now = new Date();

//   const daysThreshold = 30;

//   const thresholdDate = new Date(now.setDate(now.getDate() - daysThreshold));

//   for (const file of files) {
//     // Get metadata for the file
//     const [metadata] = await file.getMetadata();
//     const lastAccessTime = new Date(metadata.timeStorageClassUpdated || metadata.updated);
//     const gsutilUrl = `https://storage.googleapis.com/${bucketName}/${file.name}`;
//     // Check if the file hasn't been accessed within threshold
//     if (lastAccessTime < thresholdDate && metadata.storageClass !== 'ARCHIVE') {

//       // Update the storage class to ARCHIVE
//       await file.setStorageClass('ARCHIVE');

//       const video = await Video.findOneAndUpdate({ videoUrl: gsutilUrl }, { status: 'archived', storageClass: 'archived' }, { new: true });

//       const userUpdatePromise = User.findByIdAndUpdate(
//         video.uploadedBy,
//         {
//           $inc: { 'storageType.normal': -video.size, 'storageType.archived': video.size }
//         },
//         { new: true }
//       );


//       console.log(`Successfully archived ${file.name}`);
//     } else {
//       console.log(`Skipping ${file.name}: `);
//     }
//   }
// });

