import dotenv from 'dotenv';
import Video from '../models/video.js';
import { transcodingClient } from './transcodingClient.js';

dotenv.config();

// Legacy transcoding logic moved to Cloud Run service



export const createTranscodingJob = async (gcpFileName, videoId, playerSize = 'small') => {
    try {
        // Fetch original video dimensions from database
        let originalWidth, originalHeight;
        try {
            const videoDoc = await Video.findById(videoId, 'width height').lean();
            originalWidth = videoDoc?.width;
            originalHeight = videoDoc?.height;
            console.log(`Original video dimensions: ${originalWidth}x${originalHeight}`);
        } catch (error) {
            console.warn(`Could not fetch video dimensions for ${videoId}, using preset dimensions:`, error.message);
        }

        // Call the Cloud Run transcoding service
        const response = await transcodingClient.startTranscodingJob(
            gcpFileName,
            videoId,
            playerSize,
            originalWidth,
            originalHeight
        );

        console.log(`Transcoding job started via Cloud Run: ${response.jobId}`);

        // Return a response object that matches the expected format
        return {
            name: response.jobId,
            jobId: response.jobId,
            videoId: response.videoId
        };
    } catch (error) {
        console.error('Error creating transcoding job via Cloud Run:', error);
        throw error;
    }
};

export const getJobDetails = async (jobName) => {
    try {
        const response = await transcodingClient.getJobStatus(jobName);
        return response.status;
    } catch (error) {
        console.error(`Error fetching job details for ${jobName}:`, error);
        throw error;
    }
};