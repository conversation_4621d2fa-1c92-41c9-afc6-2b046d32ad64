import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const TRANSCODING_SERVICE_URL = process.env.TRANSCODING_SERVICE_URL || 'https://transcoding-service-YOUR_HASH-uc.a.run.app';

class TranscodingClient {
    constructor() {
        if (!TRANSCODING_SERVICE_URL) {
            throw new Error('Transcoding service URL not configured');
        }

        this.client = axios.create({
            baseURL: TRANSCODING_SERVICE_URL,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        // Add request interceptor for logging
        this.client.interceptors.request.use(
            (config) => {
                console.log(`Making request to transcoding service: ${config.method?.toUpperCase()} ${config.url}`);
                return config;
            },
            (error) => {
                console.error('Request interceptor error:', error);
                return Promise.reject(error);
            }
        );

        // Add response interceptor for error handling
        this.client.interceptors.response.use(
            (response) => {
                console.log(`Transcoding service response: ${response.status} ${response.statusText}`);
                return response;
            },
            (error) => {
                console.error('Transcoding service error:', {
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    data: error.response?.data,
                    message: error.message
                });
                return Promise.reject(error);
            }
        );
    }

    /**
     * Start a transcoding job
     * @param {string} gcpFileName - The GCP file name
     * @param {string} videoId - The video ID
     * @param {string} playerSize - The player size (small, medium, large)
     * @param {number} originalWidth - Original video width
     * @param {number} originalHeight - Original video height
     * @returns {Promise<Object>} The transcoding job response
     */
    async startTranscodingJob(gcpFileName, videoId, playerSize = 'small', originalWidth, originalHeight) {
        try {
            const response = await this.client.post('/transcode', {
                gcpFileName,
                videoId,
                playerSize,
                originalWidth,
                originalHeight
            });

            return response.data;
        } catch (error) {
            console.error('Error starting transcoding job:', error);
            throw new Error(`Failed to start transcoding job: ${error.response?.data?.message || error.message}`);
        }
    }

    /**
     * Get the status of a transcoding job
     * @param {string} jobId - The job ID
     * @returns {Promise<Object>} The job status
     */
    async getJobStatus(jobId) {
        try {
            const response = await this.client.get(`/status/${encodeURIComponent(jobId)}`);
            return response.data;
        } catch (error) {
            console.error('Error getting job status:', error);
            throw new Error(`Failed to get job status: ${error.response?.data?.message || error.message}`);
        }
    }

    /**
     * Generate a thumbnail for a video
     * @param {string} gcpFileName - The GCP file name
     * @param {string} videoId - The video ID
     * @returns {Promise<Object>} The thumbnail generation response
     */
    async generateThumbnail(gcpFileName, videoId) {
        try {
            const response = await this.client.post('/thumbnail', {
                gcpFileName,
                videoId
            });

            return response.data;
        } catch (error) {
            console.error('Error generating thumbnail:', error);
            throw new Error(`Failed to generate thumbnail: ${error.response?.data?.message || error.message}`);
        }
    }

    /**
     * Check if the transcoding service is healthy
     * @returns {Promise<boolean>} True if healthy, false otherwise
     */
    async healthCheck() {
        try {
            const response = await this.client.get('/health');
            return response.status === 200 && response.data.status === 'healthy';
        } catch (error) {
            console.error('Transcoding service health check failed:', error);
            return false;
        }
    }
}

// Export a singleton instance
export const transcodingClient = new TranscodingClient();

// Also export the class for testing purposes
export { TranscodingClient };
