// services/pubsubConsumer.js
import { PubSub } from '@google-cloud/pubsub';
import Video from '../models/video.js';
import { generateThumbnail } from './thumbnail.js';
import { createTranscodingJob } from './transcoder.js';

// Initialize PubSub
const pubsub = new PubSub({
  projectId: 'vidlead-prod',
  keyFilename: './key.json'
});

const subscriptionName = 'video-uploads-sub';
const subscription = pubsub.subscription(subscriptionName);

// Message handler function
async function handleMessage(message) {
    try {
        const data = JSON.parse(Buffer.from(message.data, 'base64').toString());
        console.log('Received message in subscriber ::: ', data);
        if (data.timeCreated && data.name.startsWith('uploads/')) {
            const gcpFileName = "uploads/" + encodeURIComponent(data.name.split('uploads/')[1]);
            console.log('Processing video::::::::::', gcpFileName);

            let video = null;
            const maxRetries = 5;
            const retryDelay = 10000; // 10 seconds

            for (let i = 0; i < maxRetries; i++) {
                video = await Video.findOne({
                    videoUrl: `https://storage.googleapis.com/vid-reviewer-bucket/${gcpFileName}`
                });

                if (video) {
                    console.log(`Video document found for ${gcpFileName}`);
                    break;
                }

                console.log(`Video document not found for ${gcpFileName}, attempt ${i + 1}/${maxRetries}. Retrying in ${retryDelay / 1000} seconds...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }

            if (!video) {
                console.error(`Failed to find video document for ${gcpFileName} after ${maxRetries} attempts. Acknowledging message to prevent retries.`);
                message.ack();
                return;
            }

            // Idempotency Check: Ensure we only process a video once.
            if (video.status !== 'normal') {
                console.log(`Video ${gcpFileName} is already being processed or is complete (status: ${video.status}). Acknowledging message.`);
                message.ack();
                return;
            }

            try {
                // Set status to processing immediately to act as a lock
                await Video.findByIdAndUpdate(video._id, { status: 'processing' });

                // Trigger transcoding and thumbnail generation in parallel
                await Promise.all([
                    createTranscodingJob(gcpFileName, video._id.toString()),
                    generateThumbnail(gcpFileName, video._id.toString())
                ]);

                console.log('Successfully triggered processing for video:', gcpFileName);
                message.ack();
            } catch (processingError) {
                console.error('Error processing video:', processingError);
                await Video.findByIdAndUpdate(video._id, {
                    status: 'failed',
                    processingError: processingError.message
                });
                message.ack();
            }
        } else {
            console.log('Message is not a video upload notification, acknowledging.');
            message.ack();
        }
    } catch (error) {
        console.error('An unexpected error occurred in handleMessage:', error);
        // We don't ack the message here, so Pub/Sub will try to redeliver it.
    }
}

// Error handler
const errorHandler = function (error) {
    console.error('Subscriber error:', error);
};

// Register message and error handlers
subscription.on('message', handleMessage);
subscription.on('error', errorHandler);


// Start consumer function
function startConsumer () {
  subscription.on('message', handleMessage);
  subscription.on('error', (error => console.error('Subscription error:', error)));
  console.log('PubSub consumer started. Listening for messages...');
}

// Stop consumer function
function stopConsumer () {
  subscription.removeListener('message', handleMessage);
  subscription.removeListener('error', (error) => console.error('Subscription error:', error));
  console.log('PubSub consumer stopped');
}

export { startConsumer, stopConsumer };