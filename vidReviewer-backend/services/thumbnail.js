// thumbnail.js - Updated to use Cloud Run transcoding service
import { transcodingClient } from './transcodingClient.js';
import Video from '../models/video.js';

export const generateThumbnail = async (gcpFileName, videoId, retryCount = 0, maxRetries = 3) => {
    try {
        console.log('Generating thumbnail via Cloud Run for:', gcpFileName);

        // Check if video document exists, retry if not found
        const videoDoc = await Video.findById(videoId, 'height width').lean();
        console.log("Video document:", videoDoc);
        console.log('Retry count:', retryCount);

        // If video document is not found and we haven't exceeded max retries
        if (!videoDoc && retryCount < maxRetries) {
            console.log(`Video document not found, attempt ${retryCount + 1}/${maxRetries}. Retrying in 10 seconds...`);

            // Wait 10 seconds before retrying
            await new Promise(resolve => setTimeout(resolve, 10000));
            return generateThumbnail(gcpFileName, videoId, retryCount + 1, maxRetries);
        }

        if (!videoDoc) {
            throw new Error(`Video document not found after ${maxRetries} attempts`);
        }

        // Call the Cloud Run service to generate thumbnail
        const response = await transcodingClient.generateThumbnail(gcpFileName, videoId);

        console.log('Thumbnail generated via Cloud Run:', response.thumbnailUrl);

        // Update video document with thumbnail URL
        await Video.findByIdAndUpdate(videoId, {
            videoThumbnail: response.thumbnailUrl
        });

        console.log('Video document updated with thumbnail URL');

        return response.thumbnailUrl;

    } catch (error) {
        console.error('Error in thumbnail generation via Cloud Run:', error);
        throw error;
    }
};