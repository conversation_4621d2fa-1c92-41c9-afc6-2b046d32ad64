import axios from 'axios';

const SLACK_WEBHOOK_URL = '*********************************************************************************';

export async function sendSlackNotification(message, options = {}) {
  try {
    const payload = {
      text: message,
      username: options.username || 'VidReviewer Bot',
      icon_emoji: options.icon_emoji || ':video_camera:',
      channel: options.channel || '#general',
      attachments: options.attachments || []
    };

    const response = await axios.post(SLACK_WEBHOOK_URL, payload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Slack notification sent successfully');
    return response.data;
  } catch (error) {
    console.error('Failed to send Slack notification:', error.message);
    throw error;
  }
}

export async function sendGhlTokenRefreshFailure(error, userInfo) {
  const message = `🚨 *GHL Token Refresh Failed*`;
  
  const attachment = {
    color: 'danger',
    fields: [
      {
        title: 'Error',
        value: error.message,
        short: false
      },
      {
        title: 'User Location ID',
        value: userInfo.locationId || 'N/A',
        short: true
      },
      {
        title: 'Company ID',
        value: userInfo.companyId || 'N/A',
        short: true
      },
      {
        title: 'Timestamp',
        value: new Date().toISOString(),
        short: true
      },
      {
        title: 'Action Required',
        value: 'User needs to re-authenticate with GoHighLevel',
        short: false
      }
    ],
    footer: 'VidReviewer Backend',
    ts: Math.floor(Date.now() / 1000)
  };

  return sendSlackNotification(message, {
    attachments: [attachment],
    username: 'GHL Monitor',
    icon_emoji: ':warning:'
  });
}

export async function sendApiErrorNotification(error, endpoint, userInfo) {
  const message = `⚠️ *API Error Occurred*`;
  
  const attachment = {
    color: 'warning',
    fields: [
      {
        title: 'Endpoint',
        value: endpoint,
        short: true
      },
      {
        title: 'Error Type',
        value: error.name || 'Unknown',
        short: true
      },
      {
        title: 'Error Message',
        value: error.message,
        short: false
      },
      {
        title: 'User Location ID',
        value: userInfo?.locationId || 'N/A',
        short: true
      },
      {
        title: 'Timestamp',
        value: new Date().toISOString(),
        short: true
      }
    ],
    footer: 'VidReviewer Backend',
    ts: Math.floor(Date.now() / 1000)
  };

  return sendSlackNotification(message, {
    attachments: [attachment],
    username: 'API Monitor',
    icon_emoji: ':exclamation:'
  });
} 