import Agency from '../models/agency.js';
import User from '../models/user.js';
import { decrypt, encrypt } from '../utils/encryption-utils.js';
import axios from 'axios';
import qs from 'qs';
import { GhlRefreshTokenError } from '../utils/errors.js';
import { sendGhlTokenRefreshFailure } from './slack.js';

const GHL_API_BASE_URL = 'https://services.leadconnectorhq.com';
const tokenCache = new Map();
const refreshing = new Map();

async function refreshAccessToken(tokenData) {
  const isUserToken = !!tokenData.locationId;
  const cacheKey = isUserToken
    ? `user-${tokenData.locationId}`
    : `agency-${tokenData.companyId}`;

  if (tokenCache.has(cacheKey)) {
    const cached = tokenCache.get(cacheKey);
    if (Date.now() - cached.timestamp < 23 * 60 * 60 * 1000) { // 23 hours
      return { accessToken: cached.accessToken, type: cached.type };
    }
  }

  if (refreshing.has(cacheKey)) {
    return refreshing.get(cacheKey);
  }

  const refreshPromise = (async () => {
    try {
      let entity;
      let entityType;

      if (isUserToken) {
        entity = await User.findOne({ companyId: tokenData.companyId, locationId: tokenData.locationId });
        entityType = 'user';
      } else {
        entity = await Agency.findOne({ companyId: tokenData.companyId });
        entityType = 'agency';
      }

      if (!entity) {
        throw new Error(`Could not find ${entityType} to refresh token.`);
      }

      const data = {
        client_id: process.env.CLIENT_ID,
        client_secret: process.env.CLIENT_SECRET,
        grant_type: 'refresh_token',
        refresh_token: decrypt(entity.refreshToken),
      };

      const response = await axios.post(
        `${GHL_API_BASE_URL}/oauth/token`,
        qs.stringify(data),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const { access_token, refresh_token } = response.data;

      entity.accessToken = encrypt(access_token);
      entity.refreshToken = encrypt(refresh_token);
      await entity.save();

      const tokenInfo = { accessToken: access_token, type: entityType, timestamp: Date.now() };
      tokenCache.set(cacheKey, tokenInfo);
      return { accessToken: access_token, type: entityType };

    } finally {
      refreshing.delete(cacheKey);
    }
  })();

  refreshing.set(cacheKey, refreshPromise);
  return refreshPromise;
}

export async function makeGhlApiRequest(path, method = 'GET', body = null, tokenData) {
  let accessToken = decrypt(tokenData.accessToken);

  const makeRequest = async (token) => {
    try {
      const response = await axios({
        method,
        url: `${GHL_API_BASE_URL}${path}`,
        headers: {
          Authorization: `Bearer ${token}`,
          Version: '2021-07-28',
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        data: body,
      });
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 401) {
        return null;
      }
      throw error;
    }
  };

  let result = await makeRequest(accessToken);

  if (result === null) {
    try {
      const { accessToken: newAccessToken } = await refreshAccessToken(tokenData);
      accessToken = newAccessToken;
      result = await makeRequest(accessToken);
      if (result === null) {
        throw new GhlRefreshTokenError('GHL API authentication failed even after token refresh.');
      }
    } catch (refreshError) {
      console.error('Failed to refresh GHL token:', refreshError.response?.data || refreshError.message);
      
      // Send Slack notification about the token refresh failure
      try {
        await sendGhlTokenRefreshFailure(refreshError, {
          locationId: tokenData.locationId,
          companyId: tokenData.companyId
        });
      } catch (slackError) {
        console.error('Failed to send Slack notification:', slackError.message);
      }
      
      throw new GhlRefreshTokenError('Failed to refresh GHL access token. The refresh token may be invalid or expired.');
    }
  }

  return result;
} 