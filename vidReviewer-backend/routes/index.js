import express from 'express';
import authRoute from './auth.js';
import callbackRoute from './callback.js';
import webhookRoute from './webhook.js';
import commentsRoute from './comments.js';
import projectsRoute from './projects.js';
import foldersRoute from './folders.js';
import projectFoldersRoute from './projectFolders.js';
import videosRoute from './videos.js';
import userRoute from './users.js';
import decryptSSORoute from './decrypt-sso.js';
import contactsRoute from './contacts.js';
import notificationsRoute from './notifications.js';
import invitationRoute from './invitation.js';
import refreshRoute from './refresh-token.js';
import refreshAgencyRoute from './refresh-agency.js';
import sharingRoute from './sharing.js';
const router = express.Router();

router.use('/auth', authRoute);

router.use('/oauth/callback', callbackRoute);
router.use('/auth/refresh', refreshRoute);
router.use('/auth/refresh/agency', refreshAgencyRoute);



router.use('/users', userRoute);
router.use('/webhook', webhookRoute);
router.use('/projects', projectsRoute);
router.use('/projects', projectFoldersRoute);
router.use('/folders', foldersRoute);
router.use('/videos', videosRoute);
router.use('/comments', commentsRoute);
router.use('/decrypt-sso', decryptSSORoute);
router.use('/contacts', contactsRoute);
router.use('/notifications', notificationsRoute);
router.use('/invitations', invitationRoute);
router.use('/sharing', sharingRoute);

export default router;
