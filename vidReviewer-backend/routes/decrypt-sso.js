

import express from 'express';
import CryptoJS from 'crypto-js';
import dotenv from 'dotenv';
import User from '../models/user.js';

dotenv.config();
const router = express.Router();

const decryptAndParseData = (encryptedData, key) => {
    try {
        const decryptedData = CryptoJS.AES.decrypt(encryptedData, key).toString(CryptoJS.enc.Utf8);
        return JSON.parse(decryptedData);
    } catch (error) {

        console.error('Decryption failed:', error);
        return null;
    }
};

router.post('/', async (req, res) => {
    const { encryptedData } = req.body; 
    const ssoKey = process.env.GHL_APP_SSO_KEY; 

    if (!encryptedData) {
        return res.status(400).json({ error: 'Encrypted data is required' });
    }

    const decryptedData = decryptAndParseData(encryptedData, ssoKey);

    if (!decryptedData) {
        return res.status(500).json({ error: 'Decryption failed or invalid data' });
    }

    try {

        const updatedUser = await User.findOneAndUpdate(
            { locationId: decryptedData.activeLocation },
            { email: decryptedData.email, userName: decryptedData.userName ?? '' },
            { new: true }
        );

        if (!updatedUser) {
            console.log('No user found with locationId:', decryptedData.activeLocation);
        }

        return res.json(decryptedData);
    } catch (error) {
        console.error('Error updating user email:', error);
        return res.json(decryptedData);
    }
});

export default router;
