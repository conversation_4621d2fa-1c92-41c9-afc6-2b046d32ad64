import express from 'express';
import { getComments, addComment, updateComment, deleteComment, toggleCommentDone } from '../controllers/comments.js';

const router = express.Router();

router.get('/:videoId', getComments);
router.post('/', addComment);
router.put('/:commentId', updateComment);
router.patch('/:commentId/toggle-done', toggleCommentDone);
router.delete('/:commentId', deleteComment);
export default router;
