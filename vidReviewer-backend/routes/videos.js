import express from 'express';
// import multer from 'multer';
import { addVideo, getVideos, getVideo, updateVideo, deleteVideo, getSignedUrl, downloadVideo , getDownloadUrl, archiveVideo, unarchiveVideo, getThumbnailSignedUrl, updateCustomThumbnail, resetToAutoThumbnail, getEffectiveThumbnailUrl, moveVideoToProjectFolder, bulkMoveVideos, bulkDeleteVideos, bulkArchiveVideos} from '../controllers/videos.js';

const router = express.Router();

// const upload = multer({
//     limits: { fileSize: 100 * 1024 * 1024 } 
// });

router.post('/', addVideo);
router.post('/archive', archiveVideo);
router.post('/unarchive', unarchiveVideo);
router.get('/project/:projectId', getVideos);
router.get('/:videoId', getVideo);
router.put('/:videoId', updateVideo);
router.delete('/:videoId', deleteVideo);
router.post('/download', getDownloadUrl);

router.post('/getSignedUrl', getSignedUrl);
router.post('/getThumbnailSignedUrl', getThumbnailSignedUrl);
router.put('/:videoId/thumbnail', updateCustomThumbnail);
router.delete('/:videoId/thumbnail', resetToAutoThumbnail);
router.patch('/:videoId/move-to-folder', moveVideoToProjectFolder);

// Bulk operations
router.post('/bulk-move', bulkMoveVideos);
router.post('/bulk-delete', bulkDeleteVideos);
router.post('/bulk-archive', bulkArchiveVideos);

// router.post('/cronCheck', cronCheck)

export default router;
