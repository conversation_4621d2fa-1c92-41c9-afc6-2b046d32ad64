import express from 'express';
import User from '../models/user.js';
import Video from '../models/video.js';
import sgMail from '@sendgrid/mail';
import axios from 'axios';
import { makeGhlApiRequest } from '../services/ghl.js';
import { GhlRefreshTokenError } from '../utils/errors.js';
import { sendApiErrorNotification } from '../services/slack.js';

import dotenv from 'dotenv';
dotenv.config();

sgMail.setApiKey(process.env.SENDGRID_API_KEY);
const router = express.Router();

router.post('/', async (req, res) => {
    const { locationId, contactId } = req.body;
    const emailTo = req?.body?.emailTo ?? '';

    const { link, type } = req.body;
    let firstName = req?.body?.firstName ?? '';

    firstName = firstName.trim();
    if (firstName && firstName.length > 0) {
        firstName = firstName.charAt(0).toUpperCase() + firstName.slice(1);
    }

    try {
        if (type === 'Email') {
            // Use SendGrid for email notifications
            if (!process.env.SENDGRID_API_KEY) {
                throw new Error('SendGrid API key is not configured');
            }

            const msg = {
                to: emailTo,
                from: {
                    email: '<EMAIL>',
                    name: 'Vidlead',
                },
                subject: "Your Video Project is Ready to Review!",
                html: `<p>Hey ${firstName},</p>
                       <p>Your video project is ready for review… if you could please have a look and comment any feedback or notes we would appreciate it!</p>
                       <p><a href="${link}">Video Link</a></p>
                       <p>Thanks!</p>`,
            };

            await sgMail.send(msg);
            return res.status(200).json({ message: 'Email notification sent successfully' });
        } else {
            // Use GHL for SMS notifications
            const user = await User.findOne({ locationId: locationId });
            if (!user.accessToken) {
                return res.status(400).json({ error: "Access token not found" });
            }

            const body = {
                type: req.body.type,
                contactId: contactId,
                message: `Hey ${firstName} we've finished your video. Please take a look and leave any feedback or notes here: ${link}`,
            };

            const data = await makeGhlApiRequest(
                `/conversations/messages`,
                'POST',
                body,
                {
                    accessToken: user.accessToken,
                    refreshToken: user.refreshToken,
                    companyId: user.companyId,
                    locationId: user.locationId
                }
            );
            res.json(data);
        }
    } catch (error) {
        if (error instanceof GhlRefreshTokenError) {
            await User.updateOne({ locationId: req.body.locationId }, { $set: { accessToken: null, refreshToken: null } });

            // Send Slack notification
            try {
                await sendApiErrorNotification(error, '/conversations/messages', { locationId: req.body.locationId });
            } catch (slackError) {
                console.error('Failed to send Slack notification:', slackError.message);
            }

            return res.status(401).json({
                error: 'Re-authentication required.',
                message: error.message
            });
        }
        console.error('Error sending notification:', error);
        res.status(500).json({ message: error.message });
    }
});

router.post('/notifyCollaborators', async (req, res) => {
    const { videoId, collaboratorName } = req.body;

    
    if (!videoId) {
        return res.status(400).json({ message: 'Video ID is required' });
    }
    const video = await Video.findById(videoId);
    const videoTitle = video?.title ?? 'Video';
    
    const uploadedBy = video.uploadedBy;
    const user = await User.findById(uploadedBy);
    const videoOwnerEmail = user?.email;
    const videoOwnerName = user?.userName ?? 'User';

    const locationId = user?.locationId;
    
    const link = `${process.env.FRONTEND_URL}/location/${locationId}/custom-page-link/${process.env.GHL_ID}`;
    console.log("updated link",link)

    try {
        if (!process.env.SENDGRID_API_KEY) {
            throw new Error('SendGrid API key is not configured');
        }
        const msg = {
            personalizations: [
                {
                    to: [{ email: videoOwnerEmail, name: videoOwnerName }],
                    dynamic_template_data: {
                        collaboratorName: collaboratorName,
                        link: link,
                        videoOwnerName: videoOwnerName,
                        videoTitle: videoTitle,
                    },
                },
            ],
            from: {
                email: '<EMAIL>',
                name: 'Vidlead',
            },
            subject: 'New Collaboration Request',
            template_id: 'd-9b841f318a16415b84eebb995fb32485',
        };
        await axios.post(
            'https://api.sendgrid.com/v3/mail/send',
            msg,
            {
                headers: {
                    Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
                    'Content-Type': 'application/json',
                },
            },
        );
        return res.status(200).json({ message: 'Collaboration request sent successfully' });
    } catch (error) {
        console.error('Error sending collaboration request:', error);
        return res.status(500).json({ message: 'Failed to send collaboration request' });
    }
});

export default router;


