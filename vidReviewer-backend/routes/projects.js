import express from 'express';
import {
    getProjects,
    getProjectById,
    createProject,
    updateProject,
    deleteProject,
    getCollaborators,
    moveProject
} from '../controllers/project.js';

const router = express.Router();

router.get('/:userId', getProjects);
router.get('/projectId/:projectId', getProjectById);
router.get('/collaborators/:projectId', getCollaborators);
router.post('/', createProject);
router.put('/:projectId', updateProject);
router.patch('/:projectId/move', moveProject);
router.delete('/:projectId', deleteProject);

export default router;