import express from 'express';
import User from '../models/user.js';
import Agency from '../models/agency.js';
import {encrypt, decrypt } from '../utils/encryption-utils.js';
import { GhlRefreshTokenError } from '../utils/errors.js';
import { sendApiErrorNotification } from '../services/slack.js';
import dotenv from 'dotenv';
dotenv.config();

const router = express.Router();

router.post('/:locationId', async (req, res) => {
    try {
        const { locationId } = req.params;
        let user = await User.findOne({ locationId });

        // If no user exists, try to create one using agency token
        if (!user) {
            try {
                // Find agency by locationId (we need to get companyId from somewhere)
                // Let's try to find any agency and then get location info
                const agencies = await Agency.find({});
                let locationTokenData = null;
                let matchingAgency = null;

                for (const agency of agencies) {
                    try {
                        const agencyAccessToken = decrypt(agency.access_token);
                        
                        // Try to get location token for this locationId
                        const locationTokenResponse = await fetch('https://services.leadconnectorhq.com/oauth/locationToken', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${agencyAccessToken}`,
                                'Version': '2021-07-28',
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify({ 
                                companyId: agency.companyId, 
                                locationId: locationId 
                            })
                        });

                        const tokenData = await locationTokenResponse.json();
                        
                        if (tokenData.access_token) {
                            locationTokenData = tokenData;
                            matchingAgency = agency;
                            break;
                        }
                    } catch (agencyError) {
                        console.log(`Failed to get location token from agency ${agency.companyId}:`, agencyError.message);
                        continue;
                    }
                }

                if (locationTokenData && matchingAgency) {
                    // Create new user record
                    const newUser = new User({
                        locationId: locationId,
                        companyId: matchingAgency.companyId,
                        userId: locationTokenData.userId || `user-${locationId}`,
                        accessToken: encrypt(locationTokenData.access_token),
                        refreshToken: encrypt(locationTokenData.refresh_token),
                        createdAt: new Date(),
                        updatedAt: Date.now() / 1000,
                        storageType: {
                            normal: 0,
                            archived: 0
                        },
                        storageData: 0
                    });

                    await newUser.save();
                    
                    return res.status(201).json({
                        message: 'User created and location token obtained successfully',
                        locationId: locationId,
                        hasValidToken: true,
                        isNewUser: true
                    });
                } else {
                    return res.status(404).json({ 
                        error: 'Location not found', 
                        message: 'No user found and unable to create user for this location' 
                    });
                }
                
            } catch (createUserError) {
                console.error('Error creating user from agency token:', createUserError);
                return res.status(404).json({ 
                    error: 'User not found', 
                    message: 'No user found and unable to create user for this location' 
                });
            }
        }

        // If user doesn't have a refresh token, try to get location token from agency
        if (!user.refreshToken) {
            try {
                const agency = await Agency.findOne({ companyId: user.companyId });
                if (!agency) {
                    return res.status(400).json({ 
                        error: 'No agency found', 
                        message: 'Cannot refresh token without agency or user refresh token' 
                    });
                }

                const agencyAccessToken = decrypt(agency.access_token);
                
                // Use the location token endpoint as per GHL docs
                const locationTokenResponse = await fetch('https://services.leadconnectorhq.com/oauth/locationToken', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${agencyAccessToken}`,
                        'Version': '2021-07-28',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ 
                        companyId: user.companyId, 
                        locationId: user.locationId 
                    })
                });

                const locationTokenData = await locationTokenResponse.json();
                
                if (locationTokenData.access_token) {
                    const newAccessToken = encrypt(locationTokenData.access_token);
                    const newRefreshToken = encrypt(locationTokenData.refresh_token);
                    
                    await User.updateOne({ locationId }, { 
                        $set: { 
                            accessToken: newAccessToken, 
                            refreshToken: newRefreshToken,
                            updatedAt: Date.now() / 1000 //in seconds
                        } 
                    });
                    
                    return res.status(200).json({
                        message: 'Location token obtained successfully',
                        locationId: locationId,
                        hasValidToken: true
                    });
                } else {
                    throw new Error('Failed to get location token from agency');
                }
                
            } catch (agencyError) {
                console.error('Error getting location token from agency:', agencyError);
                return res.status(400).json({ 
                    error: 'Cannot refresh token', 
                    message: 'User needs to authenticate with GoHighLevel first' 
                });
            }
        }
        
        // If user has refresh token, use it to refresh
        const refreshToken = decrypt(user.refreshToken);

        const url = 'https://services.leadconnectorhq.com/oauth/token';
        const encodedParams = new URLSearchParams();

        encodedParams.append('client_id', process.env.GHL_CLIENT_ID || process.env.CLIENT_ID);
        encodedParams.append('client_secret', process.env.GHL_CLIENT_SECRET || process.env.CLIENT_SECRET);
        encodedParams.append('grant_type', 'refresh_token');
        encodedParams.append('refresh_token', refreshToken);

        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                Accept: 'application/json' 
            },
            body: encodedParams
        };

        const response = await fetch(url, options);
        const data = await response.json();
        
        if (data.error || response.status === 422) {
            // Clear invalid tokens
            await User.updateOne({ locationId }, { $set: { accessToken: null, refreshToken: null } });
            
            // Send Slack notification
            try {
                await sendApiErrorNotification(
                    new GhlRefreshTokenError(data.error_description || data.error || 'Token refresh failed'), 
                    '/oauth/token/refresh', 
                    { locationId }
                );
            } catch (slackError) {
                console.error('Failed to send Slack notification:', slackError.message);
            }
            
            return res.status(401).json({ 
                error: 'Re-authentication required',
                message: 'Refresh token is invalid or expired. Please reconnect your GoHighLevel account.'
            });
        }

        const newRefreshToken = encrypt(data.refresh_token);
        const newAccessToken = encrypt(data.access_token);
        await User.updateOne({ locationId }, { $set: { refreshToken: newRefreshToken, accessToken: newAccessToken, updatedAt: Date.now() / 1000 } });
        
        res.status(200).json({
            message: 'Token refresh successful',
            locationId: locationId,
            hasValidToken: true
        });
        
    } catch (error) {
        console.error('Error refreshing token:', error);
        
        // Send Slack notification for unexpected errors
        try {
            await sendApiErrorNotification(error, '/oauth/token/refresh', { locationId: req.params.locationId });
        } catch (slackError) {
            console.error('Failed to send Slack notification:', slackError.message);
        }
        
        res.status(500).json({ error: 'Failed to refresh token' });
    }
});

export default router;
