import express from 'express';
import { 
    createSharingToken, 
    getSharedResource, 
    getResourceInvitations, 
    revokeSharingInvitation 
} from '../controllers/sharing.js';

const router = express.Router();

// Create sharing token for any resource type
// POST /api/sharing/:resourceType/:resourceId/token
router.post('/:resourceType/:resourceId/token', createSharingToken);

// Get shared resource by token (public route)
// GET /api/sharing/shared/:token
router.get('/shared/:token', getSharedResource);

// Get all invitations for a resource
// GET /api/sharing/:resourceType/:resourceId/invitations
router.get('/:resourceType/:resourceId/invitations', getResourceInvitations);

// Revoke sharing invitation
// DELETE /api/sharing/invitations/:invitationId
router.delete('/invitations/:invitationId', revokeSharingInvitation);

export default router;
