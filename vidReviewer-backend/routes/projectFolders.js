import express from 'express';
import {
    getProjectFolderTree,
    getProjectFolderContents,
    createProjectFolder,
    updateProjectFolder,
    moveProjectFolder,
    deleteProjectFolder,
    bulkMoveProjectFolders,
    bulkDeleteProjectFolders,
    searchProjectContent
} from '../controllers/projectFolder.js';

const router = express.Router();

// Get sub-project tree structure for a project
router.get('/:projectId/folders/tree', getProjectFolderTree);

// Advanced search within a project
router.get('/:projectId/search', searchProjectContent);

// Get sub-project contents within a project (sub-sub-projects and videos)
router.get('/:projectId/folders/:folderId/contents', getProjectFolderContents);

// Get root sub-project contents (for videos/sub-projects not in any sub-project within project)
router.get('/:projectId/folders/root/contents', getProjectFolderContents);

// Create a new sub-project within a project (max 3 allowed)
router.post('/:projectId/folders', createProjectFolder);

// Update sub-project
router.put('/:projectId/folders/:folderId', updateProjectFolder);

// Move sub-project to a different parent within the same project
router.patch('/:projectId/folders/:folderId/move', moveProjectFolder);

// Delete sub-project
router.delete('/:projectId/folders/:folderId', deleteProjectFolder);

// Bulk operations for sub-projects
router.post('/:projectId/folders/bulk-move', bulkMoveProjectFolders);
router.post('/:projectId/folders/bulk-delete', bulkDeleteProjectFolders);

export default router;
