import express from 'express';
import Agency from '../models/agency.js';
import { encrypt, decrypt } from '../utils/encryption-utils.js';
import dotenv from 'dotenv';
dotenv.config();

const router = express.Router();

router.post('/:companyId', async (req, res) => {
    try {
        const { companyId } = req.params;

        const agency = await Agency.findOne({ companyId });

        const refreshToken = decrypt(agency.refresh_token);

        const url = 'https://services.leadconnectorhq.com/oauth/token';
        const encodedParams = new URLSearchParams();

        encodedParams.append('client_id', process.env.CLIENT_ID);
        encodedParams.append('client_secret', process.env.CLIENT_SECRET);
        encodedParams.append('grant_type', 'refresh_token');
        encodedParams.append('refresh_token', refreshToken);
        encodedParams.append('redirect_uri', process.env.REDIRECT_URI);

        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                Accept: 'application/json'
            },
            body: encodedParams
        };
        const response = await fetch(url, options);
        const data = await response.json();


        if (data.error) {
            return res.status(401).json({ error: 'Unauthorized' });
        }

        const newRefreshToken = encrypt(data.refresh_token);
        const newAccessToken = encrypt(data.access_token);
        await Agency.updateOne({ companyId }, { $set: { refresh_token: newRefreshToken, access_token: newAccessToken } });

        res.status(200).json(data);
    } catch (error) {
        console.error('Error refreshing token:', error);
        res.status(500).json({ error: 'Failed to refresh token' });
    }

});

export default router;
