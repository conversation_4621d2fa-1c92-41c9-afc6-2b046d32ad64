import express from 'express';
import {
    getFolders,
    getFolderTree,
    getFolderById,
    getFolderContents,
    createFolder,
    updateFolder,
    moveFolder,
    deleteFolder
} from '../controllers/folder.js';

const router = express.Router();

// Get all folders for a user (flat list)
router.get('/user/:userId', getFolders);

// Get folder tree structure for a user
router.get('/tree/:userId', getFolderTree);

// Get root folder contents (for projects/folders not in any folder)
// NOTE: This must come before /:folderId/contents to avoid "root" being treated as a folder ID
router.get('/root/contents', getFolderContents);

// Get folder by ID
router.get('/:folderId', getFolderById);

// Get folder contents (subfolders and projects)
router.get('/:folderId/contents', getFolderContents);

// Create a new folder
router.post('/', createFolder);

// Update folder
router.put('/:folderId', updateFolder);

// Move folder to a different parent
router.patch('/:folderId/move', moveFolder);

// Delete folder
router.delete('/:folderId', deleteFolder);

export default router;
