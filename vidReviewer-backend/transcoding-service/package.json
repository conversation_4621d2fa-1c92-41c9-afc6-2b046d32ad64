{"name": "transcoding-service", "version": "1.0.0", "description": "Cloud Run service for video transcoding", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node --watch server.js"}, "type": "module", "dependencies": {"@google-cloud/video-transcoder": "^4.1.0", "@google-cloud/storage": "^7.14.0", "@google-cloud/pubsub": "^4.10.0", "express": "^4.21.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "fluent-ffmpeg": "^2.1.3"}, "engines": {"node": ">=20"}}