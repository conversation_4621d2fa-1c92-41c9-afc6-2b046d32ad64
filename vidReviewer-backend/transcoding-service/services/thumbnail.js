import ffmpeg from 'fluent-ffmpeg';
import { promises as fsPromises } from 'fs';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { Storage } from '@google-cloud/storage';
import { pipeline } from 'stream/promises';

// Initialize Storage
const storage = new Storage({
    projectId: process.env.GCP_PROJECT_ID || 'vidlead-prod'
});

const bucketName = process.env.GCS_BUCKET_NAME || 'vid-reviewer-bucket';
const bucket = storage.bucket(bucketName);

export const generateThumbnail = async (gcpFileName, videoId) => {
    const tempDir = os.tmpdir();
    const inputPath = path.join(tempDir, `input-${videoId}.mp4`);
    const outputPath = path.join(tempDir, `thumbnail-${videoId}.jpg`);
    const thumbnailFileName = `thumbnails/${videoId}.jpg`;

    try {
        console.log(`Starting thumbnail generation for video ${videoId}`);

        // Download video file from GCS to local temp directory
        const file = bucket.file(gcpFileName);
        const readStream = file.createReadStream();
        const writeStream = fs.createWriteStream(inputPath);

        await pipeline(readStream, writeStream);
        console.log(`Downloaded video file to ${inputPath}`);

        // Generate thumbnail using ffmpeg
        await new Promise((resolve, reject) => {
            ffmpeg(inputPath)
                .screenshots({
                    timestamps: ['10%'], // Take screenshot at 10% of video duration
                    filename: `thumbnail-${videoId}.jpg`,
                    folder: tempDir,
                    size: '640x360'
                })
                .on('end', () => {
                    console.log('Thumbnail generation completed');
                    resolve();
                })
                .on('error', (err) => {
                    console.error('Error generating thumbnail:', err);
                    reject(err);
                });
        });

        // Upload thumbnail to GCS
        const thumbnailFile = bucket.file(thumbnailFileName);
        const uploadStream = thumbnailFile.createWriteStream({
            metadata: {
                contentType: 'image/jpeg',
            },
        });

        const readThumbnailStream = fs.createReadStream(outputPath);
        await pipeline(readThumbnailStream, uploadStream);

        // Note: Thumbnail is publicly accessible via bucket-level IAM policy
        // (makePublic() not needed with uniform bucket-level access)

        const thumbnailUrl = `https://storage.googleapis.com/${bucketName}/${thumbnailFileName}`;
        console.log(`Thumbnail uploaded successfully: ${thumbnailUrl}`);

        return thumbnailUrl;

    } catch (error) {
        console.error('Error in thumbnail generation:', error);
        throw error;
    } finally {
        // Clean up temporary files
        try {
            await fsPromises.unlink(inputPath).catch(() => {});
            await fsPromises.unlink(outputPath).catch(() => {});
        } catch (cleanupError) {
            console.warn('Error cleaning up temporary files:', cleanupError);
        }
    }
};
