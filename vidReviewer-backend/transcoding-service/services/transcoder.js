import { TranscoderServiceClient } from '@google-cloud/video-transcoder';
import dotenv from 'dotenv';
dotenv.config();
const client    = new TranscoderServiceClient();
const projectId = process.env.GCP_PROJECT_ID;
const location  = process.env.GCP_LOCATION;
const bucket    = process.env.GCS_BUCKET_NAME;
// Fixed 1080p single resolution for everything
const FIXED_1080P = {
  height: 1080,
  width: 1920,
  maxBitrate: 8_000_000
};

// Always return fixed 1080p configuration
function buildABRLadder(srcW, srcH, player) {
  // Always return single 1080p configuration regardless of source or player
  return [FIXED_1080P];
}

function calculateDimensions(originalWidth, originalHeight, targetWidth, targetHeight) {
    if (!originalWidth || !originalHeight) {
        // Fallback to preset dimensions if original dimensions are not available
        return { width: targetWidth, height: targetHeight };
    }

    const originalAspectRatio = originalWidth / originalHeight;
    const targetAspectRatio = targetWidth / targetHeight;

    let finalWidth, finalHeight;

    if (originalAspectRatio > targetAspectRatio) {
        // Original video is wider, fit by width
        finalWidth = Math.min(targetWidth, originalWidth);
        finalHeight = Math.round(finalWidth / originalAspectRatio);
    } else {
        // Original video is taller or same aspect ratio, fit by height
        finalHeight = Math.min(targetHeight, originalHeight);
        finalWidth = Math.round(finalHeight * originalAspectRatio);
    }

    // Ensure dimensions are even numbers (required for some codecs)
    finalWidth = finalWidth % 2 === 0 ? finalWidth : finalWidth - 1;
    finalHeight = finalHeight % 2 === 0 ? finalHeight : finalHeight - 1;

    // Ensure minimum dimensions
    finalWidth = Math.max(finalWidth, 320);
    finalHeight = Math.max(finalHeight, 180);

    return { width: finalWidth, height: finalHeight };
}

export async function createTranscodingJob(
  gcsFile,
  videoId,
  playerSize = 'large',
  origW,
  origH
) {
  const inputUri  = `gs://${bucket}/${gcsFile}`;
  const outputUri = `gs://${bucket}/hls/${videoId}/`;
  // Always use fixed 1080p
  const ladder = buildABRLadder(origW||0, origH||0, playerSize);
  console.log('Using fixed 1080p:', ladder.map(r=>`${r.width}×${r.height}@${(r.maxBitrate/1000000).toFixed(1)}Mbps`).join(', '));
  // build elementary streams
  const elementaryStreams = ladder.map((r) => {
    const { width, height } = calculateDimensions(origW||r.width, origH||r.height, r.width, r.height);
    return {
      key: `video${height}`,
      videoStream: {
        h264: {
          widthPixels:    width,
          heightPixels:   height,
          bitrateBps:     r.maxBitrate,  // Target bitrate for VBR
          rateControlMode:'vbr',         // Two-pass VBR for exact bitrate targeting
          profile:        'high',
          tune:           'film',
          pixelFormat:    'yuv420p',
          vbvSizeBits:    Math.floor(r.maxBitrate * 1.5),
          vbvFullnessBits:Math.floor(r.maxBitrate * 0.9),
          frameRate:      30,
          gopDuration:    { seconds: 2 },
          enableTwoPass:  true,          // Enable two-pass for better quality
          bFrameCount:    3,
          bPyramid:       true,
        }
      }
    };
  });
  // add audio once
  elementaryStreams.push({
    key: 'audio',
    audioStream: {
      codec:           'aac',
      bitrateBps:      128_000,
      channelCount:    2,
      sampleRateHertz: 44_100
    }
  });
  // Create separate mux streams for video and audio (fmp4 requires one stream per mux)
  const muxStreams = [];

  // Add video mux streams
  elementaryStreams
    .filter(s => !!s.videoStream)
    .forEach(s => {
      muxStreams.push({
        key: `${s.key}-mux`,
        container: 'fmp4',
        elementaryStreams: [s.key]
      });
    });

  // Add audio mux stream
  muxStreams.push({
    key: 'audio-mux',
    container: 'fmp4',
    elementaryStreams: ['audio']
  });

  const manifests = [{
    fileName: 'manifest.m3u8',
    type: 'HLS',
    muxStreams: muxStreams.map(m => m.key)
  }];
  // submit
  const [resp] = await client.createJob({
    parent: client.locationPath(projectId, location),
    job: {
      inputUri, outputUri,
      config: { elementaryStreams, muxStreams, manifests,
        pubsubDestination:{ topic:`projects/${projectId}/topics/transcoder-notifications` }
      }
    }
  });
  console.log('Job created:', resp.name);
  return resp;
}

export const getJobStatus = async (jobName) => {
    try {
        const [job] = await client.getJob({ name: jobName });
        return {
            name: job.name,
            state: job.state,
            createTime: job.createTime,
            startTime: job.startTime,
            endTime: job.endTime,
            error: job.error,
            config: job.config
        };
    } catch (error) {
        console.error('Error getting job status:', error);
        throw error;
    }
};
