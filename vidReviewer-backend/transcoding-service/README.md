# Video Transcoding Service

A Cloud Run service for handling video transcoding operations using Google Cloud Video Transcoder API.

## Features

- Video transcoding to HLS format with multiple quality streams
- Dynamic dimension calculation based on original video dimensions
- Thumbnail generation using FFmpeg
- RESTful API endpoints
- Health check endpoint
- Automatic scaling with Cloud Run

## API Endpoints

### POST /transcode
Start a transcoding job for a video.

**Request Body:**
```json
{
  "gcpFileName": "uploads/video.mp4",
  "videoId": "64f1a2b3c4d5e6f7g8h9i0j1",
  "playerSize": "small",
  "originalWidth": 1920,
  "originalHeight": 1080
}
```

**Response:**
```json
{
  "success": true,
  "jobId": "projects/vidlead-prod/locations/us-central1/jobs/12345",
  "videoId": "64f1a2b3c4d5e6f7g8h9i0j1",
  "message": "Transcoding job started successfully"
}
```

### GET /status/:jobId
Get the status of a transcoding job.

**Response:**
```json
{
  "success": true,
  "jobId": "projects/vidlead-prod/locations/us-central1/jobs/12345",
  "status": {
    "name": "projects/vidlead-prod/locations/us-central1/jobs/12345",
    "state": "SUCCEEDED",
    "createTime": "2024-01-01T00:00:00Z",
    "startTime": "2024-01-01T00:00:01Z",
    "endTime": "2024-01-01T00:05:00Z"
  }
}
```

### POST /thumbnail
Generate a thumbnail for a video.

**Request Body:**
```json
{
  "gcpFileName": "uploads/video.mp4",
  "videoId": "64f1a2b3c4d5e6f7g8h9i0j1"
}
```

**Response:**
```json
{
  "success": true,
  "videoId": "64f1a2b3c4d5e6f7g8h9i0j1",
  "thumbnailUrl": "https://storage.googleapis.com/vid-reviewer-bucket/thumbnails/video.jpg",
  "message": "Thumbnail generated successfully"
}
```

### GET /health
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "service": "transcoding-service"
}
```

## Environment Variables

- `GCP_PROJECT_ID`: Google Cloud Project ID (default: vidlead-prod)
- `GCP_LOCATION`: Google Cloud region (default: us-central1)
- `GCS_BUCKET_NAME`: Google Cloud Storage bucket name (default: vid-reviewer-bucket)
- `PORT`: Port to run the service on (default: 8080)

## Deployment

### Prerequisites

1. Google Cloud SDK installed and configured
2. Docker installed
3. Appropriate IAM permissions for Cloud Run, Video Transcoder API, and Cloud Storage

### Deploy to Cloud Run

1. Make the deploy script executable:
```bash
chmod +x deploy.sh
```

2. Run the deployment script:
```bash
./deploy.sh
```

Or deploy manually:

```bash
# Build and push the Docker image
docker build -t gcr.io/vidlead-prod/transcoding-service .
docker push gcr.io/vidlead-prod/transcoding-service

# Deploy to Cloud Run
gcloud run deploy transcoding-service \
  --image gcr.io/vidlead-prod/transcoding-service \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --concurrency 10 \
  --max-instances 100 \
  --set-env-vars "GCP_PROJECT_ID=vidlead-prod,GCP_LOCATION=us-central1,GCS_BUCKET_NAME=vid-reviewer-bucket" \
  --service-account "<EMAIL>"
```

### Using Cloud Build

You can also use Cloud Build for automated deployments:

```bash
gcloud builds submit --config cloudbuild.yaml
```

## Service Account Permissions

The service account needs the following IAM roles:

- `roles/transcoder.admin` - For creating and managing transcoding jobs
- `roles/storage.objectAdmin` - For reading/writing to Cloud Storage
- `roles/pubsub.publisher` - For publishing transcoding completion notifications

## Configuration

### Video Quality Presets

The service supports three player sizes with dynamic dimension calculation:

- **Small**: Base 640x360, 800kbps
- **Medium**: Base 854x480, 1.5Mbps  
- **Large**: Base 1280x720, 3Mbps

Dimensions are automatically calculated based on the original video dimensions while maintaining aspect ratio.

### Audio Configuration

- Codec: AAC
- Bitrate: 128kbps
- Sample Rate: 44.1kHz
- Channels: 2 (stereo)

## Monitoring

The service includes:

- Health check endpoint at `/health`
- Structured logging for all operations
- Error handling with appropriate HTTP status codes
- Request/response logging for debugging

## Scaling

Cloud Run automatically scales the service based on incoming requests:

- **Concurrency**: 10 requests per instance
- **Max Instances**: 100
- **Memory**: 2GB per instance
- **CPU**: 2 vCPUs per instance
- **Timeout**: 1 hour (3600 seconds)

## Error Handling

The service includes comprehensive error handling:

- Input validation for all endpoints
- Graceful error responses with appropriate HTTP status codes
- Automatic cleanup of temporary files
- Retry logic for transient failures
