import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createTranscodingJob, getJobStatus } from './services/transcoder.js';
import { generateThumbnail } from './services/thumbnail.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 8080;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({ status: 'healthy', service: 'transcoding-service' });
});

// Root endpoint
app.get('/', (req, res) => {
    res.json({ 
        service: 'Video Transcoding Service',
        version: '1.0.0',
        endpoints: [
            'POST /transcode - Start transcoding job',
            'GET /status/:jobId - Get job status',
            'POST /thumbnail - Generate thumbnail'
        ]
    });
});

// Start transcoding job
app.post('/transcode', async (req, res) => {
    try {
        const { gcpFileName, videoId, playerSize = 'small', originalWidth, originalHeight } = req.body;

        if (!gcpFileName || !videoId) {
            return res.status(400).json({
                error: 'Missing required parameters',
                required: ['gcpFileName', 'videoId']
            });
        }

        console.log(`Starting transcoding job for video ${videoId}`);
        
        const job = await createTranscodingJob(gcpFileName, videoId, playerSize, originalWidth, originalHeight);
        
        res.status(200).json({
            success: true,
            jobId: job.name,
            videoId,
            message: 'Transcoding job started successfully'
        });

    } catch (error) {
        console.error('Error starting transcoding job:', error);
        res.status(500).json({
            error: 'Failed to start transcoding job',
            message: error.message
        });
    }
});

// Get job status
app.get('/status/:jobId', async (req, res) => {
    try {
        const { jobId } = req.params;
        
        const status = await getJobStatus(jobId);
        
        res.status(200).json({
            success: true,
            jobId,
            status
        });

    } catch (error) {
        console.error('Error getting job status:', error);
        res.status(500).json({
            error: 'Failed to get job status',
            message: error.message
        });
    }
});

// Generate thumbnail
app.post('/thumbnail', async (req, res) => {
    try {
        const { gcpFileName, videoId } = req.body;

        if (!gcpFileName || !videoId) {
            return res.status(400).json({
                error: 'Missing required parameters',
                required: ['gcpFileName', 'videoId']
            });
        }

        console.log(`Generating thumbnail for video ${videoId}`);
        
        const thumbnailUrl = await generateThumbnail(gcpFileName, videoId);
        
        res.status(200).json({
            success: true,
            videoId,
            thumbnailUrl,
            message: 'Thumbnail generated successfully'
        });

    } catch (error) {
        console.error('Error generating thumbnail:', error);
        res.status(500).json({
            error: 'Failed to generate thumbnail',
            message: error.message
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        message: err.message
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Endpoint not found',
        path: req.originalUrl
    });
});

// Graceful shutdown
const shutdown = () => {
    console.log('Shutting down transcoding service...');
    process.exit(0);
};

process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);

app.listen(PORT, () => {
    console.log(`Transcoding service listening on port ${PORT}`);
});
