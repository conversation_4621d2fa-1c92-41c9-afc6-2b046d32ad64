#!/bin/bash

# Set project ID
PROJECT_ID="vidlead-prod"
SERVICE_NAME="transcoding-service"
REGION="us-central1"
REPOSITORY="transcoding-repo"
IMAGE_URL="$REGION-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$SERVICE_NAME"

echo "Building and deploying transcoding service to Cloud Run..."

# Build the Docker image
echo "Building Docker image for linux/amd64..."
docker build --platform linux/amd64 -t $IMAGE_URL .

# Push to Artifact Registry
echo "Pushing image to Artifact Registry..."
docker push $IMAGE_URL

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
  --image $IMAGE_URL \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --concurrency 10 \
  --max-instances 100 \
  --set-env-vars "GCP_PROJECT_ID=$PROJECT_ID,GCP_LOCATION=$REGION,GCS_BUCKET_NAME=vid-reviewer-bucket" \
  --service-account "<EMAIL>"

echo "Deployment complete!"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform managed --region $REGION --format 'value(status.url)')
echo "Service URL: $SERVICE_URL"
