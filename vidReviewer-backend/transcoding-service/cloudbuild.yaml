steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/transcoding-service:$COMMIT_SHA', '.']
    dir: 'transcoding-service'

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/transcoding-service:$COMMIT_SHA']

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'transcoding-service'
      - '--image'
      - 'gcr.io/$PROJECT_ID/transcoding-service:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--memory'
      - '2Gi'
      - '--cpu'
      - '2'
      - '--timeout'
      - '3600'
      - '--concurrency'
      - '10'
      - '--max-instances'
      - '100'
      - '--set-env-vars'
      - 'GCP_PROJECT_ID=$PROJECT_ID,GCP_LOCATION=us-central1,GCS_BUCKET_NAME=vid-reviewer-bucket'
      - '--service-account'
      - '<EMAIL>'

images:
  - 'gcr.io/$PROJECT_ID/transcoding-service:$COMMIT_SHA'

# gcloud builds submit --config cloudbuild.yaml