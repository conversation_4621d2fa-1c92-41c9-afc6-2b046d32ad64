#!/usr/bin/env node

/**
 * Test script for the Cloud Run transcoding service
 * Usage: node test-service.js [service-url]
 */

import axios from 'axios';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuration
const SERVICE_URL = process.argv[2] || 'http://localhost:8080';
const TEST_VIDEO_FILE = 'uploads/test-video.mp4';
const TEST_VIDEO_ID = '64f1a2b3c4d5e6f7g8h9i0j1';

console.log(`Testing transcoding service at: ${SERVICE_URL}`);

// Test data
const testTranscodeRequest = {
    gcpFileName: TEST_VIDEO_FILE,
    videoId: TEST_VIDEO_ID,
    playerSize: 'small',
    originalWidth: 1920,
    originalHeight: 1080
};

const testThumbnailRequest = {
    gcpFileName: TEST_VIDEO_FILE,
    videoId: TEST_VIDEO_ID
};

async function testHealthCheck() {
    console.log('\n🔍 Testing health check...');
    try {
        const response = await axios.get(`${SERVICE_URL}/health`);
        console.log('✅ Health check passed:', response.data);
        return true;
    } catch (error) {
        console.error('❌ Health check failed:', error.message);
        return false;
    }
}

async function testRootEndpoint() {
    console.log('\n🔍 Testing root endpoint...');
    try {
        const response = await axios.get(`${SERVICE_URL}/`);
        console.log('✅ Root endpoint passed:', response.data);
        return true;
    } catch (error) {
        console.error('❌ Root endpoint failed:', error.message);
        return false;
    }
}

async function testTranscodeEndpoint() {
    console.log('\n🔍 Testing transcode endpoint...');
    try {
        const response = await axios.post(`${SERVICE_URL}/transcode`, testTranscodeRequest);
        console.log('✅ Transcode endpoint passed:', response.data);
        
        // Return job ID for status testing
        return response.data.jobId;
    } catch (error) {
        console.error('❌ Transcode endpoint failed:', error.response?.data || error.message);
        return null;
    }
}

async function testStatusEndpoint(jobId) {
    if (!jobId) {
        console.log('\n⏭️  Skipping status test (no job ID)');
        return false;
    }

    console.log('\n🔍 Testing status endpoint...');
    try {
        const encodedJobId = encodeURIComponent(jobId);
        const response = await axios.get(`${SERVICE_URL}/status/${encodedJobId}`);
        console.log('✅ Status endpoint passed:', response.data);
        return true;
    } catch (error) {
        console.error('❌ Status endpoint failed:', error.response?.data || error.message);
        return false;
    }
}

async function testThumbnailEndpoint() {
    console.log('\n🔍 Testing thumbnail endpoint...');
    try {
        const response = await axios.post(`${SERVICE_URL}/thumbnail`, testThumbnailRequest);
        console.log('✅ Thumbnail endpoint passed:', response.data);
        return true;
    } catch (error) {
        console.error('❌ Thumbnail endpoint failed:', error.response?.data || error.message);
        return false;
    }
}

async function testInvalidRequests() {
    console.log('\n🔍 Testing error handling...');
    
    let errorTestsPassed = 0;
    const totalErrorTests = 3;

    // Test missing parameters
    try {
        await axios.post(`${SERVICE_URL}/transcode`, {});
        console.error('❌ Should have failed with missing parameters');
    } catch (error) {
        if (error.response?.status === 400) {
            console.log('✅ Correctly rejected request with missing parameters');
            errorTestsPassed++;
        } else {
            console.error('❌ Wrong error status for missing parameters:', error.response?.status);
        }
    }

    // Test invalid job ID
    try {
        await axios.get(`${SERVICE_URL}/status/invalid-job-id`);
        console.error('❌ Should have failed with invalid job ID');
    } catch (error) {
        if (error.response?.status >= 400) {
            console.log('✅ Correctly handled invalid job ID');
            errorTestsPassed++;
        } else {
            console.error('❌ Wrong error handling for invalid job ID');
        }
    }

    // Test non-existent endpoint
    try {
        await axios.get(`${SERVICE_URL}/non-existent`);
        console.error('❌ Should have failed with 404');
    } catch (error) {
        if (error.response?.status === 404) {
            console.log('✅ Correctly returned 404 for non-existent endpoint');
            errorTestsPassed++;
        } else {
            console.error('❌ Wrong status for non-existent endpoint:', error.response?.status);
        }
    }

    return errorTestsPassed === totalErrorTests;
}

async function runAllTests() {
    console.log('🚀 Starting Cloud Run Transcoding Service Tests');
    console.log('=' .repeat(50));

    const results = {
        healthCheck: false,
        rootEndpoint: false,
        transcodeEndpoint: false,
        statusEndpoint: false,
        thumbnailEndpoint: false,
        errorHandling: false
    };

    // Run tests
    results.healthCheck = await testHealthCheck();
    results.rootEndpoint = await testRootEndpoint();
    
    const jobId = await testTranscodeEndpoint();
    results.transcodeEndpoint = !!jobId;
    
    results.statusEndpoint = await testStatusEndpoint(jobId);
    results.thumbnailEndpoint = await testThumbnailEndpoint();
    results.errorHandling = await testInvalidRequests();

    // Summary
    console.log('\n' + '=' .repeat(50));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('=' .repeat(50));

    const testNames = {
        healthCheck: 'Health Check',
        rootEndpoint: 'Root Endpoint',
        transcodeEndpoint: 'Transcode Endpoint',
        statusEndpoint: 'Status Endpoint',
        thumbnailEndpoint: 'Thumbnail Endpoint',
        errorHandling: 'Error Handling'
    };

    let passedTests = 0;
    const totalTests = Object.keys(results).length;

    for (const [key, passed] of Object.entries(results)) {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${testNames[key]}`);
        if (passed) passedTests++;
    }

    console.log('\n' + '-'.repeat(50));
    console.log(`📈 Overall: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Service is ready for deployment.');
        process.exit(0);
    } else {
        console.log('⚠️  Some tests failed. Please check the service configuration.');
        process.exit(1);
    }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Run tests
runAllTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
});
