import express from 'express';
import dotenv from 'dotenv';
import routes from './routes/index.js';
import cors from 'cors';
import { connectToDB,closeDatabaseConnections } from './config/db.js';
import { startConsumer, stopConsumer } from './services/messageHandler.js';
import './services/cronjob.js';

dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
    console.log('Health check');
    res.send('OK');
});

app.get('/', (req, res) => {
    res.send('Hello World');
});

app.use('/', routes);

const shutdown = async () => {
    console.log('Shutting down gracefully...');
    closeDatabaseConnections();
    stopConsumer();
    process.exit(0);
};

process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);

(async () => {
    try {
        const PORT = process.env.PORT || 3000;
        
        // Try to connect to database, but don't fail if it's not available
        try {
            await connectToDB();
            console.log('Database connected successfully');
        } catch (dbError) {
            console.error('Database connection failed:', dbError.message);
            console.log('Server will start without database connection. Some features may not work properly.');
        }
        
        // Start message consumer
        try {
            startConsumer();
        } catch (consumerError) {
            console.error('Failed to start message consumer:', consumerError.message);
        }

        app.listen(PORT, () => {
            console.log(`App listening on port ${PORT}!`);
        });
    } catch (error) {
        console.error('Failed to start server:', error.message);
        process.exit(1);
    }
})();
