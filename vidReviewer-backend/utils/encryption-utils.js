import {
    createCipheriv,
    createDecipheriv
} from 'crypto';
import dotenv from 'dotenv';
dotenv.config();

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY;
const ENCRYPTION_IV = process.env.ENCRYPTION_IV;

const encrypt = function (token) {
    const algorithm = 'aes-256-cbc';
    const cipher = createCipheriv(algorithm, ENCRYPTION_KEY, ENCRYPTION_IV);

    let encrypted = cipher.update(token, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return encrypted;
};

const decrypt = function (token) {
    const algorithm = 'aes-256-cbc';
    const decipher = createDecipheriv(algorithm, ENCRYPTION_KEY, ENCRYPTION_IV);

    let decrypted = decipher.update(token, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
};

export {
    encrypt ,
    decrypt
};