{"name": "vidReviewer-backend", "version": "1.0.0", "main": "index.js", "directories": {"lib": "lib"}, "scripts": {"test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "start": "node server.js", "dev": "node --watch server.js", "migrate": "node scripts/run-migrations.js", "migrate:dry-run": "node scripts/run-migrations.js --dry-run", "migrate:status": "node scripts/check-migration-status.js", "migrate:schema": "node scripts/001-add-folder-support.js", "migrate:data": "node scripts/002-migrate-project-data.js", "migrate:indexes": "node scripts/003-optimize-folder-indexes.js", "sub-projects:validate": "node scripts/validate-sub-projects.js", "sub-projects:migrate": "node scripts/migrate-excess-sub-projects.js", "sub-projects:health-check": "node scripts/sub-project-health-check.js", "sub-projects:test": "node scripts/test-sub-projects.js", "sub-projects:deploy": "node scripts/deploy-sub-projects.js", "sub-projects:cleanup": "node scripts/cleanup-sub-project-issues.js"}, "author": "", "license": "ISC", "description": "", "type": "module", "dependencies": {"@google-cloud/pubsub": "^4.10.0", "@google-cloud/storage": "^7.14.0", "@google-cloud/video-transcoder": "^4.1.0", "@sendgrid/mail": "^8.1.4", "@slack/web-api": "^7.9.3", "axios": "^1.9.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "express": "^4.21.2", "fluent-ffmpeg": "^2.1.3", "jsonwebtoken": "^9.0.2", "mongodb": "^6.12.0", "mongoose": "^8.9.2", "node-cron": "^3.0.3"}}