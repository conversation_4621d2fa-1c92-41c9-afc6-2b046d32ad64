import Video from '../models/video.js';
import User from "../models/user.js"
import { Storage } from '@google-cloud/storage';
import dotenv from 'dotenv';
import Project from '../models/project.js';
import Folder from '../models/folder.js';
import { createTranscodingJob } from '../services/transcoder.js';
import { generateThumbnail } from '../services/thumbnail.js';
import ffmpeg from 'fluent-ffmpeg';

dotenv.config();


const storage = new Storage({
    keyFilename: './key.json'
});

const bucketName = 'vid-reviewer-bucket';

const bucket = storage.bucket(bucketName);

// Helper function to get the effective thumbnail URL (custom or auto-generated)
export const getEffectiveThumbnailUrl = (video) => {
    if (video.thumbnailType === 'custom' && video.customThumbnail) {
        return video.customThumbnail;
    }
    return video.videoThumbnail;
};

// Helper function to extract video duration from GCS file
const extractVideoDuration = async (gcpFileName) => {
    try {
        const file = bucket.file(gcpFileName);
        const [exists] = await file.exists();

        if (!exists) {
            console.log('File does not exist for duration extraction:', gcpFileName);
            return 0;
        }

        // Generate a temporary signed URL for ffmpeg to access
        const [signedUrl] = await file.getSignedUrl({
            version: 'v4',
            action: 'read',
            expires: Date.now() + 10 * 60 * 1000, // 10 minutes
        });

        return new Promise((resolve) => {
            ffmpeg.ffprobe(signedUrl, (err, metadata) => {
                if (err) {
                    console.error('Error extracting video duration:', err);
                    resolve(0);
                    return;
                }

                const duration = metadata?.format?.duration || 0;
                resolve(Math.round(duration)); // Return duration in seconds
            });
        });
    } catch (error) {
        console.error('Error in extractVideoDuration:', error);
        return 0;
    }
};

// Move video to a different project folder
export const moveVideoToProjectFolder = async (req, res) => {
    try {
        const { videoId } = req.params;
        const { projectFolderId } = req.body;

        const video = await Video.findById(videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }

        // Validate project folder if provided
        if (projectFolderId) {
            const folder = await Folder.findOne({
                _id: projectFolderId,
                contextType: 'project',
                contextId: video.projectId
            });
            if (!folder) {
                return res.status(400).json({ error: 'Project folder not found' });
            }
        }

        video.projectFolderId = projectFolderId || null;
        await video.save();

        res.json(video);
    } catch (error) {
        console.error('Move video to project folder error:', error);
        res.status(500).json({ error: 'Failed to move video to project folder' });
    }
};

// Bulk move videos to a project folder
export const bulkMoveVideos = async (req, res) => {
    try {
        const { videoIds, projectFolderId } = req.body;

        if (!Array.isArray(videoIds) || videoIds.length === 0) {
            return res.status(400).json({ error: 'Video IDs array is required' });
        }

        // Validate project folder if provided
        if (projectFolderId) {
            const folder = await Folder.findById(projectFolderId);
            if (!folder || folder.contextType !== 'project') {
                return res.status(400).json({ error: 'Project folder not found' });
            }
        }

        const result = await Video.updateMany(
            { _id: { $in: videoIds } },
            { projectFolderId: projectFolderId || null }
        );

        res.json({
            message: `Successfully moved ${result.modifiedCount} videos`,
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        console.error('Bulk move videos error:', error);
        res.status(500).json({ error: 'Failed to bulk move videos' });
    }
};

// Bulk delete videos
export const bulkDeleteVideos = async (req, res) => {
    try {
        const { videoIds } = req.body;

        if (!Array.isArray(videoIds) || videoIds.length === 0) {
            return res.status(400).json({ error: 'Video IDs array is required' });
        }

        // Get videos to delete for cleanup (for future file cleanup)
        // const videosToDelete = await Video.find({ _id: { $in: videoIds } });

        // Delete videos from database
        const result = await Video.deleteMany({ _id: { $in: videoIds } });

        // TODO: Add cleanup for GCS files and thumbnails
        // for (const video of videosToDelete) {
        //     await deleteVideoFiles(video);
        // }

        res.json({
            message: `Successfully deleted ${result.deletedCount} videos`,
            deletedCount: result.deletedCount
        });
    } catch (error) {
        console.error('Bulk delete videos error:', error);
        res.status(500).json({ error: 'Failed to bulk delete videos' });
    }
};

// Bulk archive videos
export const bulkArchiveVideos = async (req, res) => {
    try {
        const { videoIds } = req.body;

        if (!Array.isArray(videoIds) || videoIds.length === 0) {
            return res.status(400).json({ error: 'Video IDs array is required' });
        }

        const result = await Video.updateMany(
            { _id: { $in: videoIds } },
            {
                status: 'archived',
                storageClass: 'archived'
            }
        );

        res.json({
            message: `Successfully archived ${result.modifiedCount} videos`,
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        console.error('Bulk archive videos error:', error);
        res.status(500).json({ error: 'Failed to bulk archive videos' });
    }
};

export const getSignedUrl = async (req, res) => {
    try {
        let { fileName, fileType } = req.body;
        console.log("fileName in signed url", fileName)
        console.log("fileType in signed url", fileType)
        fileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '-');
        // Generate unique filename
        let gcpFileName = `uploads/${Date.now()}-${fileName}`;
        console.log("encoded name in signed url", gcpFileName)
        const file = bucket.file(gcpFileName);

        const options = {
            version: 'v4',
            action: 'write',
            expires: Date.now() + 30 * 60 * 1000, // 30 minutes
            contentType: fileType,
        };

        const [signedUrl] = await file.getSignedUrl(options);

        res.json({
            url: signedUrl,
            fileName: gcpFileName
        });
    } catch (error) {
        console.error('Error generating signed URL:', error);
        res.status(500).json({ error: error.message });
    }
};

export const getThumbnailSignedUrl = async (req, res) => {
    try {
        let { fileName, fileType, videoId } = req.body;

        // Validate input
        if (!fileName || !fileType || !videoId) {
            return res.status(400).json({ error: 'fileName, fileType, and videoId are required' });
        }

        // Validate file type (only allow image types)
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(fileType.toLowerCase())) {
            return res.status(400).json({ error: 'Only image files (JPEG, PNG, WebP) are allowed for thumbnails' });
        }

        // Verify video exists
        const video = await Video.findById(videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }

        // Clean filename and generate unique path
        fileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '-');
        const fileExtension = fileName.split('.').pop();
        const gcpFileName = `custom-thumbnails/${videoId}/${Date.now()}-${fileName}`;

        console.log("Generating signed URL for custom thumbnail:", gcpFileName);

        const file = bucket.file(gcpFileName);
        const options = {
            version: 'v4',
            action: 'write',
            expires: Date.now() + 30 * 60 * 1000, // 30 minutes
            contentType: fileType,
        };

        const [signedUrl] = await file.getSignedUrl(options);

        res.json({
            url: signedUrl,
            fileName: gcpFileName,
            videoId: videoId
        });
    } catch (error) {
        console.error('Error generating thumbnail signed URL:', error);
        res.status(500).json({ error: error.message });
    }
};

export const getVideos = async (req, res) => {
    try {
        const { projectId } = req.params;
        const { projectFolderId } = req.query;

        // Build query based on folder filter
        const query = {
            projectId,
            status: { $ne: 'deleted' }
        };

        if (projectFolderId !== undefined) {
            // If projectFolderId is provided, filter by that folder (null for root)
            query.projectFolderId = projectFolderId === 'null' ? null : projectFolderId;
        }

        const videos = await Video.find(query).sort({ uploadedOn: -1 });
        res.json(videos);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch videos' });
    }
};

export const getVideo = async (req, res) => {
    try {
        const video = await Video.findById(req.params.videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }
        res.json(video);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch video' });
    }
};

const STORAGE_LIMIT = 107374182400

export const addVideo = async (req, res) => {

    try {
        let { title, description = '', projectId, uploadedBy, gcpFileName, isDownloadable = false, isCommentsEnabled = true, height, width, size, projectFolderId } = req.body;
        console.log("GCP FILE NAME IN UPLOAD VIDEO :::::::", gcpFileName)
        if (!gcpFileName) {
            return res.status(400).json({ error: 'No file name provided' });
        }
        // gcpFileName = "uploads/" + encodeURIComponent(gcpFileName.split("uploads/")[1]);
        console.log("encoded file name in uplaod video ::::::::", gcpFileName);
        const user = await User.findById(uploadedBy);

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Validate project folder if provided
        if (projectFolderId) {
            const folder = await Folder.findOne({
                _id: projectFolderId,
                contextType: 'project',
                contextId: projectId
            });
            if (!folder) {
                return res.status(400).json({ error: 'Project folder not found' });
            }
        }
        if (user.storageType?.normal + user.storageType?.archived + size > STORAGE_LIMIT) {
            return res.status(402).json({
                error: "Storage limit exceeded",
                message: "Your storage limit of 100GB has been exceeded. Please upgrade your plan or free up some space."
            });
        }

        const publicUrl = `https://storage.googleapis.com/${bucketName}/${gcpFileName}`;
        console.log("final publicUrl ::::::: ", publicUrl);

        // Extract video duration
        const duration = await extractVideoDuration(gcpFileName);
        console.log("Extracted video duration:", duration, "seconds");

        const newVideo = new Video({
            title,
            description,
            projectId,
            projectFolderId: projectFolderId || null,
            videoUrl: publicUrl,
            uploadedBy,
            isDownloadable,
            isCommentsEnabled,
            height,
            width,
            size,
            duration,
            storageClass: 'normal',
            status: 'transcoding',
        });

        await newVideo.save();
        
        // Start transcoding job and thumbnail generation in parallel
        // Only generate auto thumbnail if no custom thumbnail is set
        try {
            const promises = [createTranscodingJob(gcpFileName, newVideo._id.toString())];

            // Only generate auto thumbnail if thumbnailType is 'auto' (default)
            if (newVideo.thumbnailType === 'auto') {
                promises.push(generateThumbnail(gcpFileName, newVideo._id.toString()));
            }

            const [transcodingJob] = await Promise.all(promises);

            console.log(`Transcoding job started for video ${newVideo._id}:`, transcodingJob.name);

            // Store the transcoding job name and set the HLS playlist URL
            const hlsPlaylistUrl = `https://storage.googleapis.com/${bucketName}/hls/${newVideo._id}/manifest.m3u8`;
            await Video.findByIdAndUpdate(newVideo._id, {
                hlsPlaylistUrl,
                transcodingJobName: transcodingJob.name
            }, { new: true });

        } catch (processingError) {
            console.error('Error starting video processing:', processingError);

            // Update video status to failed and store error
            await Video.findByIdAndUpdate(newVideo._id, {
                status: 'failed',
                processingError: processingError.message
            });

            return res.status(500).json({
                error: 'Failed to start video processing',
                details: processingError.message
            });
        }
        
        await Project.findByIdAndUpdate(projectId,
            { $inc: { videoCount: 1 } },
            { new: true }
        );

        await User.findByIdAndUpdate(uploadedBy,
            {
                $inc: { 'storageType.normal': size },
                $set: { 'storageData': ((user.storageType?.normal + user.storageType?.archived + size) / STORAGE_LIMIT) * 100 }
            },
            { new: true }
        );

        res.status(201).json(newVideo);
    } catch (error) {
        console.error('Error in addVideo function:', error);
        res.status(500).json({ error: 'Failed to add video', details: error.message });
    }
};

export const updateVideo = async (req, res) => {
    try {
        const { title, description, status, isDownloadable, isCommentsEnabled } = req.body;

        // Build update object with only provided fields
        const updateFields = {
            updatedAt: new Date()
        };

        if (title !== undefined) updateFields.title = title;
        if (description !== undefined) updateFields.description = description || '';
        if (status !== undefined) updateFields.status = status;
        if (isDownloadable !== undefined) updateFields.isDownloadable = isDownloadable;
        if (isCommentsEnabled !== undefined) updateFields.isCommentsEnabled = isCommentsEnabled;

        const updatedVideo = await Video.findByIdAndUpdate(
            req.params.videoId,
            updateFields,
            { new: true }
        );

        if (!updatedVideo) {
            return res.status(404).json({ error: 'Video not found' });
        }
        res.json(updatedVideo);
    } catch (error) {
        console.error('Update video error:', error);
        res.status(500).json({ error: 'Failed to update video' });
    }
};

export const updateCustomThumbnail = async (req, res) => {
    try {
        const { videoId } = req.params;
        const { gcpFileName } = req.body;

        if (!gcpFileName) {
            return res.status(400).json({ error: 'gcpFileName is required' });
        }

        // Verify video exists
        const video = await Video.findById(videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }

        // Verify the uploaded file exists in GCS
        const file = bucket.file(gcpFileName);
        const [exists] = await file.exists();
        if (!exists) {
            return res.status(404).json({ error: 'Uploaded thumbnail file not found' });
        }

        // Generate public URL for the custom thumbnail
        const customThumbnailUrl = `https://storage.googleapis.com/${bucketName}/${gcpFileName}`;

        // Update video with custom thumbnail
        const updatedVideo = await Video.findByIdAndUpdate(
            videoId,
            {
                customThumbnail: customThumbnailUrl,
                thumbnailType: 'custom',
                updatedAt: new Date()
            },
            { new: true }
        );

        console.log(`Custom thumbnail updated for video ${videoId}:`, customThumbnailUrl);
        res.json({
            message: 'Custom thumbnail updated successfully',
            video: updatedVideo,
            thumbnailUrl: customThumbnailUrl
        });
    } catch (error) {
        console.error('Error updating custom thumbnail:', error);
        res.status(500).json({ error: 'Failed to update custom thumbnail', details: error.message });
    }
};

export const resetToAutoThumbnail = async (req, res) => {
    try {
        const { videoId } = req.params;

        // Verify video exists
        const video = await Video.findById(videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }

        // Reset to auto-generated thumbnail
        const updatedVideo = await Video.findByIdAndUpdate(
            videoId,
            {
                thumbnailType: 'auto',
                customThumbnail: null,
                updatedAt: new Date()
            },
            { new: true }
        );

        console.log(`Reset to auto thumbnail for video ${videoId}`);
        res.json({
            message: 'Reset to auto-generated thumbnail successfully',
            video: updatedVideo
        });
    } catch (error) {
        console.error('Error resetting to auto thumbnail:', error);
        res.status(500).json({ error: 'Failed to reset thumbnail', details: error.message });
    }
};

export const deleteVideo = async (req, res) => {
    try {
        const deletedVideo = await Video.findByIdAndDelete(req.params.videoId);
        if (!deletedVideo) {
            return res.status(404).json({ error: 'Video not found' });
        }

        // Delete file from GCS
        const cloudFile = "uploads" + deletedVideo.videoUrl.split("/uploads")[1];
        console.log("cloudFile", cloudFile);
        const file = bucket.file(cloudFile);
        file.delete();

        if (deletedVideo.storageClass === 'normal') {
            await User.findByIdAndUpdate(deletedVideo.uploadedBy, { $inc: { 'storageType.normal': -deletedVideo.size } }, { new: true });
        } else if (deletedVideo.storageClass === 'archived') {
            await User.findByIdAndUpdate(deletedVideo.uploadedBy, { $inc: { 'storageType.archived': -deletedVideo.size } }, { new: true });
        }
        await Project.findByIdAndUpdate(deletedVideo.projectId, { $inc: { videoCount: -1 } }, { new: true });

        res.json({ message: 'Video deleted successfully', deletedVideo });
    } catch (error) {
        res.status(500).json({ error: 'Failed to delete video' });
    }
};

// export const downloadVideo = async (req, res) => {



export const downloadVideo = async (req, res) => {
    try {
        const fileName = "gs://vid-reviewer-bucket/uploads/1738094038136-M3_MacBook_Air_Midnight_unboxing_to_cleanse_your_feed_-_Byte_Review_720p_h264.mp4";
        const options = {
            version: 'v4',
            action: 'read',
            expires: Date.now() + 15 * 60 * 1000, // URL expires in 15 minutes
        };

        console.log(bucket);
        const [signedUrl] = await storage
            .bucket(bucket)
            .file(fileName)
            .getSignedUrl(options);
        res.json({ signedUrl });
    } catch (error) {
        console.error('Error generating signed URL:', error);
        res.status(500).json({ error: 'Failed to generate download URL' });
    }
};


export const getDownloadUrl = async (req, res) => {
    try {
        const { videoPath } = req.body;
        console.log("videoPath", videoPath);

        if (!videoPath) {
            return res.status(400).json({ error: 'Video path is required' });
        }

        // Extract filename from the full path
        // videoPath might be like "https://storage.googleapis.com/bucket/uploads/filename.mp4"
        // or just "uploads/filename.mp4"
        let fileName;
        if (videoPath.includes('/uploads/')) {
            fileName = videoPath.split('/uploads/')[1];
        } else {
            fileName = videoPath.split('/').pop();
        }

        const bucketInstance = storage.bucket(bucketName);
        const file = bucketInstance.file("uploads/" + fileName);

        // Check if file exists
        const [exists] = await file.exists();
        if (!exists) {
            return res.status(404).json({ error: 'Video file not found' });
        }

        // Generate signed URL with appropriate options
        const [signedUrl] = await file.getSignedUrl({
            version: 'v4',
            action: 'read',
            expires: Date.now() + 15 * 60 * 1000, // 15 minutes
            // Add CORS-enabling options
            responseDisposition: `attachment; filename="${fileName}"`,
            responseType: 'application/octet-stream'
        });

        res.json({ signedUrl });
    } catch (error) {
        console.error('Error generating signed URL:', error);
        return res.status(500).json({ error: 'Failed to generate download URL' });
    }
};

export const archiveVideo = async (req, res) => {
    try {
        const { videoId } = req.body;

        const video = await Video.findByIdAndUpdate(videoId, { status: 'archived', storageClass: 'archived' }, { new: true });
        const userUpdatePromise = User.findByIdAndUpdate(
            video.uploadedBy,
            {
                $inc: { 'storageType.normal': -video.size, 'storageType.archived': video.size } 
            },
            { new: true }
        );

        // Archiving file from GCS
        const cloudFile = "uploads" + video.videoUrl.split("/uploads")[1];
        const file = bucket.file(cloudFile);
        await file.setStorageClass('ARCHIVE');
         
        await Promise.all([userUpdatePromise]);

        return res.json(video);
    } catch (error) {
        return res.status(500).json({ error: 'Failed to archive video',message: error.message });
    }
};

export const unarchiveVideo = async (req, res) => {
    try {
        const { videoId } = req.body;

        const video = await Video.findById(videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }

        const user = await User.findById(video.uploadedBy);

        if (user.storageType.normal + user.storageType.archived + video.size > STORAGE_LIMIT) {
            return res.status(402).json({
                error: "Storage limit exceeded",
                message: "Your storage limit of 100GB has been exceeded. Please upgrade your plan or free up some space."
            });
        }
        
        const unarchivedVideo = await Video.findByIdAndUpdate(videoId, { status: 'normal', storageClass: 'normal' }, { new: true });
        
        const userUpdatePromise = User.findByIdAndUpdate(
            video.uploadedBy,
            {
                $inc: { 'storageType.archived': -video.size, 'storageType.normal': video.size }, 
                $set: { 'storageData': ((user.storageType?.normal + user.storageType?.archived + video.size) / STORAGE_LIMIT) * 100 }
            },
            { new: true }
        );

          // Unarchiving file from GCS
          const cloudFile = "uploads" + unarchivedVideo.videoUrl.split("/uploads")[1];
          const file = bucket.file(cloudFile);
          await file.setStorageClass('STANDARD');
        
        await Promise.all([userUpdatePromise]);
        return res.json(unarchivedVideo);
        
    } catch (error) {
        return res.status(500).json({ error: 'Failed to unarchive video',message: error.message });
    }
};

// export const cronCheck = async (req, res) => {
//     console.log('Starting archival check...');
//   const bucket = storage.bucket(bucketName);

//   const [files] = await bucket.getFiles({
//     prefix: 'uploads/'
//   });
//   const now = new Date();

//   const daysThreshold = 30;

//   const thresholdDate = new Date(now.setDate(now.getDate() - daysThreshold));

//   for (const file of files) {
//     // Get metadata for the file
//     const [metadata] = await file.getMetadata();
//     const lastAccessTime = new Date(metadata.timeStorageClassUpdated || metadata.updated);
//     const gsutilUrl = `https://storage.googleapis.com/${bucketName}/${file.name}`;
//     // Check if the file hasn't been accessed within threshold
//     if (lastAccessTime < thresholdDate && metadata.storageClass !== 'ARCHIVE') {

//       // Update the storage class to ARCHIVE
//       await file.setStorageClass('ARCHIVE');

//       const video = await Video.findOneAndUpdate({ videoUrl: gsutilUrl }, { status: 'archived', storageClass: 'archived' }, { new: true });

//       const userUpdatePromise = User.findByIdAndUpdate(
//         video.uploadedBy,
//         {
//           $inc: { 'storageType.normal': -video.size, 'storageType.archived': video.size }
//         },
//         { new: true }
//       );


//       console.log(`Successfully archived ${file.name}`);
//     } else {
//       console.log(`Skipping ${file.name}: `);
//     }
//   }
// }