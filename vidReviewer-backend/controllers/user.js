import User from '../models/user.js';
import { encrypt, decrypt } from '../utils/encryption-utils.js';
export const getUserByLocationId = async (req, res) => {
    try {
        const user = await User.findOne({ locationId: req.params.locationId });

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        if (user.accessToken && user.refreshToken) {
            const decryptedData = {
                _id: user._id,
                name: user.name,
                email: user.email,
                locationId: user.locationId,
                accessToken: decrypt(user.accessToken),
                refreshToken: decrypt(user.refreshToken)
            };
            return res.status(200).json(decryptedData);
        }

        const decryptedData = {
            _id: user._id,
            name: user.name,
            email: user.email,
            locationId: user.locationId,
            storageUsed: user.storageType.normal + user.storageType.archived,
        };
        return res.json(decryptedData);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: error.message });
    }
};

export const getStorageByLocationId = async (req, res) => {
    try {
        const user = await User.findOne({ locationId: req.params.locationId });
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        return res.status(200).json({ storage: user.storageType });
    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: error.message });
    }

};

export const saveUser = async (locationId, userData) => {

    try {
        const userExists = await User.findOne({ locationId: locationId });
        if (userExists) {
            const encryptedData = {
                ...userData,
                accessToken: encrypt(userData.accessToken),
                refreshToken: encrypt(userData.refreshToken)
            };
            await User.updateOne({ locationId: locationId }, { $set: encryptedData });
            return userExists;
        }
        const encryptedData = {
            ...userData,
            accessToken: encrypt(userData.accessToken),
            refreshToken: encrypt(userData.refreshToken)
        };
        const user = await User.create(encryptedData);
        return user;
    } catch (error) {
        console.error('Error during user saving:', error.message);
        throw new Error('Failed to save user');
    }
};