import Invitation from '../models/invitation.js';

export const validateInvitationToken = async (req, res) => {
    const { token } = req.params;
    try {
        const invitation = await Invitation.findOne({ token, expiresAt: { $gt: new Date() } }); 
        if (!invitation) {
            return res.status(404).json({ message: 'Invalid or expired invitation token' });
        }
        if (invitation.status === 'accepted') {
            return res.status(200).json({ invitation: invitation, message: 'Invitation already accepted' });
        }
        invitation.status = 'accepted';
        await invitation.save();
        res.json(invitation);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal server error' });
    }
}