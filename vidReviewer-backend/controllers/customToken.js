import crypto from 'crypto';
import Invitation from '../models/invitation.js';
import dotenv from 'dotenv';
import Project from '../models/project.js';
dotenv.config();

export const customToken = async (req, res) => {
    const { projectId } = req.params;
    const { name, email } = req.body;
    try {
        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }

        // Check for existing invitation using new schema fields
        const existingInvitation = await Invitation.findOne({
            $or: [
                { projectId, email, status: { $in: ['pending', 'accepted'] } }, // Legacy
                { resourceId: projectId, resourceType: 'project', email, status: { $in: ['pending', 'accepted'] } } // New
            ]
        });

        if (existingInvitation && existingInvitation.expiresAt > new Date()) {
            return res.status(200).json({
                token: existingInvitation.token,
                message: 'Invitation already exists for this project and email.'
            });
        }

        if (existingInvitation && existingInvitation.expiresAt < new Date()) {
            existingInvitation.token = crypto.randomBytes(9).toString('base64url');
            existingInvitation.expiresAt = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000);
            existingInvitation.status = 'pending';
            await existingInvitation.save();
            return res.status(200).json({ token: existingInvitation.token, message: 'Invitation expired, new token created.' });
        }

        const token = crypto.randomBytes(9).toString('base64url');

        const invitation = new Invitation({
            name,
            email,
            projectId, // Keep for backward compatibility
            resourceType: 'project',
            resourceId: projectId,
            token,
        });

        await invitation.save();
        res.status(201).json({ token });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal server error' });
    }
}