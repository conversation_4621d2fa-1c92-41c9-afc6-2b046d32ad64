import Comment from '../models/comment.js';
import Video from '../models/video.js';
export const getComments = async (req, res) => {
    try {
        // Check if comments are enabled for this video
        const video = await Video.findById(req.params.videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }
        if (!video.isCommentsEnabled) {
            return res.status(403).json({ error: 'Comments are disabled for this video' });
        }

        const parentComments = await Comment.find({ videoId: req.params.videoId, parentCommentId: null }).sort({ createdAt: -1 });
        const commentsWithReplies = await Promise.all(parentComments.map(async (comment) => {
            const replies = await Comment.find({
                parentCommentId: comment._id
            }).populate('userId', 'name');

            return {
                ...comment.toObject(),
                replies
            };
        }));
        res.json({ comments: commentsWithReplies });
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch comments' });
    }
};

export const addComment = async (req, res) => {
    try {
        const { videoId, userId, content, parentCommentId, time } = req.body;

        // Check if the video exists and comments are enabled
        const video = await Video.findById(videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }
        if (video.status === 'deleted') {
            return res.status(400).json({ error: 'Video is deleted' });
        }
        if (!video.isCommentsEnabled) {
            return res.status(403).json({ error: 'Comments are disabled for this video' });
        }

        let { addedBy = 'Anonymous' } = req.body;

        addedBy = addedBy
            .trim()
            .replace(/\s+/g, ' ')
            .split(' ')
            .filter(word => word.length > 0)
            .map(word =>
                word.toLowerCase()
                    .replace(/[^a-z\-']/g, '')
                    .replace(/^./, char => char.toUpperCase())
            )
            .join(' ');

        // If empty after sanitization, use default
        addedBy = addedBy || 'Anonymous';

        const newComment = new Comment({
            videoId,
            userId,
            content,
            parentCommentId,
            time,
            addedBy
        });
        await newComment.save();

        video.comments.push(newComment._id);
        await video.save();

        res.status(201).json(newComment);
    } catch (error) {
        res.status(500).json({ error: 'Failed to add comment', error: error.message });
    }
};

export const updateComment = async (req, res) => {
    try {
        const { content, status } = req.body;

        // Find the comment first to get the videoId
        const comment = await Comment.findById(req.params.commentId);
        if (!comment) {
            return res.status(404).json({ error: 'Comment not found' });
        }

        // Check if comments are enabled for this video
        const video = await Video.findById(comment.videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }
        if (!video.isCommentsEnabled) {
            return res.status(403).json({ error: 'Comments are disabled for this video' });
        }

        const updatedComment = await Comment.findByIdAndUpdate(
            req.params.commentId,
            { content, status, updatedAt: new Date() },
            { new: true }
        );
        res.json(updatedComment);
    } catch (error) {
        console.error('Update error:', error);
        res.status(500).json({ error: 'Failed to update comment' });
    }
};

export const deleteComment = async (req, res) => {
    try {
        // Find the comment first to get the videoId
        const comment = await Comment.findById(req.params.commentId);
        if (!comment) {
            return res.status(404).json({ error: 'Comment not found' });
        }

        // Check if comments are enabled for this video
        const video = await Video.findById(comment.videoId);
        if (!video) {
            return res.status(404).json({ error: 'Video not found' });
        }
        if (!video.isCommentsEnabled) {
            return res.status(403).json({ error: 'Comments are disabled for this video' });
        }

        const deletedComment = await Comment.findByIdAndDelete(req.params.commentId);
        video.comments = video.comments.filter(comment => comment.toString() !== deletedComment._id.toString());
        await video.save();

        res.json({ message: 'Comment deleted successfully', deletedComment });
    } catch (error) {
        console.error('Delete error:', error);
        res.status(500).json({ error: 'Failed to delete comment' });
    }
};
