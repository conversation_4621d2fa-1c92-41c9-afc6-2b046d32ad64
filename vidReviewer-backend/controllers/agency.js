import Agency from '../models/agency.js';
import { encrypt } from '../utils/encryption-utils.js';
export const saveAgency = async (agencyData) => {
    try {
        const agencyExists = await Agency.findOne({ companyId: agencyData.companyId });
        if (agencyExists) {
            const encryptedData = {
                ...agencyData,
                access_token: encrypt(agencyData.access_token),
                refresh_token: encrypt(agencyData.refresh_token)
            };
            await Agency.updateOne({ companyId: agencyData.companyId }, { $set: encryptedData });
            return agencyExists;
        }
        const encryptedData = {
            ...agencyData,
            access_token: encrypt(agencyData.access_token),
            refresh_token: encrypt(agencyData.refresh_token)
        };
        const agency = await Agency.create(encryptedData);
        return agency;
    } catch (error) {
        console.error('Error during agency saving:', error.message);
        throw new Error('Failed to save agency');
    }
};
