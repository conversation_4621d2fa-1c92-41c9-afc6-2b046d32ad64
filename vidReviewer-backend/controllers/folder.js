import Folder from '../models/folder.js';
import Project from '../models/project.js';
import Video from '../models/video.js';

// Get all folders for a user (flat list) - only user folders, not sub-projects
export const getFolders = async (req, res) => {
    try {
        const { userId } = req.params;
        const folders = await Folder.find({
            createdBy: userId,
            contextType: 'user'
        }).sort({ path: 1 });
        res.json({ folders });
    } catch (error) {
        console.error('Fetch folders error:', error);
        res.status(500).json({ error: 'Failed to fetch folders' });
    }
};

// Get folder tree structure for a user - only user folders, not sub-projects
export const getFolderTree = async (req, res) => {
    try {
        const { userId } = req.params;
        const { includeProjects = false } = req.query;

        const folderTree = await Folder.getFolderTree('user', null, userId);
        
        if (includeProjects === 'true') {
            // Add projects to each folder in the tree
            const addProjectsToTree = async (folders) => {
                for (const folder of folders) {
                    const projects = await Project.find({
                        createdBy: userId,
                        folderId: folder._id
                    }).sort({ createdAt: -1 });

                    // Add thumbnails to projects
                    const projectsWithThumbnails = await Promise.all(projects.map(async project => {
                        let thumbnails = await Video.find({ projectId: project._id, status: { $ne: 'deleted' } }, { videoThumbnail: 1, _id: 0 }).sort({ uploadedOn: -1 }).limit(4);
                        return { ...project.toObject(), thumbnails };
                    }));

                    folder.projects = projectsWithThumbnails;

                    if (folder.children && folder.children.length > 0) {
                        await addProjectsToTree(folder.children);
                    }
                }
            };

            await addProjectsToTree(folderTree);

            // Also get root projects (projects not in any folder)
            const rootProjects = await Project.find({
                createdBy: userId,
                folderId: null
            }).sort({ createdAt: -1 });

            // Add thumbnails to root projects
            const rootProjectsWithThumbnails = await Promise.all(rootProjects.map(async project => {
                let thumbnails = await Video.find({ projectId: project._id, status: { $ne: 'deleted' } }, { videoThumbnail: 1, _id: 0 }).sort({ uploadedOn: -1 }).limit(4);
                return { ...project.toObject(), thumbnails };
            }));
            
            res.json({ folderTree, rootProjects: rootProjectsWithThumbnails });
        } else {
            res.json({ folderTree });
        }
    } catch (error) {
        console.error('Fetch folder tree error:', error);
        res.status(500).json({ error: 'Failed to fetch folder tree' });
    }
};

// Get folder by ID
export const getFolderById = async (req, res) => {
    try {
        const { folderId } = req.params;

        // Handle special case for "root" - return a virtual root folder
        if (folderId === 'root') {
            return res.json({
                _id: null,
                name: 'Root',
                description: 'Root folder',
                path: '/',
                level: 0,
                parentFolder: null,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        const folder = await Folder.findById(folderId);

        if (!folder) {
            return res.status(404).json({ error: 'Folder not found' });
        }

        res.json(folder);
    } catch (error) {
        console.error('Fetch folder error:', error);
        res.status(500).json({ error: 'Failed to fetch folder' });
    }
};

// Get folder contents (subfolders and projects)
export const getFolderContents = async (req, res) => {
    try {
        const { folderId } = req.params;
        const { userId } = req.query;

        if (!userId) {
            return res.status(400).json({ error: 'userId is required' });
        }

        // Handle "root" folder case - convert to null for database queries
        // This handles both /root/contents and /:folderId/contents where folderId is "root"
        const actualFolderId = (folderId === 'root' || !folderId) ? null : folderId;

        // Get subfolders - only user folders, not sub-projects
        const subfolders = await Folder.find({
            createdBy: userId,
            contextType: 'user',
            parentFolder: actualFolderId
        }).sort({ name: 1 });

        // Get projects in this folder
        const projects = await Project.find({
            createdBy: userId,
            folderId: actualFolderId
        }).sort({ createdAt: -1 });

        // Add thumbnails to projects
        const projectsWithThumbnails = await Promise.all(projects.map(async project => {
            let thumbnails = await Video.find({ projectId: project._id, status: { $ne: 'deleted' } }, { videoThumbnail: 1, _id: 0 }).sort({ uploadedOn: -1 }).limit(4);
            return { ...project.toObject(), thumbnails };
        }));

        res.json({ subfolders, projects: projectsWithThumbnails });
    } catch (error) {
        console.error('Fetch folder contents error:', error);
        res.status(500).json({ error: 'Failed to fetch folder contents' });
    }
};

// Create a new folder
export const createFolder = async (req, res) => {
    try {
        const { name, description, createdBy, parentFolder } = req.body;

        if (!name || !createdBy) {
            return res.status(400).json({ error: 'Name and createdBy are required' });
        }

        // Check if parent folder exists and belongs to the user
        if (parentFolder) {
            const parent = await Folder.findOne({ _id: parentFolder, createdBy });
            if (!parent) {
                return res.status(400).json({ error: 'Parent folder not found or access denied' });
            }
        }

        // Check for duplicate folder names in the same parent
        const existingFolder = await Folder.findOne({
            name,
            createdBy,
            contextType: 'user',
            parentFolder: parentFolder || null
        });

        if (existingFolder) {
            return res.status(400).json({ error: 'Folder with this name already exists in the same location' });
        }
        const newFolder = new Folder({
            name,
            description,
            createdBy,
            contextType: 'user',
            parentFolder: parentFolder || null
        });

        await newFolder.save();
        res.status(201).json(newFolder);
    } catch (error) {
        console.error('Create folder error:', error);
        res.status(500).json({ error: 'Failed to create folder', details: error.message });
    }
};

// Update folder
export const updateFolder = async (req, res) => {
    try {
        const { folderId } = req.params;
        const { name, description } = req.body;

        // Prevent updating "root" folder
        if (folderId === 'root') {
            return res.status(400).json({ error: 'Cannot update root folder' });
        }

        const folder = await Folder.findById(folderId);
        if (!folder) {
            return res.status(404).json({ error: 'Folder not found' });
        }
        
        // Check for duplicate names if name is being changed
        if (name && name !== folder.name) {
            const existingFolder = await Folder.findOne({
                name,
                createdBy: folder.createdBy,
                parentFolder: folder.parentFolder,
                _id: { $ne: folderId }
            });
            
            if (existingFolder) {
                return res.status(400).json({ error: 'Folder with this name already exists in the same location' });
            }
        }
        
        if (name) folder.name = name;
        if (description !== undefined) folder.description = description;
        
        await folder.save();
        res.json(folder);
    } catch (error) {
        console.error('Update folder error:', error);
        res.status(500).json({ error: 'Failed to update folder' });
    }
};

// Move folder to a different parent
export const moveFolder = async (req, res) => {
    try {
        const { folderId } = req.params;
        const { parentFolder } = req.body;
        const newParentId = parentFolder;

        // Prevent moving "root" folder
        if (folderId === 'root') {
            return res.status(400).json({ error: 'Cannot move root folder' });
        }

        const folder = await Folder.findById(folderId);
        if (!folder) {
            return res.status(404).json({ error: 'Folder not found' });
        }
        
        // Validate the move operation
        const canMove = await Folder.canMoveFolder(folderId, newParentId);
        if (!canMove) {
            return res.status(400).json({ error: 'Cannot move folder: would create circular reference' });
        }
        
        // Check if new parent exists and belongs to the same user
        if (newParentId) {
            const newParent = await Folder.findOne({ _id: newParentId, createdBy: folder.createdBy });
            if (!newParent) {
                return res.status(400).json({ error: 'New parent folder not found or access denied' });
            }
        }
        
        // Check for duplicate names in the new location
        const existingFolder = await Folder.findOne({
            name: folder.name,
            createdBy: folder.createdBy,
            parentFolder: newParentId || null,
            _id: { $ne: folderId }
        });
        
        if (existingFolder) {
            return res.status(400).json({ error: 'Folder with this name already exists in the destination' });
        }
        
        folder.parentFolder = newParentId || null;
        await folder.save();
        
        res.json(folder);
    } catch (error) {
        console.error('Move folder error:', error);
        res.status(500).json({ error: 'Failed to move folder' });
    }
};

// Delete folder (and optionally its contents)
export const deleteFolder = async (req, res) => {
    try {
        const { folderId } = req.params;
        const { deleteContents = false } = req.query;

        // Prevent deleting "root" folder
        if (folderId === 'root') {
            return res.status(400).json({ error: 'Cannot delete root folder' });
        }

        const folder = await Folder.findById(folderId);
        if (!folder) {
            return res.status(404).json({ error: 'Folder not found' });
        }
        
        if (deleteContents === 'true') {
            // Delete all descendant folders and their projects
            const descendants = await folder.getDescendants();
            const allFolderIds = [folderId, ...descendants.map(f => f._id)];
            
            // Delete all projects in these folders
            const deletedProjects = await Project.updateMany(
                { folderId: { $in: allFolderIds } },
                { status: 'deleted' }
            );
            
            // Delete all folders
            await Folder.deleteMany({ _id: { $in: allFolderIds } });
            
            res.json({ 
                message: 'Folder and all contents deleted successfully', 
                deletedFolder: folder,
                deletedFoldersCount: allFolderIds.length,
                deletedProjectsCount: deletedProjects.modifiedCount
            });
        } else {
            // Check if folder has contents
            const children = await folder.getChildren();
            const projects = await Project.find({ folderId });
            
            if (children.length > 0 || projects.length > 0) {
                return res.status(400).json({ 
                    error: 'Folder is not empty. Move contents or use deleteContents=true to force delete.' 
                });
            }
            
            await Folder.findByIdAndDelete(folderId);
            res.json({ message: 'Folder deleted successfully', deletedFolder: folder });
        }
    } catch (error) {
        console.error('Delete folder error:', error);
        res.status(500).json({ error: 'Failed to delete folder' });
    }
};
