import Invitation from '../models/invitation.js';
import Project from '../models/project.js';
import Video from '../models/video.js';
import Folder from '../models/folder.js';

export const getProjects = async (req, res) => {
    try {
        const { userId } = req.params;
        const { folderId } = req.query;

        // Build query based on folder filter
        const query = { createdBy: userId };
        if (folderId !== undefined) {
            // If folderId is provided, filter by that folder (null for root)
            query.folderId = folderId === 'null' ? null : folderId;
        }

        const projects = await Project.find(query).sort({ createdAt: -1 });
        if (projects.length === 0) {
            return res.status(200).json({message: "No projects found"});
        }

        const projectsWithThumbnails = await Promise.all(projects.map(async project => {
            let thumbnails = await Video.find({ projectId: project._id, status: { $ne: 'deleted' } }, { videoThumbnail: 1, _id: 0 }).sort({ uploadedOn: -1 }).limit(4);
            return { ...project.toObject(), thumbnails };
        }));

        res.json({ projects: projectsWithThumbnails });
    } catch (error) {
        console.error('Fetch projects error:', error);
        res.status(500).json({ error: 'Failed to fetch projects' });
    }
};

export const getProjectById = async (req, res) => {
    try {
        const project = await Project.findById(req.params.projectId);

        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        res.json(project);
    } catch (error) {
        console.error('Fetch project error:', error);
        res.status(500).json({ error: 'Failed to fetch project' });
    }
};

export const createProject = async (req, res) => {
    try {
        const { title, description, createdBy, name="", email="", folderId } = req.body;

        // Validate folder if provided
        if (folderId) {
            const folder = await Folder.findOne({ _id: folderId, createdBy });
            if (!folder) {
                return res.status(400).json({ error: 'Folder not found or access denied' });
            }
        }

        const newProject = new Project({
            title,
            description,
            createdBy,
            name,
            email,
            folderId: folderId || null
        });
        await newProject.save();
        res.status(201).json(newProject);
    } catch (error) {
        console.error('Create project error:', error);
        res.status(500).json({ error: 'Failed to create project', details: error.message });
    }
};

export const updateProject = async (req, res) => {
    try {
        const { title, description } = req.body;
        const updatedProject = await Project.findByIdAndUpdate(
            req.params.projectId,
            { title, description },
            { new: true }
        );

        if (!updatedProject) {
            return res.status(404).json({ error: 'Project not found' });
        }

        res.json(updatedProject);
    } catch (error) {
        console.error('Update project error:', error);
        res.status(500).json({ error: 'Failed to update project' });
    }
};

export const deleteProject = async (req, res) => {
    try {
        const deletedProject = await Project.findByIdAndDelete(req.params.projectId);

        if (!deletedProject) {
            return res.status(404).json({ error: 'Project not found' });
        }
        
        const updatedVideos = await Video.updateMany(
            { projectId: req.params.projectId },
            { status: 'deleted' }
        );

        res.json({ message: 'Project and associated videos deleted successfully', deletedProject, deletedVideosCount: updatedVideos.modifiedCount });
    } catch (error) {
        console.error('Delete project error:', error);
        res.status(500).json({ error: 'Failed to delete project' });
    }
};

export const getCollaborators = async (req, res) => {
    const projectId = req.params.projectId;
    const collaborators = await Invitation.find({ projectId: projectId}, { name: 1, _id: 0 });
    res.json(collaborators);
};

// Move project to a different folder
export const moveProject = async (req, res) => {
    try {
        const { projectId } = req.params;
        const { folderId } = req.body;

        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        // Validate folder if provided
        if (folderId) {
            const folder = await Folder.findOne({ _id: folderId, createdBy: project.createdBy });
            if (!folder) {
                return res.status(400).json({ error: 'Folder not found or access denied' });
            }
        }

        project.folderId = folderId || null;
        await project.save();

        res.json(project);
    } catch (error) {
        console.error('Move project error:', error);
        res.status(500).json({ error: 'Failed to move project' });
    }
};