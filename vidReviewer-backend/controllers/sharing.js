import crypto from 'crypto';
import Invitation from '../models/invitation.js';
import Project from '../models/project.js';
import Folder from '../models/folder.js';
import Video from '../models/video.js';
import User from '../models/user.js';
import dotenv from 'dotenv';

dotenv.config();

// Helper function to get resource model and validate access
const getResourceAndValidateAccess = async (resourceType, resourceId, userId) => {
    let resource;
    let hasAccess = false;

    switch (resourceType) {
        case 'project':
            resource = await Project.findById(resourceId);
            if (resource) {
                hasAccess = resource.createdBy.toString() === userId;
            }
            break;
        
        case 'folder':
            resource = await Folder.findById(resourceId);
            if (resource) {
                hasAccess = resource.createdBy.toString() === userId;
            }
            break;
        
        case 'video':
            resource = await Video.findById(resourceId).populate('projectId');
            if (resource) {
                // User has access if they uploaded the video or own the project
                hasAccess = resource.uploadedBy.toString() === userId || 
                           resource.projectId.createdBy.toString() === userId;
            }
            break;
        
        default:
            throw new Error('Invalid resource type');
    }

    return { resource, hasAccess };
};

// Create sharing token for any resource type
export const createSharingToken = async (req, res) => {
    const { resourceType, resourceId } = req.params;
    const { name, email, locationId } = req.body;

    // Validate required fields
    if (!locationId) {
        return res.status(400).json({ message: 'Location ID is required' });
    }
    if (!name) {
        return res.status(400).json({ message: 'Name is required' });
    }
    if (!email) {
        return res.status(400).json({ message: 'Email is required' });
    }

    try {
        // Find user by locationId to get their MongoDB _id
        const user = await User.findOne({ locationId });
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        const userId = user._id.toString();

        // Validate resource type
        if (!['project', 'folder', 'video'].includes(resourceType)) {
            return res.status(400).json({ message: 'Invalid resource type' });
        }

        // Validate resource ID format (MongoDB ObjectId)
        if (!/^[0-9a-fA-F]{24}$/.test(resourceId)) {
            return res.status(400).json({ message: 'Invalid resource ID format' });
        }

        // Get resource and validate access
        const { resource, hasAccess } = await getResourceAndValidateAccess(resourceType, resourceId, userId);

        if (!resource) {
            return res.status(404).json({ message: `${resourceType.charAt(0).toUpperCase() + resourceType.slice(1)} not found` });
        }

        if (!hasAccess) {
            return res.status(403).json({ message: 'Access denied' });
        }

        // Check for existing invitation
        const existingInvitation = await Invitation.findOne({ 
            resourceId, 
            resourceType,
            email, 
            status: { $in: ['pending', 'accepted'] } 
        });

        if (existingInvitation && existingInvitation.expiresAt > new Date()) {
            return res.status(200).json({
                token: existingInvitation.token,
                message: `Invitation already exists for this ${resourceType} and email.`
            });
        }

        // Update expired invitation or create new one
        if (existingInvitation && existingInvitation.expiresAt < new Date()) {
            existingInvitation.token = crypto.randomBytes(9).toString('base64url');
            existingInvitation.expiresAt = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000);
            existingInvitation.status = 'pending';
            await existingInvitation.save();
            return res.status(200).json({ 
                token: existingInvitation.token, 
                message: `Invitation expired, new token created for ${resourceType}.` 
            });
        }

        // Create new invitation
        const token = crypto.randomBytes(9).toString('base64url');
        const invitation = new Invitation({
            name,
            email,
            resourceType,
            resourceId,
            token,
            // Set projectId for backward compatibility
            projectId: resourceType === 'project' ? resourceId : 
                      resourceType === 'video' ? resource.projectId._id : 
                      resourceType === 'folder' && resource.contextType === 'project' ? resource.contextId : null
        });

        await invitation.save();
        res.status(201).json({ token, resourceType, resourceId });
    } catch (error) {
        console.error('Error creating sharing token:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Get shared resource by token
export const getSharedResource = async (req, res) => {
    const { token } = req.params;

    try {
        const invitation = await Invitation.findOne({
            token,
            status: { $in: ['pending', 'accepted'] },
            expiresAt: { $gt: new Date() }
        }).populate('resourceId');

        if (!invitation) {
            return res.status(404).json({ message: 'Invalid or expired sharing link' });
        }

        // Mark as accepted if pending
        if (invitation.status === 'pending') {
            invitation.status = 'accepted';
            await invitation.save();
        }

        let resourceData;
        switch (invitation.resourceType) {
            case 'project':
                resourceData = await Project.findById(invitation.resourceId)
                    .populate('createdBy', 'userName email');

                // Get project folders and videos
                if (resourceData) {
                    const folders = await Folder.find({
                        contextType: 'project',
                        contextId: invitation.resourceId
                    }).populate('createdBy', 'userName email');

                    const videos = await Video.find({
                        projectId: invitation.resourceId
                    }).populate('uploadedBy', 'userName email');

                    // Get thumbnails for project card display
                    const thumbnails = await Video.find(
                        { projectId: invitation.resourceId, status: { $ne: 'deleted' } },
                        { videoThumbnail: 1, _id: 0 }
                    ).sort({ uploadedOn: -1 }).limit(4);

                    resourceData = {
                        ...resourceData.toObject(),
                        folders,
                        videos,
                        thumbnails
                    };
                }
                break;

            case 'folder':
                resourceData = await Folder.findById(invitation.resourceId)
                    .populate('createdBy', 'userName email')
                    .populate('contextId', 'title');

                // Get folder contents
                if (resourceData) {
                    const subfolders = await Folder.find({
                        parentFolder: invitation.resourceId
                    }).populate('createdBy', 'userName email');

                    // Check if this is a project folder (sub-project) or user folder
                    let videos;
                    if (resourceData.contextType === 'project') {
                        // For project folders, videos are stored with projectFolderId
                        videos = await Video.find({
                            projectFolderId: invitation.resourceId,
                            status: { $ne: 'deleted' }
                        }).populate('uploadedBy', 'userName email');
                    } else {
                        // For user folders, videos are stored with folderId
                        videos = await Video.find({
                            folderId: invitation.resourceId,
                            status: { $ne: 'deleted' }
                        }).populate('uploadedBy', 'userName email');
                    }

                    resourceData = {
                        ...resourceData.toObject(),
                        subfolders,
                        videos
                    };
                }
                break;

            case 'video':
                resourceData = await Video.findById(invitation.resourceId)
                    .populate('uploadedBy', 'userName email')
                    .populate('projectId', 'title');
                break;
        }

        res.status(200).json({
            resourceType: invitation.resourceType,
            resource: resourceData,
            sharedBy: invitation.name,
            sharedEmail: invitation.email,
            sharedTo: invitation.name, // Name of the person to whom the resource was shared
            sharedToId: invitation._id // ID of the invitation/sharing record
        });
    } catch (error) {
        console.error('Error getting shared resource:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// List all sharing invitations for a resource
export const getResourceInvitations = async (req, res) => {
    const { resourceType, resourceId } = req.params;
    const userId = req.user?.id;

    try {
        // Validate access to resource
        const { resource, hasAccess } = await getResourceAndValidateAccess(resourceType, resourceId, userId);
        
        if (!resource) {
            return res.status(404).json({ message: `${resourceType.charAt(0).toUpperCase() + resourceType.slice(1)} not found` });
        }

        if (!hasAccess) {
            return res.status(403).json({ message: 'Access denied' });
        }

        const invitations = await Invitation.find({
            resourceId,
            resourceType,
            expiresAt: { $gt: new Date() }
        }).sort({ createdAt: -1 });

        res.status(200).json(invitations);
    } catch (error) {
        console.error('Error getting resource invitations:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Revoke sharing invitation
export const revokeSharingInvitation = async (req, res) => {
    const { invitationId } = req.params;
    const userId = req.user?.id;

    try {
        const invitation = await Invitation.findById(invitationId);
        if (!invitation) {
            return res.status(404).json({ message: 'Invitation not found' });
        }

        // Validate access to the resource
        const { hasAccess } = await getResourceAndValidateAccess(
            invitation.resourceType, 
            invitation.resourceId, 
            userId
        );

        if (!hasAccess) {
            return res.status(403).json({ message: 'Access denied' });
        }

        // Set expiration to now to effectively revoke
        invitation.expiresAt = new Date();
        invitation.status = 'revoked';
        await invitation.save();

        res.status(200).json({ message: 'Invitation revoked successfully' });
    } catch (error) {
        console.error('Error revoking invitation:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};
