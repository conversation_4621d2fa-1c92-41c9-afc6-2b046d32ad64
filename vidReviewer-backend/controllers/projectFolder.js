import Folder from '../models/folder.js';
import Project from '../models/project.js';
import Video from '../models/video.js';

// Get folder tree structure for a project
export const getProjectFolderTree = async (req, res) => {
    try {
        const { projectId } = req.params;
        const { includeVideos = false } = req.query;
        
        // Verify project exists and user has access
        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }
        
        const folderTree = await Folder.getFolderTree('project', projectId, project.createdBy);
        
        if (includeVideos === 'true') {
            // Add videos and statistics to each folder in the tree
            const addVideosAndStatsToTree = async (folders) => {
                for (const folder of folders) {
                    const videos = await Video.find({
                        projectId,
                        projectFolderId: folder._id,
                        status: { $ne: 'deleted' }
                    }).sort({ uploadedOn: -1 });

                    folder.videos = videos;

                    // Calculate folder statistics
                    folder.stats = {
                        videoCount: videos.length,
                        totalSize: videos.reduce((sum, video) => sum + (video.size || 0), 0),
                        totalDuration: videos.reduce((sum, video) => sum + (video.duration || 0), 0),
                        folderCount: folder.children ? folder.children.length : 0
                    };

                    if (folder.children && folder.children.length > 0) {
                        await addVideosAndStatsToTree(folder.children);

                        // Add child statistics to parent
                        for (const child of folder.children) {
                            if (child.stats) {
                                folder.stats.videoCount += child.stats.videoCount;
                                folder.stats.totalSize += child.stats.totalSize;
                                folder.stats.totalDuration += child.stats.totalDuration;
                                folder.stats.folderCount += child.stats.folderCount;
                            }
                        }
                    }
                }
            };

            await addVideosAndStatsToTree(folderTree);

            // Also get root videos (videos not in any project folder)
            const rootVideos = await Video.find({
                projectId,
                projectFolderId: null,
                status: { $ne: 'deleted' }
            }).sort({ uploadedOn: -1 });

            // Calculate root statistics
            const rootStats = {
                videoCount: rootVideos.length,
                totalSize: rootVideos.reduce((sum, video) => sum + (video.size || 0), 0),
                totalDuration: rootVideos.reduce((sum, video) => sum + (video.duration || 0), 0),
                folderCount: folderTree.length
            };

            res.json({ folderTree, rootVideos, rootStats });
        } else {
            // Add basic statistics without videos
            const addStatsToTree = async (folders) => {
                for (const folder of folders) {
                    const videoCount = await Video.countDocuments({
                        projectId,
                        projectFolderId: folder._id,
                        status: { $ne: 'deleted' }
                    });

                    folder.stats = {
                        videoCount,
                        folderCount: folder.children ? folder.children.length : 0
                    };

                    if (folder.children && folder.children.length > 0) {
                        await addStatsToTree(folder.children);

                        // Add child statistics to parent
                        for (const child of folder.children) {
                            if (child.stats) {
                                folder.stats.videoCount += child.stats.videoCount;
                                folder.stats.folderCount += child.stats.folderCount;
                            }
                        }
                    }
                }
            };

            await addStatsToTree(folderTree);
            res.json({ folderTree });
        }
    } catch (error) {
        console.error('Fetch project folder tree error:', error);
        res.status(500).json({ error: 'Failed to fetch project folder tree' });
    }
};

// Get folder contents within a project (subfolders and videos)
export const getProjectFolderContents = async (req, res) => {
    try {
        const { projectId, folderId } = req.params;
        
        // Verify project exists
        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }
        
        // If folderId is provided, verify it belongs to this project
        if (folderId && folderId !== 'root') {
            const folder = await Folder.findOne({ 
                _id: folderId, 
                contextType: 'project', 
                contextId: projectId 
            });
            if (!folder) {
                return res.status(404).json({ error: 'Folder not found in this project' });
            }
        }
        
        // Get subfolders
        const subfolders = await Folder.find({
            contextType: 'project',
            contextId: projectId,
            parentFolder: folderId === 'root' ? null : folderId
        }).sort({ name: 1 });

        // Add video count to each subfolder
        const subfoldersWithVideoCounts = await Promise.all(subfolders.map(async (subfolder) => {
            const videoCount = await Video.countDocuments({
                projectId,
                projectFolderId: subfolder._id,
                status: { $ne: 'deleted' }
            });
            return {
                ...subfolder.toObject(),
                videoCount
            };
        }));

        // Get videos in this folder
        const videos = await Video.find({
            projectId,
            projectFolderId: folderId === 'root' ? null : folderId,
            status: { $ne: 'deleted' }
        }).sort({ uploadedOn: -1 });

        res.json({ subProjects: subfoldersWithVideoCounts, videos });
    } catch (error) {
        console.error('Fetch project folder contents error:', error);
        res.status(500).json({ error: 'Failed to fetch project folder contents' });
    }
};

// Create a new sub-project within a project
export const createProjectFolder = async (req, res) => {
    try {
        const { projectId } = req.params;
        const { name, description, parentFolder } = req.body;

        if (!name) {
            return res.status(400).json({ error: 'Sub-project name is required' });
        }
        
        // Verify project exists and get creator
        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }
        
        // Check if parent sub-project exists and belongs to this project
        if (parentFolder) {
            const parent = await Folder.findOne({
                _id: parentFolder,
                contextType: 'project',
                contextId: projectId
            });
            if (!parent) {
                return res.status(400).json({ error: 'Parent sub-project not found in this project' });
            }
        }
        // Removed 3 sub-project limit validation
        
        // Check for duplicate sub-project names in the same parent within the project
        const existingFolder = await Folder.findOne({
            name,
            contextType: 'project',
            contextId: projectId,
            parentFolder: parentFolder || null
        });

        if (existingFolder) {
            return res.status(400).json({ error: 'Sub-project with this name already exists in the same location' });
        }
        
        const newFolder = new Folder({
            name,
            description,
            createdBy: project.createdBy,
            contextType: 'project',
            contextId: projectId,
            parentFolder: parentFolder || null
        });
        
        await newFolder.save();
        res.status(201).json(newFolder);
    } catch (error) {
        console.error('Create sub-project error:', error);

        // Handle specific validation errors
        // Removed 3 sub-project limit error handling

        res.status(500).json({ error: 'Failed to create sub-project', details: error.message });
    }
};

// Update project folder
export const updateProjectFolder = async (req, res) => {
    try {
        const { projectId, folderId } = req.params;
        const { name, description } = req.body;
        
        const folder = await Folder.findOne({ 
            _id: folderId, 
            contextType: 'project', 
            contextId: projectId 
        });
        
        if (!folder) {
            return res.status(404).json({ error: 'Folder not found in this project' });
        }
        
        // Check for duplicate names if name is being changed
        if (name && name !== folder.name) {
            const existingFolder = await Folder.findOne({
                name,
                contextType: 'project',
                contextId: projectId,
                parentFolder: folder.parentFolder,
                _id: { $ne: folderId }
            });
            
            if (existingFolder) {
                return res.status(400).json({ error: 'Folder with this name already exists in the same location' });
            }
        }
        
        if (name) folder.name = name;
        if (description !== undefined) folder.description = description;
        
        await folder.save();
        res.json(folder);
    } catch (error) {
        console.error('Update project folder error:', error);
        res.status(500).json({ error: 'Failed to update project folder' });
    }
};

// Move project folder to a different parent within the same project
export const moveProjectFolder = async (req, res) => {
    try {
        const { projectId, folderId } = req.params;
        const { newParentId } = req.body;
        
        const folder = await Folder.findOne({ 
            _id: folderId, 
            contextType: 'project', 
            contextId: projectId 
        });
        
        if (!folder) {
            return res.status(404).json({ error: 'Folder not found in this project' });
        }
        
        // Validate the move operation
        const canMove = await Folder.canMoveFolder(folderId, newParentId);
        if (!canMove) {
            return res.status(400).json({ error: 'Cannot move folder: would create circular reference' });
        }
        
        // Check if new parent exists and belongs to the same project
        if (newParentId) {
            const newParent = await Folder.findOne({ 
                _id: newParentId, 
                contextType: 'project', 
                contextId: projectId 
            });
            if (!newParent) {
                return res.status(400).json({ error: 'New parent folder not found in this project' });
            }
        }
        
        // Check for duplicate names in the new location
        const existingFolder = await Folder.findOne({
            name: folder.name,
            contextType: 'project',
            contextId: projectId,
            parentFolder: newParentId || null,
            _id: { $ne: folderId }
        });
        
        if (existingFolder) {
            return res.status(400).json({ error: 'Folder with this name already exists in the destination' });
        }
        
        folder.parentFolder = newParentId || null;
        await folder.save();
        
        res.json(folder);
    } catch (error) {
        console.error('Move project folder error:', error);
        res.status(500).json({ error: 'Failed to move project folder' });
    }
};

// Delete project folder (and optionally its contents)
export const deleteProjectFolder = async (req, res) => {
    try {
        const { projectId, folderId } = req.params;
        const { deleteContents = false } = req.query;
        
        const folder = await Folder.findOne({ 
            _id: folderId, 
            contextType: 'project', 
            contextId: projectId 
        });
        
        if (!folder) {
            return res.status(404).json({ error: 'Folder not found in this project' });
        }
        
        if (deleteContents === 'true') {
            // Delete all descendant folders and their videos
            const descendants = await folder.getDescendants();
            const allFolderIds = [folderId, ...descendants.map(f => f._id)];
            
            // Delete all videos in these folders
            const deletedVideos = await Video.updateMany(
                { projectFolderId: { $in: allFolderIds } },
                { status: 'deleted' }
            );
            
            // Delete all folders
            await Folder.deleteMany({ _id: { $in: allFolderIds } });
            
            res.json({ 
                message: 'Project folder and all contents deleted successfully', 
                deletedFolder: folder,
                deletedFoldersCount: allFolderIds.length,
                deletedVideosCount: deletedVideos.modifiedCount
            });
        } else {
            // Check if folder has contents
            const children = await Folder.find({ 
                contextType: 'project', 
                contextId: projectId, 
                parentFolder: folderId 
            });
            const videos = await Video.find({ 
                projectId, 
                projectFolderId: folderId,
                status: { $ne: 'deleted' }
            });
            
            if (children.length > 0 || videos.length > 0) {
                return res.status(400).json({ 
                    error: 'Folder is not empty. Move contents or use deleteContents=true to force delete.' 
                });
            }
            
            await Folder.findByIdAndDelete(folderId);
            res.json({ message: 'Project folder deleted successfully', deletedFolder: folder });
        }
    } catch (error) {
        console.error('Delete project folder error:', error);
        res.status(500).json({ error: 'Failed to delete project folder' });
    }
};

// Bulk move project folders
export const bulkMoveProjectFolders = async (req, res) => {
    try {
        const { projectId } = req.params;
        const { folderIds, newParentId } = req.body;

        if (!Array.isArray(folderIds) || folderIds.length === 0) {
            return res.status(400).json({ error: 'Folder IDs array is required' });
        }

        // Verify project exists
        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        // Validate new parent folder if provided
        if (newParentId) {
            const parentFolder = await Folder.findOne({
                _id: newParentId,
                contextType: 'project',
                contextId: projectId
            });
            if (!parentFolder) {
                return res.status(400).json({ error: 'Parent folder not found' });
            }
        }

        // Check for circular references
        if (newParentId && folderIds.includes(newParentId)) {
            return res.status(400).json({ error: 'Cannot move folder into itself' });
        }

        // Update all folders
        const result = await Folder.updateMany(
            {
                _id: { $in: folderIds },
                contextType: 'project',
                contextId: projectId
            },
            {
                parentFolder: newParentId || null,
                // Update path and level will be handled by folder model hooks if implemented
            }
        );

        res.json({
            message: `Successfully moved ${result.modifiedCount} folders`,
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        console.error('Bulk move project folders error:', error);
        res.status(500).json({ error: 'Failed to bulk move project folders' });
    }
};

// Bulk delete project folders
export const bulkDeleteProjectFolders = async (req, res) => {
    try {
        const { projectId } = req.params;
        const { folderIds, deleteContents = false } = req.body;

        if (!Array.isArray(folderIds) || folderIds.length === 0) {
            return res.status(400).json({ error: 'Folder IDs array is required' });
        }

        // Verify project exists
        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        let deletedFoldersCount = 0;
        let deletedVideosCount = 0;

        for (const folderId of folderIds) {
            const folder = await Folder.findOne({
                _id: folderId,
                contextType: 'project',
                contextId: projectId
            });

            if (!folder) {
                continue; // Skip if folder doesn't exist
            }

            if (deleteContents) {
                // Get all descendant folders
                const allFolderIds = await Folder.getAllDescendantIds(folderId, 'project', projectId);
                allFolderIds.push(folderId);

                // Move videos to deleted status or delete them
                const deletedVideos = await Video.updateMany(
                    {
                        projectId,
                        projectFolderId: { $in: allFolderIds },
                        status: { $ne: 'deleted' }
                    },
                    { status: 'deleted' }
                );
                deletedVideosCount += deletedVideos.modifiedCount;

                // Delete all folders
                const deletedFolders = await Folder.deleteMany({ _id: { $in: allFolderIds } });
                deletedFoldersCount += deletedFolders.deletedCount;
            } else {
                // Check if folder has contents
                const children = await Folder.find({
                    contextType: 'project',
                    contextId: projectId,
                    parentFolder: folderId
                });
                const videos = await Video.find({
                    projectId,
                    projectFolderId: folderId,
                    status: { $ne: 'deleted' }
                });

                if (children.length > 0 || videos.length > 0) {
                    return res.status(400).json({
                        error: `Folder ${folder.name} is not empty. Move contents or use deleteContents=true to force delete.`
                    });
                }

                await Folder.findByIdAndDelete(folderId);
                deletedFoldersCount += 1;
            }
        }

        res.json({
            message: `Successfully deleted ${deletedFoldersCount} folders and ${deletedVideosCount} videos`,
            deletedFoldersCount,
            deletedVideosCount
        });
    } catch (error) {
        console.error('Bulk delete project folders error:', error);
        res.status(500).json({ error: 'Failed to bulk delete project folders' });
    }
};

// Advanced search within a project
export const searchProjectContent = async (req, res) => {
    try {
        const { projectId } = req.params;
        const {
            query = '',
            type = 'all',
            dateRange = 'all',
            sizeRange = 'all',
            status = 'all'
        } = req.query;

        // Verify project exists
        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        let videoFilter = {
            projectId,
            status: { $ne: 'deleted' }
        };
        let folderFilter = {
            contextType: 'project',
            contextId: projectId
        };

        // Text search
        if (query.trim()) {
            const searchRegex = new RegExp(query.trim(), 'i');
            videoFilter.$or = [
                { title: searchRegex },
                { description: searchRegex }
            ];
            folderFilter.$or = [
                { name: searchRegex },
                { description: searchRegex }
            ];
        }

        // Date range filter
        if (dateRange !== 'all') {
            const now = new Date();
            let cutoffDate;

            switch (dateRange) {
                case 'today':
                    cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case 'week':
                    cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    cutoffDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                    break;
                case 'year':
                    cutoffDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                    break;
                default:
                    cutoffDate = new Date(0);
            }

            videoFilter.uploadedOn = { $gte: cutoffDate };
            folderFilter.createdAt = { $gte: cutoffDate };
        }

        // Size filter for videos
        if (sizeRange !== 'all') {
            switch (sizeRange) {
                case 'small':
                    videoFilter.size = { $lt: 100 * 1024 * 1024 }; // < 100MB
                    break;
                case 'medium':
                    videoFilter.size = {
                        $gte: 100 * 1024 * 1024,
                        $lt: 1024 * 1024 * 1024
                    }; // 100MB - 1GB
                    break;
                case 'large':
                    videoFilter.size = { $gte: 1024 * 1024 * 1024 }; // > 1GB
                    break;
            }
        }

        // Status filter for videos
        if (status !== 'all') {
            switch (status) {
                case 'processing':
                    videoFilter.status = 'processing';
                    break;
                case 'ready':
                    videoFilter.status = 'ready';
                    break;
                case 'archived':
                    videoFilter.storageClass = 'archived';
                    break;
            }
        }

        let videos = [];
        let folders = [];

        // Execute searches based on type filter
        if (type === 'all' || type === 'videos') {
            videos = await Video.find(videoFilter)
                .sort({ uploadedOn: -1 })
                .limit(100); // Limit results for performance
        }

        if (type === 'all' || type === 'folders') {
            folders = await Folder.find(folderFilter)
                .sort({ createdAt: -1 })
                .limit(100); // Limit results for performance
        }

        res.json({
            videos,
            folders,
            totalVideos: videos.length,
            totalFolders: folders.length,
            searchQuery: query,
            filters: { type, dateRange, sizeRange, status }
        });
    } catch (error) {
        console.error('Search project content error:', error);
        res.status(500).json({ error: 'Failed to search project content' });
    }
};
