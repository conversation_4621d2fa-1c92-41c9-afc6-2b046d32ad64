import User from "../models/user.js";
import { makeGhlApiRequest } from "../services/ghl.js";
import { GhlRefreshTokenError } from "../utils/errors.js";
import { sendApiErrorNotification } from "../services/slack.js";

export const getContacts = async (req, res) => {
  try {
    const { locationId } = req.params;
    const { query } = req.body ?? "";
    let user = await User.findOne({ locationId: locationId });

    // For testing purposes, return mock data if locationId starts with 'test-'
    if (locationId.startsWith('test-')) {
      const mockContacts = [
        {
          id: 'contact-1',
          firstNameLowerCase: 'john',
          lastNameLowerCase: 'doe',
          email: '<EMAIL>',
          phone: '+1234567890'
        },
        {
          id: 'contact-2',
          firstNameLowerCase: 'jane',
          lastNameLowerCase: 'smith',
          email: '<EMAIL>',
          phone: '+1234567891'
        },
        {
          id: 'contact-3',
          firstNameLowerCase: 'bob',
          lastNameLowerCase: 'johnson',
          email: '<EMAIL>',
          phone: '+1234567892'
        },
        {
          id: 'contact-4',
          firstNameLowerCase: 'alice',
          lastNameLowerCase: 'brown',
          email: '<EMAIL>',
          phone: '+1234567893'
        },
        {
          id: 'contact-5',
          firstNameLowerCase: 'charlie',
          lastNameLowerCase: 'wilson',
          email: '<EMAIL>',
          phone: '+1234567894'
        }
      ];

      // Filter contacts based on query if provided
      let filteredContacts = mockContacts;
      if (query && query.length > 0) {
        filteredContacts = mockContacts.filter(contact =>
          contact.firstNameLowerCase.includes(query.toLowerCase()) ||
          contact.lastNameLowerCase.includes(query.toLowerCase()) ||
          contact.email.toLowerCase().includes(query.toLowerCase()) ||
          contact.phone.includes(query)
        );
      }

      return res.status(200).json({
        contacts: filteredContacts,
        total: filteredContacts.length,
        traceId: 'mock-trace-id'
      });
    }

    if (!user.accessToken) {
      return res.status(400).json({ error: "Access token not found" });
    }

    const requestBody = {
      locationId: locationId,
      pageLimit: 100,
    };

    if (query) {
      requestBody.filters = [
        {
          group: "OR",
          filters: [
            {
              field: "firstNameLowerCase",
              operator: "contains",
              value: query,
            },
            {
              field: "lastNameLowerCase",
              operator: "contains",
              value: query,
            },
            {
              field: "email",
              operator: "contains",
              value: query,
            },
            {
              field: "phone",
              operator: "contains",
              value: query,
            },
          ],
        },
      ];
    }

    const data = await makeGhlApiRequest(
      `/contacts/search`,
      'POST',
      requestBody,
      {
        accessToken: user.accessToken,
        refreshToken: user.refreshToken,
        companyId: user.companyId,
        locationId: user.locationId
      }
    );

    if (!data.contacts) {
      return res.status(200).json({
        contacts: [],
        total: 0,
      });
    }

    const filteredContacts = data.contacts.filter(
      (contact) =>
        contact.firstNameLowerCase && contact.firstNameLowerCase.trim() !== "",
    );
    const filteredData = {
      ...data,
      contacts: filteredContacts,
      total: filteredContacts.length,
    };

    res.status(200).json(filteredData);
  } catch (error) {
    if (error instanceof GhlRefreshTokenError) {
      await User.updateOne({ locationId: req.params.locationId }, { $set: { accessToken: null, refreshToken: null } });
      
      // Send Slack notification
      try {
        await sendApiErrorNotification(error, '/contacts/search', { locationId: req.params.locationId });
      } catch (slackError) {
        console.error('Failed to send Slack notification:', slackError.message);
      }
      
      return res.status(401).json({ 
        error: 'Re-authentication required.', 
        message: error.message 
      });
    }
    console.error("Fetch contacts error:", error);
    res.status(500).json({ error: "Failed to fetch contacts" });
  }
};
