import qs from 'qs';
import { saveUser } from './user.js';
import { saveAgency } from './agency.js';
import { v4 as uuidv4 } from 'uuid';
import { connectToDB } from '../config/db.js';

export default async function callback(req, res) {
    
    const data = qs.stringify({
        'client_id': process.env.CLIENT_ID,
        'client_secret': process.env.CLIENT_SECRET,
        'grant_type': 'authorization_code',
        'code': req.query.code,
        'user_type': 'Company',
        'redirect_uri': process.env.REDIRECT_URI
    });

    const url = 'https://services.leadconnectorhq.com/oauth/token';
    const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    };

    try {
        console.log("INSIDE CALLBACK")
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: data
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const responseData = await response.json();
        console.log("RESPONSE DATA from callback", responseData)
        
        // Ensure database connection before saving data
        try {
            await connectToDB();
        } catch (dbError) {
            console.error('Database connection failed:', dbError.message);
            // Return success response even if database save fails
            // This prevents OAuth flow from breaking due to database issues
            return res.json({ 
                data: responseData, 
                warning: 'Data received but not saved due to database connectivity issues' 
            });
        }

        try {
            if (!responseData.locationId) {
                const agencyData = {
                    access_token: responseData.access_token,
                    refresh_token: responseData.refresh_token,
                    companyId: responseData.companyId,
                    userId: responseData.userId ?? uuidv4(),
                    createdAt: Date.now() / 1000,//in seconds
                    updatedAt: Date.now() / 1000 //in seconds
                };
                await saveAgency(agencyData);
                console.log("Agency data saved successfully");
            } else {
                const userData = {
                    locationId: responseData.locationId,
                    companyId: responseData.companyId,
                    userId: responseData.userId ?? uuidv4(),
                    accessToken: responseData.access_token,
                    refreshToken: responseData.refresh_token,
                    createdAt: Date.now() / 1000, //in seconds
                    updatedAt: Date.now() / 1000 //in seconds
                };
                console.log("USER DATA saved from callback", userData)
                await saveUser(responseData.locationId, userData);
                console.log("User data saved successfully");
            }
        } catch (saveError) {
            console.error('Error saving data to database:', saveError.message);
            // Return success response even if database save fails
            // This prevents OAuth flow from breaking due to database issues
            return res.json({ 
                data: responseData, 
                warning: 'Authentication successful but data not saved due to database issues' 
            });
        }

        return res.json({ data: responseData });
    } catch (error) {
        console.error('Error during token request:', error.message);
        return res.status(500).json({ error: 'Failed to fetch token' });
    }
}
