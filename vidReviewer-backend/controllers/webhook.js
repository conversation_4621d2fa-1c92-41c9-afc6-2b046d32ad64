import User from '../models/user.js';
import Video from '../models/video.js';
import { connectToDB } from '../config/db.js';
import { v4 as uuidv4 } from 'uuid';
import { getJobDetails } from '../services/transcoder.js';

export const handleTranscoderNotification = async (req, res) => {
    try {
        console.log('Received transcoder webhook notification:', JSON.stringify(req.body, null, 2));

        // Handle Pub/Sub push notification format
        let notification;
        if (req.body.message && req.body.message.data) {
            const message = Buffer.from(req.body.message.data, 'base64').toString('utf-8');
            notification = JSON.parse(message);
        } else {
            // Handle direct notification format (if any)
            notification = req.body;
        }

        console.log('Parsed transcoder notification:', JSON.stringify(notification, null, 2));

        if (!notification.job || !notification.job.name) {
            console.error('Invalid notification format received');
            return res.status(400).send('Invalid notification format');
        }

        const jobName = notification.job.name;
        const job = await getJobDetails(jobName);

        const outputUri = job.config.output.uri;
        const videoId = outputUri.split('/hls/')[1].split('/')[0];

        console.log(`Processing transcoder webhook for video: ${videoId}, job state: ${job.state}`);

        if (job.state === 'SUCCEEDED') {
            const hlsPlaylistUrl = `https://storage.googleapis.com/vid-reviewer-bucket/hls/${videoId}/manifest.m3u8`;
            await Video.findByIdAndUpdate(videoId, {
                status: 'complete',
                hlsPlaylistUrl: hlsPlaylistUrl,
            });
            console.log(`✅ Video ${videoId} successfully transcoded and updated via webhook.`);
        } else if (job.state === 'FAILED') {
            await Video.findByIdAndUpdate(videoId, {
                status: 'failed',
                processingError: JSON.stringify(job.error, null, 2),
            });
            console.error(`❌ Transcoding failed for video ${videoId}:`, job.error);
        } else {
            console.log(`📋 Video ${videoId} transcoding job state via webhook: ${job.state}`);
        }

        res.status(204).send();
    } catch (error) {
        console.error('Error processing transcoder notification:', error);
        res.status(500).send();
    }
};

export default async function webhook(req, res) {
    const event = req.body;
    console.log("Event received:", event);

    if (event.type !== 'INSTALL') {
        return res.status(400).json({ error: 'Invalid event type' });
    }
    try {
        const { appId, companyId, locationId } = event;
        const userId = event.userId ?? uuidv4();


        await connectToDB();

        if (event.installType === 'Location') {
            const user = await User.findOne({ companyId: companyId, locationId: locationId });
            console.log("User found:", user);
            if (!user) {
                console.log("USER NOT FOUND, CREATING NEW USER in webhook")
                const newUser = new User({
                    appId,
                    companyId,
                    userId,
                    locationId
                });
                 await User.create(newUser);
            }
        }

    } catch (error) {
        console.error('Error processing webhook:', error.message);
        return res.status(500).json({ error: 'Failed to process webhook' });
    }
}
