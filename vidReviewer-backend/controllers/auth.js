
export default async function auth(req, res) {

    const options = {
        requestType: "code",
        redirectUri: process.env.REDIRECT_URI, 
        clientId: process.env.CLIENT_ID,
        scopes: [
            "contacts.readonly",
            "contacts.write",
            "locations.readonly",
            "conversations/message.write",
            "conversations.write",
            "users.readonly",
            "users.write"
        ]
    };

    res.redirect(`${process.env.BASE_URL}/oauth/chooselocation?response_type=${options.requestType}&redirect_uri=${options.redirectUri}&client_id=${options.clientId}&scope=${options.scopes.join(' ')}&loginWindowOpenMode=self`);
}
