{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,

    /* Aliases */
    "baseUrl": ".",
    "paths": {
      "~app/*": ["src/app/*"],
      "~assets/*": ["src/assets/*"],
      "~components/*": ["src/components/*"],
      "~features/*": ["src/features/*"],
      "~hooks/*": ["src/hooks/*"],
      "~lib/*": ["src/lib/*"],
      "~stores/*": ["src/stores/*"],
      "~types/*": ["src/types/*"],
      "~utils/*": ["src/utils/*"],
      "~/*": ["src/*"]
    }
  },
  "include": ["src"]
}
