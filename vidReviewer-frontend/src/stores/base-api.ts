import {
  // type BaseQueryFn,
  create<PERSON>pi,
  // type <PERSON><PERSON><PERSON><PERSON><PERSON>,
  fetchBaseQuery,
  // type FetchBaseQueryError,
  // retry,
} from '@reduxjs/toolkit/query/react';

// import { Mutex } from 'async-mutex';

// Define the response type for refreshToken
export interface RefreshTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
  scope: string;
  userType: string;
  companyId: string;
  locationId: string;
  userId: string;
}

// interface DataWithStatusCode {
//   statusCode?: number;
// }

// create a new mutex
// const mutex = new Mutex();

// const refreshToken = async (
//   locationId: string,
// ): Promise<RefreshTokenResponse | null> => {
//   try {
//     const response = await fetch(
//       `${import.meta.env.VITE_API_URL}auth/refresh/${locationId}`,
//       {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//       },
//     );

//     if (!response.ok) {
//       throw new Error(`Failed to refresh token: ${response.status}`);
//     }

//     const data = (await response.json()) as RefreshTokenResponse;
//     return data;
//   } catch (error) {
//     console.error(error);
//     return null;
//   }
// };

const baseQuery = fetchBaseQuery({
  baseUrl: import.meta.env.VITE_API_URL,
});

// Extended base query with token refresh logic
// const baseQueryWithTokenRefresh: BaseQueryFn<
//   FetchArgs | string,
//   unknown,
//   FetchBaseQueryError
// > = async (args, api, extraOptions) => {
//   // wait until the mutex is available without locking it
//   await mutex.waitForUnlock();
//   let result = await baseQuery(args, api, extraOptions);

//   if ((result.data as DataWithStatusCode).statusCode === 401) {
//     // checking whether the mutex is locked
//     if (!mutex.isLocked()) {
//       const release = await mutex.acquire();
//       const user = localStorage.getItem('user');
//       if (user !== null) {
//         try {
//           const locationId = (JSON.parse(user) as { locationId: string })
//             .locationId;
//           const data = await refreshToken(locationId);

//           if (data?.access_token) {
//             result = await baseQuery(args, api, extraOptions);
//           }
//         } catch (error) {
//           console.error(error);
//         } finally {
//           // release must be called once the mutex should be released again.
//           release();
//         }
//       }
//     } else {
//       // wait until the mutex is available without locking it
//       await mutex.waitForUnlock();
//       result = await baseQuery(args, api, extraOptions);
//     }
//   }

//   // bail out of re-tries immediately if server error,
//   // because we know successive re-retries probably would be redundant
//   if (result.error?.status && result.error.status === 500) {
//     retry.fail(result.error);
//   }

//   return result;
// };

export const TAGS = {
  PROJECTS: 'projects',
  FOLDERS: 'folders',
  VIDEOS: 'videos',
  COMMENTS: 'comments',
  COLLABORATORS: 'collaborators',
  STORAGE: 'storage',
} as const;

export const baseAPI = createApi({
  reducerPath: 'baseAPI',
  baseQuery,
  tagTypes: Object.values(TAGS),
  endpoints: () => ({}),
});
