/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router';

// Import Routes

import { Route as rootRoute } from './routes/__root';
import { Route as NotAuthenticatedImport } from './routes/not-authenticated';
import { Route as AgencyAccountImport } from './routes/agency-account';
import { Route as LocationRouteImport } from './routes/location/route';
import { Route as CRouteImport } from './routes/c/route';
import { Route as AuthRouteImport } from './routes/_auth/route';
import { Route as SharedTokenRouteImport } from './routes/shared/$token/route';
import { Route as LocationLocationIdRouteImport } from './routes/location/$locationId/route';
import { Route as CCollaborateIdRouteImport } from './routes/c/$collaborateId/route';
import { Route as AuthReviewVideoRouteImport } from './routes/_auth/review-video/route';
import { Route as AuthFoldersRouteImport } from './routes/_auth/folders/route';
import { Route as AuthFolderRouteImport } from './routes/_auth/folder/route';
import { Route as SharedTokenVideoIdRouteImport } from './routes/shared_/$token/$videoId/route';
import { Route as CCollaborateIdVideoRouteImport } from './routes/c/$collaborateId/video/route';
import { Route as CCollaborateIdProjectRouteImport } from './routes/c/$collaborateId/project/route';
import { Route as AuthReviewVideoVideoIdRouteImport } from './routes/_auth/review-video/$videoId/route';
import { Route as AuthFolderFolderIdRouteImport } from './routes/_auth/folder/$folderId/route';
import { Route as CCollaborateIdVideoVideoIdRouteImport } from './routes/c/$collaborateId/video/$videoId/route';
import { Route as CCollaborateIdProjectProjectIdRouteImport } from './routes/c/$collaborateId/project/$projectId/route';

// Create Virtual Routes

const IndexLazyImport = createFileRoute('/')();

// Create/Update Routes

const NotAuthenticatedRoute = NotAuthenticatedImport.update({
  id: '/not-authenticated',
  path: '/not-authenticated',
  getParentRoute: () => rootRoute,
} as any);

const AgencyAccountRoute = AgencyAccountImport.update({
  id: '/agency-account',
  path: '/agency-account',
  getParentRoute: () => rootRoute,
} as any);

const LocationRouteRoute = LocationRouteImport.update({
  id: '/location',
  path: '/location',
  getParentRoute: () => rootRoute,
} as any);

const CRouteRoute = CRouteImport.update({
  id: '/c',
  path: '/c',
  getParentRoute: () => rootRoute,
} as any);

const AuthRouteRoute = AuthRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any).lazy(() => import('./routes/_auth/route.lazy').then((d) => d.Route));

const IndexLazyRoute = IndexLazyImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any).lazy(() => import('./routes/index.lazy').then((d) => d.Route));

const SharedTokenRouteRoute = SharedTokenRouteImport.update({
  id: '/shared/$token',
  path: '/shared/$token',
  getParentRoute: () => rootRoute,
} as any);

const LocationLocationIdRouteRoute = LocationLocationIdRouteImport.update({
  id: '/$locationId',
  path: '/$locationId',
  getParentRoute: () => LocationRouteRoute,
} as any);

const CCollaborateIdRouteRoute = CCollaborateIdRouteImport.update({
  id: '/$collaborateId',
  path: '/$collaborateId',
  getParentRoute: () => CRouteRoute,
} as any).lazy(() =>
  import('./routes/c/$collaborateId/route.lazy').then((d) => d.Route),
);

const AuthReviewVideoRouteRoute = AuthReviewVideoRouteImport.update({
  id: '/review-video',
  path: '/review-video',
  getParentRoute: () => AuthRouteRoute,
} as any);

const AuthFoldersRouteRoute = AuthFoldersRouteImport.update({
  id: '/folders',
  path: '/folders',
  getParentRoute: () => AuthRouteRoute,
} as any).lazy(() =>
  import('./routes/_auth/folders/route.lazy').then((d) => d.Route),
);

const AuthFolderRouteRoute = AuthFolderRouteImport.update({
  id: '/folder',
  path: '/folder',
  getParentRoute: () => AuthRouteRoute,
} as any).lazy(() =>
  import('./routes/_auth/folder/route.lazy').then((d) => d.Route),
);

const SharedTokenVideoIdRouteRoute = SharedTokenVideoIdRouteImport.update({
  id: '/shared_/$token/$videoId',
  path: '/shared/$token/$videoId',
  getParentRoute: () => rootRoute,
} as any);

const CCollaborateIdVideoRouteRoute = CCollaborateIdVideoRouteImport.update({
  id: '/video',
  path: '/video',
  getParentRoute: () => CCollaborateIdRouteRoute,
} as any);

const CCollaborateIdProjectRouteRoute = CCollaborateIdProjectRouteImport.update(
  {
    id: '/project',
    path: '/project',
    getParentRoute: () => CCollaborateIdRouteRoute,
  } as any,
);

const AuthReviewVideoVideoIdRouteRoute =
  AuthReviewVideoVideoIdRouteImport.update({
    id: '/$videoId',
    path: '/$videoId',
    getParentRoute: () => AuthReviewVideoRouteRoute,
  } as any);

const AuthFolderFolderIdRouteRoute = AuthFolderFolderIdRouteImport.update({
  id: '/$folderId',
  path: '/$folderId',
  getParentRoute: () => AuthFolderRouteRoute,
} as any).lazy(() =>
  import('./routes/_auth/folder/$folderId/route.lazy').then((d) => d.Route),
);

const CCollaborateIdVideoVideoIdRouteRoute =
  CCollaborateIdVideoVideoIdRouteImport.update({
    id: '/$videoId',
    path: '/$videoId',
    getParentRoute: () => CCollaborateIdVideoRouteRoute,
  } as any);

const CCollaborateIdProjectProjectIdRouteRoute =
  CCollaborateIdProjectProjectIdRouteImport.update({
    id: '/$projectId',
    path: '/$projectId',
    getParentRoute: () => CCollaborateIdProjectRouteRoute,
  } as any);

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/';
      path: '/';
      fullPath: '/';
      preLoaderRoute: typeof IndexLazyImport;
      parentRoute: typeof rootRoute;
    };
    '/_auth': {
      id: '/_auth';
      path: '';
      fullPath: '';
      preLoaderRoute: typeof AuthRouteImport;
      parentRoute: typeof rootRoute;
    };
    '/c': {
      id: '/c';
      path: '/c';
      fullPath: '/c';
      preLoaderRoute: typeof CRouteImport;
      parentRoute: typeof rootRoute;
    };
    '/location': {
      id: '/location';
      path: '/location';
      fullPath: '/location';
      preLoaderRoute: typeof LocationRouteImport;
      parentRoute: typeof rootRoute;
    };
    '/agency-account': {
      id: '/agency-account';
      path: '/agency-account';
      fullPath: '/agency-account';
      preLoaderRoute: typeof AgencyAccountImport;
      parentRoute: typeof rootRoute;
    };
    '/not-authenticated': {
      id: '/not-authenticated';
      path: '/not-authenticated';
      fullPath: '/not-authenticated';
      preLoaderRoute: typeof NotAuthenticatedImport;
      parentRoute: typeof rootRoute;
    };
    '/_auth/folder': {
      id: '/_auth/folder';
      path: '/folder';
      fullPath: '/folder';
      preLoaderRoute: typeof AuthFolderRouteImport;
      parentRoute: typeof AuthRouteImport;
    };
    '/_auth/folders': {
      id: '/_auth/folders';
      path: '/folders';
      fullPath: '/folders';
      preLoaderRoute: typeof AuthFoldersRouteImport;
      parentRoute: typeof AuthRouteImport;
    };
    '/_auth/review-video': {
      id: '/_auth/review-video';
      path: '/review-video';
      fullPath: '/review-video';
      preLoaderRoute: typeof AuthReviewVideoRouteImport;
      parentRoute: typeof AuthRouteImport;
    };
    '/c/$collaborateId': {
      id: '/c/$collaborateId';
      path: '/$collaborateId';
      fullPath: '/c/$collaborateId';
      preLoaderRoute: typeof CCollaborateIdRouteImport;
      parentRoute: typeof CRouteImport;
    };
    '/location/$locationId': {
      id: '/location/$locationId';
      path: '/$locationId';
      fullPath: '/location/$locationId';
      preLoaderRoute: typeof LocationLocationIdRouteImport;
      parentRoute: typeof LocationRouteImport;
    };
    '/shared/$token': {
      id: '/shared/$token';
      path: '/shared/$token';
      fullPath: '/shared/$token';
      preLoaderRoute: typeof SharedTokenRouteImport;
      parentRoute: typeof rootRoute;
    };
    '/_auth/folder/$folderId': {
      id: '/_auth/folder/$folderId';
      path: '/$folderId';
      fullPath: '/folder/$folderId';
      preLoaderRoute: typeof AuthFolderFolderIdRouteImport;
      parentRoute: typeof AuthFolderRouteImport;
    };
    '/_auth/review-video/$videoId': {
      id: '/_auth/review-video/$videoId';
      path: '/$videoId';
      fullPath: '/review-video/$videoId';
      preLoaderRoute: typeof AuthReviewVideoVideoIdRouteImport;
      parentRoute: typeof AuthReviewVideoRouteImport;
    };
    '/c/$collaborateId/project': {
      id: '/c/$collaborateId/project';
      path: '/project';
      fullPath: '/c/$collaborateId/project';
      preLoaderRoute: typeof CCollaborateIdProjectRouteImport;
      parentRoute: typeof CCollaborateIdRouteImport;
    };
    '/c/$collaborateId/video': {
      id: '/c/$collaborateId/video';
      path: '/video';
      fullPath: '/c/$collaborateId/video';
      preLoaderRoute: typeof CCollaborateIdVideoRouteImport;
      parentRoute: typeof CCollaborateIdRouteImport;
    };
    '/shared_/$token/$videoId': {
      id: '/shared_/$token/$videoId';
      path: '/shared/$token/$videoId';
      fullPath: '/shared/$token/$videoId';
      preLoaderRoute: typeof SharedTokenVideoIdRouteImport;
      parentRoute: typeof rootRoute;
    };
    '/c/$collaborateId/project/$projectId': {
      id: '/c/$collaborateId/project/$projectId';
      path: '/$projectId';
      fullPath: '/c/$collaborateId/project/$projectId';
      preLoaderRoute: typeof CCollaborateIdProjectProjectIdRouteImport;
      parentRoute: typeof CCollaborateIdProjectRouteImport;
    };
    '/c/$collaborateId/video/$videoId': {
      id: '/c/$collaborateId/video/$videoId';
      path: '/$videoId';
      fullPath: '/c/$collaborateId/video/$videoId';
      preLoaderRoute: typeof CCollaborateIdVideoVideoIdRouteImport;
      parentRoute: typeof CCollaborateIdVideoRouteImport;
    };
  }
}

// Create and export the route tree

interface AuthFolderRouteRouteChildren {
  AuthFolderFolderIdRouteRoute: typeof AuthFolderFolderIdRouteRoute;
}

const AuthFolderRouteRouteChildren: AuthFolderRouteRouteChildren = {
  AuthFolderFolderIdRouteRoute: AuthFolderFolderIdRouteRoute,
};

const AuthFolderRouteRouteWithChildren = AuthFolderRouteRoute._addFileChildren(
  AuthFolderRouteRouteChildren,
);

interface AuthReviewVideoRouteRouteChildren {
  AuthReviewVideoVideoIdRouteRoute: typeof AuthReviewVideoVideoIdRouteRoute;
}

const AuthReviewVideoRouteRouteChildren: AuthReviewVideoRouteRouteChildren = {
  AuthReviewVideoVideoIdRouteRoute: AuthReviewVideoVideoIdRouteRoute,
};

const AuthReviewVideoRouteRouteWithChildren =
  AuthReviewVideoRouteRoute._addFileChildren(AuthReviewVideoRouteRouteChildren);

interface AuthRouteRouteChildren {
  AuthFolderRouteRoute: typeof AuthFolderRouteRouteWithChildren;
  AuthFoldersRouteRoute: typeof AuthFoldersRouteRoute;
  AuthReviewVideoRouteRoute: typeof AuthReviewVideoRouteRouteWithChildren;
}

const AuthRouteRouteChildren: AuthRouteRouteChildren = {
  AuthFolderRouteRoute: AuthFolderRouteRouteWithChildren,
  AuthFoldersRouteRoute: AuthFoldersRouteRoute,
  AuthReviewVideoRouteRoute: AuthReviewVideoRouteRouteWithChildren,
};

const AuthRouteRouteWithChildren = AuthRouteRoute._addFileChildren(
  AuthRouteRouteChildren,
);

interface CCollaborateIdProjectRouteRouteChildren {
  CCollaborateIdProjectProjectIdRouteRoute: typeof CCollaborateIdProjectProjectIdRouteRoute;
}

const CCollaborateIdProjectRouteRouteChildren: CCollaborateIdProjectRouteRouteChildren =
  {
    CCollaborateIdProjectProjectIdRouteRoute:
      CCollaborateIdProjectProjectIdRouteRoute,
  };

const CCollaborateIdProjectRouteRouteWithChildren =
  CCollaborateIdProjectRouteRoute._addFileChildren(
    CCollaborateIdProjectRouteRouteChildren,
  );

interface CCollaborateIdVideoRouteRouteChildren {
  CCollaborateIdVideoVideoIdRouteRoute: typeof CCollaborateIdVideoVideoIdRouteRoute;
}

const CCollaborateIdVideoRouteRouteChildren: CCollaborateIdVideoRouteRouteChildren =
  {
    CCollaborateIdVideoVideoIdRouteRoute: CCollaborateIdVideoVideoIdRouteRoute,
  };

const CCollaborateIdVideoRouteRouteWithChildren =
  CCollaborateIdVideoRouteRoute._addFileChildren(
    CCollaborateIdVideoRouteRouteChildren,
  );

interface CCollaborateIdRouteRouteChildren {
  CCollaborateIdProjectRouteRoute: typeof CCollaborateIdProjectRouteRouteWithChildren;
  CCollaborateIdVideoRouteRoute: typeof CCollaborateIdVideoRouteRouteWithChildren;
}

const CCollaborateIdRouteRouteChildren: CCollaborateIdRouteRouteChildren = {
  CCollaborateIdProjectRouteRoute: CCollaborateIdProjectRouteRouteWithChildren,
  CCollaborateIdVideoRouteRoute: CCollaborateIdVideoRouteRouteWithChildren,
};

const CCollaborateIdRouteRouteWithChildren =
  CCollaborateIdRouteRoute._addFileChildren(CCollaborateIdRouteRouteChildren);

interface CRouteRouteChildren {
  CCollaborateIdRouteRoute: typeof CCollaborateIdRouteRouteWithChildren;
}

const CRouteRouteChildren: CRouteRouteChildren = {
  CCollaborateIdRouteRoute: CCollaborateIdRouteRouteWithChildren,
};

const CRouteRouteWithChildren =
  CRouteRoute._addFileChildren(CRouteRouteChildren);

interface LocationRouteRouteChildren {
  LocationLocationIdRouteRoute: typeof LocationLocationIdRouteRoute;
}

const LocationRouteRouteChildren: LocationRouteRouteChildren = {
  LocationLocationIdRouteRoute: LocationLocationIdRouteRoute,
};

const LocationRouteRouteWithChildren = LocationRouteRoute._addFileChildren(
  LocationRouteRouteChildren,
);

export interface FileRoutesByFullPath {
  '/': typeof IndexLazyRoute;
  '': typeof AuthRouteRouteWithChildren;
  '/c': typeof CRouteRouteWithChildren;
  '/location': typeof LocationRouteRouteWithChildren;
  '/agency-account': typeof AgencyAccountRoute;
  '/not-authenticated': typeof NotAuthenticatedRoute;
  '/folder': typeof AuthFolderRouteRouteWithChildren;
  '/folders': typeof AuthFoldersRouteRoute;
  '/review-video': typeof AuthReviewVideoRouteRouteWithChildren;
  '/c/$collaborateId': typeof CCollaborateIdRouteRouteWithChildren;
  '/location/$locationId': typeof LocationLocationIdRouteRoute;
  '/shared/$token': typeof SharedTokenRouteRoute;
  '/folder/$folderId': typeof AuthFolderFolderIdRouteRoute;
  '/review-video/$videoId': typeof AuthReviewVideoVideoIdRouteRoute;
  '/c/$collaborateId/project': typeof CCollaborateIdProjectRouteRouteWithChildren;
  '/c/$collaborateId/video': typeof CCollaborateIdVideoRouteRouteWithChildren;
  '/shared/$token/$videoId': typeof SharedTokenVideoIdRouteRoute;
  '/c/$collaborateId/project/$projectId': typeof CCollaborateIdProjectProjectIdRouteRoute;
  '/c/$collaborateId/video/$videoId': typeof CCollaborateIdVideoVideoIdRouteRoute;
}

export interface FileRoutesByTo {
  '/': typeof IndexLazyRoute;
  '': typeof AuthRouteRouteWithChildren;
  '/c': typeof CRouteRouteWithChildren;
  '/location': typeof LocationRouteRouteWithChildren;
  '/agency-account': typeof AgencyAccountRoute;
  '/not-authenticated': typeof NotAuthenticatedRoute;
  '/folder': typeof AuthFolderRouteRouteWithChildren;
  '/folders': typeof AuthFoldersRouteRoute;
  '/review-video': typeof AuthReviewVideoRouteRouteWithChildren;
  '/c/$collaborateId': typeof CCollaborateIdRouteRouteWithChildren;
  '/location/$locationId': typeof LocationLocationIdRouteRoute;
  '/shared/$token': typeof SharedTokenRouteRoute;
  '/folder/$folderId': typeof AuthFolderFolderIdRouteRoute;
  '/review-video/$videoId': typeof AuthReviewVideoVideoIdRouteRoute;
  '/c/$collaborateId/project': typeof CCollaborateIdProjectRouteRouteWithChildren;
  '/c/$collaborateId/video': typeof CCollaborateIdVideoRouteRouteWithChildren;
  '/shared/$token/$videoId': typeof SharedTokenVideoIdRouteRoute;
  '/c/$collaborateId/project/$projectId': typeof CCollaborateIdProjectProjectIdRouteRoute;
  '/c/$collaborateId/video/$videoId': typeof CCollaborateIdVideoVideoIdRouteRoute;
}

export interface FileRoutesById {
  __root__: typeof rootRoute;
  '/': typeof IndexLazyRoute;
  '/_auth': typeof AuthRouteRouteWithChildren;
  '/c': typeof CRouteRouteWithChildren;
  '/location': typeof LocationRouteRouteWithChildren;
  '/agency-account': typeof AgencyAccountRoute;
  '/not-authenticated': typeof NotAuthenticatedRoute;
  '/_auth/folder': typeof AuthFolderRouteRouteWithChildren;
  '/_auth/folders': typeof AuthFoldersRouteRoute;
  '/_auth/review-video': typeof AuthReviewVideoRouteRouteWithChildren;
  '/c/$collaborateId': typeof CCollaborateIdRouteRouteWithChildren;
  '/location/$locationId': typeof LocationLocationIdRouteRoute;
  '/shared/$token': typeof SharedTokenRouteRoute;
  '/_auth/folder/$folderId': typeof AuthFolderFolderIdRouteRoute;
  '/_auth/review-video/$videoId': typeof AuthReviewVideoVideoIdRouteRoute;
  '/c/$collaborateId/project': typeof CCollaborateIdProjectRouteRouteWithChildren;
  '/c/$collaborateId/video': typeof CCollaborateIdVideoRouteRouteWithChildren;
  '/shared_/$token/$videoId': typeof SharedTokenVideoIdRouteRoute;
  '/c/$collaborateId/project/$projectId': typeof CCollaborateIdProjectProjectIdRouteRoute;
  '/c/$collaborateId/video/$videoId': typeof CCollaborateIdVideoVideoIdRouteRoute;
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | '/'
    | ''
    | '/c'
    | '/location'
    | '/agency-account'
    | '/not-authenticated'
    | '/folder'
    | '/folders'
    | '/review-video'
    | '/c/$collaborateId'
    | '/location/$locationId'
    | '/shared/$token'
    | '/folder/$folderId'
    | '/review-video/$videoId'
    | '/c/$collaborateId/project'
    | '/c/$collaborateId/video'
    | '/shared/$token/$videoId'
    | '/c/$collaborateId/project/$projectId'
    | '/c/$collaborateId/video/$videoId';
  fileRoutesByTo: FileRoutesByTo;
  to:
    | '/'
    | ''
    | '/c'
    | '/location'
    | '/agency-account'
    | '/not-authenticated'
    | '/folder'
    | '/folders'
    | '/review-video'
    | '/c/$collaborateId'
    | '/location/$locationId'
    | '/shared/$token'
    | '/folder/$folderId'
    | '/review-video/$videoId'
    | '/c/$collaborateId/project'
    | '/c/$collaborateId/video'
    | '/shared/$token/$videoId'
    | '/c/$collaborateId/project/$projectId'
    | '/c/$collaborateId/video/$videoId';
  id:
    | '__root__'
    | '/'
    | '/_auth'
    | '/c'
    | '/location'
    | '/agency-account'
    | '/not-authenticated'
    | '/_auth/folder'
    | '/_auth/folders'
    | '/_auth/review-video'
    | '/c/$collaborateId'
    | '/location/$locationId'
    | '/shared/$token'
    | '/_auth/folder/$folderId'
    | '/_auth/review-video/$videoId'
    | '/c/$collaborateId/project'
    | '/c/$collaborateId/video'
    | '/shared_/$token/$videoId'
    | '/c/$collaborateId/project/$projectId'
    | '/c/$collaborateId/video/$videoId';
  fileRoutesById: FileRoutesById;
}

export interface RootRouteChildren {
  IndexLazyRoute: typeof IndexLazyRoute;
  AuthRouteRoute: typeof AuthRouteRouteWithChildren;
  CRouteRoute: typeof CRouteRouteWithChildren;
  LocationRouteRoute: typeof LocationRouteRouteWithChildren;
  AgencyAccountRoute: typeof AgencyAccountRoute;
  NotAuthenticatedRoute: typeof NotAuthenticatedRoute;
  SharedTokenRouteRoute: typeof SharedTokenRouteRoute;
  SharedTokenVideoIdRouteRoute: typeof SharedTokenVideoIdRouteRoute;
}

const rootRouteChildren: RootRouteChildren = {
  IndexLazyRoute: IndexLazyRoute,
  AuthRouteRoute: AuthRouteRouteWithChildren,
  CRouteRoute: CRouteRouteWithChildren,
  LocationRouteRoute: LocationRouteRouteWithChildren,
  AgencyAccountRoute: AgencyAccountRoute,
  NotAuthenticatedRoute: NotAuthenticatedRoute,
  SharedTokenRouteRoute: SharedTokenRouteRoute,
  SharedTokenVideoIdRouteRoute: SharedTokenVideoIdRouteRoute,
};

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_auth",
        "/c",
        "/location",
        "/agency-account",
        "/not-authenticated",
        "/shared/$token",
        "/shared_/$token/$videoId"
      ]
    },
    "/": {
      "filePath": "index.lazy.tsx"
    },
    "/_auth": {
      "filePath": "_auth/route.tsx",
      "children": [
        "/_auth/folder",
        "/_auth/folders",
        "/_auth/review-video"
      ]
    },
    "/c": {
      "filePath": "c/route.tsx",
      "children": [
        "/c/$collaborateId"
      ]
    },
    "/location": {
      "filePath": "location/route.tsx",
      "children": [
        "/location/$locationId"
      ]
    },
    "/agency-account": {
      "filePath": "agency-account.tsx"
    },
    "/not-authenticated": {
      "filePath": "not-authenticated.tsx"
    },
    "/_auth/folder": {
      "filePath": "_auth/folder/route.tsx",
      "parent": "/_auth",
      "children": [
        "/_auth/folder/$folderId"
      ]
    },
    "/_auth/folders": {
      "filePath": "_auth/folders/route.tsx",
      "parent": "/_auth"
    },
    "/_auth/review-video": {
      "filePath": "_auth/review-video/route.tsx",
      "parent": "/_auth",
      "children": [
        "/_auth/review-video/$videoId"
      ]
    },
    "/c/$collaborateId": {
      "filePath": "c/$collaborateId/route.tsx",
      "parent": "/c",
      "children": [
        "/c/$collaborateId/project",
        "/c/$collaborateId/video"
      ]
    },
    "/location/$locationId": {
      "filePath": "location/$locationId/route.tsx",
      "parent": "/location"
    },
    "/shared/$token": {
      "filePath": "shared/$token/route.tsx"
    },
    "/_auth/folder/$folderId": {
      "filePath": "_auth/folder/$folderId/route.tsx",
      "parent": "/_auth/folder"
    },
    "/_auth/review-video/$videoId": {
      "filePath": "_auth/review-video/$videoId/route.tsx",
      "parent": "/_auth/review-video"
    },
    "/c/$collaborateId/project": {
      "filePath": "c/$collaborateId/project/route.tsx",
      "parent": "/c/$collaborateId",
      "children": [
        "/c/$collaborateId/project/$projectId"
      ]
    },
    "/c/$collaborateId/video": {
      "filePath": "c/$collaborateId/video/route.tsx",
      "parent": "/c/$collaborateId",
      "children": [
        "/c/$collaborateId/video/$videoId"
      ]
    },
    "/shared_/$token/$videoId": {
      "filePath": "shared_/$token/$videoId/route.tsx"
    },
    "/c/$collaborateId/project/$projectId": {
      "filePath": "c/$collaborateId/project/$projectId/route.tsx",
      "parent": "/c/$collaborateId/project"
    },
    "/c/$collaborateId/video/$videoId": {
      "filePath": "c/$collaborateId/video/$videoId/route.tsx",
      "parent": "/c/$collaborateId/video"
    }
  }
}
ROUTE_MANIFEST_END */
