import { useNavigate } from '@tanstack/react-router';
import { format } from 'date-fns';
import { Bell, CheckCircle, Download, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import CommentSidebar from '~components/custom/comment-sidebar';
import { VideoPlayerForExternalUser } from '~components/custom/video-player-for-external-user';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~components/ui/alert-dialog';
import { Button } from '~components/ui/button';
import { Progress } from '~components/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~components/ui/tooltip';
import { cn } from '~lib/utils';

import { useGetCommentsQuery } from '~features/comment/api/get-comments';
import { useNotifyCollaboratorsMutation } from '~features/notification/api/notify-collaborators';
import { useGetSharedResourceQuery } from '~features/sharing/api/get-shared-resource';
import { useDownloadVideoMutation } from '~features/video/api/download-video';
import { useGetVideoQuery } from '~features/video/api/get-video';
import { getEffectiveThumbnailUrl } from '~features/video/utils/thumbnail-utils';

interface SharedVideoViewProps {
  token: string;
  videoId: string;
}

export const SharedVideoView = ({ token, videoId }: SharedVideoViewProps) => {
  const navigate = useNavigate();

  const { data: sharedResource, error } = useGetSharedResourceQuery(token);

  const { data, isFetching: isVideoFetching } = useGetVideoQuery({ videoId });

  const { data: comments } = useGetCommentsQuery({ videoId });

  const [downloadVideo] = useDownloadVideoMutation();
  const [notifyCollaborators, { isLoading }] = useNotifyCollaboratorsMutation();

  const [videoProgress, setVideoProgress] = useState(0);
  const [newCommentAdded, setNewCommentAdded] = useState(false);
  const [isNotified, setIsNotified] = useState(false);
  const [isAnimating, _setIsAnimating] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [downloadAbortController, setDownloadAbortController] =
    useState<AbortController | null>(null);

  const handleDownload = async () => {
    let toastId: string | number | undefined;

    if (!data) {
      toast.error('Failed to download video');
      return;
    }

    try {
      // Create abort controller for cancellation
      const abortController = new AbortController();
      setDownloadAbortController(abortController);

      toastId = toast.loading('Preparing download...', {
        duration: Number.POSITIVE_INFINITY,
      });

      const response = await downloadVideo({
        videoPath: data.videoUrl,
      }).unwrap();

      const videoResponse = await fetch(response.signedUrl, {
        signal: abortController.signal,
      });

      if (!videoResponse.ok) {
        toast.error('Failed to download video');
        throw new Error('Download failed');
      }

      // Extract content length for progress tracking
      const contentLength = videoResponse.headers.get('Content-Length');
      if (!contentLength) throw new Error('Unable to determine file size');

      const totalSize = parseInt(contentLength, 10);
      let loadedSize = 0;

      // Create a stream reader to track progress
      const reader = videoResponse.body?.getReader();
      const chunks: Array<Uint8Array> = [];

      const updateToast = (progress: number) => {
        toast.custom(
          () => (
            <div
              className={`pointer-events-auto flex w-96 rounded-lg bg-white shadow-lg ring-1 ring-black/5`}
            >
              <div className='flex-1 p-4'>
                <div className='flex items-start'>
                  <div className='shrink-0 pt-0.5'>
                    <Download className='size-10 text-blue-600' />
                  </div>
                  <div className='ml-3 flex-1'>
                    <p className='text-sm font-medium text-gray-900'>
                      Downloading video...
                    </p>
                    <p className='mt-1 text-sm text-gray-500'>
                      {progress}% complete
                    </p>
                    <div className='mt-2'>
                      <Progress value={progress} />
                    </div>
                    <div className='mt-3 flex justify-end'>
                      <Button
                        onClick={() => {
                          abortController.abort();
                          setDownloadAbortController(null);
                          toast.dismiss(toastId);
                          toast.info('Download cancelled');
                        }}
                        variant='outline'
                        size='sm'
                        className='h-8 px-3 text-xs'
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ),
          {
            id: toastId,
            duration: Number.POSITIVE_INFINITY,
          },
        );
      };

      while (reader) {
        // Check if download was cancelled
        if (abortController.signal.aborted) {
          await reader.cancel();
          throw new DOMException('Download cancelled', 'AbortError');
        }

        const { done, value } = await reader.read();
        if (done) break;

        chunks.push(value);
        loadedSize += value.length;

        // Calculate and display progress
        const progress = Math.round((loadedSize / totalSize) * 100);

        updateToast(progress);
      }

      const blob = new Blob(chunks);

      // const blob = await videoResponse.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download =
        data.title + '.' + (data.videoUrl.split('.').pop() ?? 'mp4');
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      // Cleanup
      setDownloadAbortController(null);

      // Dismiss the download progress toast
      toast.dismiss(toastId);

      // Show a new success toast
      toast.custom(
        () => (
          <div
            className={`pointer-events-auto flex w-96 rounded-lg bg-white shadow-lg ring-1 ring-black/5`}
          >
            <div className='flex-1 p-4'>
              <div className='flex items-start'>
                <div className='shrink-0 pt-0.5'>
                  <CheckCircle className='size-10 text-green-600' />
                </div>
                <div className='ml-3 flex-1'>
                  <p className='text-sm font-medium text-gray-900'>
                    Download complete!
                  </p>
                  <p className='mt-1 text-sm text-gray-500'>
                    Your video has been downloaded successfully.
                  </p>
                </div>
              </div>
            </div>
          </div>
        ),
        { duration: 3000 }, // This toast will auto-close after 3 seconds
      );
    } catch (error) {
      setDownloadAbortController(null);
      console.error(error);
      // Check if it was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        // Download was cancelled, toast.dismiss was already called in cancelDownload
        return;
      }
      toast.error('An error occurred during download', { id: toastId });
    }
  };

  const notify = async () => {
    if (!sharedResource) {
      toast.error('Failed to notify collaborators');
      return;
    }

    try {
      const response = await notifyCollaborators({
        videoId,
        link: window.location.href,
        collaboratorName: 'Anonymous',
      }).unwrap();
      if (response.message === 'Contact ID not found') {
        // await refreshAgencyToken({ companyId: 'DSKcIUTONZ2lAf0Q4hvQ' });
        await notifyCollaborators({
          videoId,
          link: window.location.href,
          collaboratorName: 'Anonymous',
        });
        setIsNotified(true);
        toast.success('Video editor notified');
      } else {
        setIsNotified(true);
        toast.success('Video editor notified');
      }
    } catch (error) {
      console.error(error);
      toast.error('Failed to notify editor');
    }
  };

  // const goBack = () => {
  //   if (newCommentAdded && !isNotified) {
  //     setShowAlert(true);
  //   } else {
  //     void navigate({
  //       to: '/shared/$token',
  //       params: { token },
  //     });
  //   }
  // };

  if (isVideoFetching) {
    return (
      <div className='flex h-screen w-full items-center justify-center'>
        <Loader2 size={48} className='animate-spin' />
      </div>
    );
  }

  if (error) return <div>Error occurred</div>;

  return (
    <>
      <div className='flex flex-col'>
        <div className='flex h-16 items-center justify-between gap-1 border-b px-1 sm:px-6'>
          <div className='flex items-center gap-1 sm:gap-4'>
            {/* <Button
              type='button'
              onClick={goBack}
              variant={'ghost'}
              className='h-fit bg-transparent p-0 text-black hover:bg-transparent [&_svg]:size-6'
            >
              <ArrowLeft />
            </Button> */}
            <h1 className='text-2xl font-bold'>{data?.title}</h1>
          </div>
          <div className='flex items-center gap-1 sm:gap-4'>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Button
                    type='button'
                    variant='secondary'
                    className='bg-[#DC2625] text-sm font-semibold text-white hover:bg-[#DC2625]'
                    onClick={notify}
                  >
                    {isLoading ? (
                      <Loader2 className='animate-spin' />
                    ) : (
                      <div
                        className={cn(
                          'inline-block',
                          isAnimating && 'animate-tilt',
                        )}
                      >
                        <Bell />
                      </div>
                    )}
                    <span className='hidden sm:inline-block'>
                      {isLoading ? 'Notifying' : 'Notify'}
                    </span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Notify Video Editor</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {data?.isDownloadable && (
              <Button
                type='button'
                variant='secondary'
                className='bg-[#DC2625] text-sm font-semibold text-white hover:bg-[#DC2625]'
                onClick={handleDownload}
                disabled={downloadAbortController !== null}
              >
                <Download />
                <span className='hidden sm:inline-block'>
                  {downloadAbortController ? 'Downloading...' : 'Download'}
                </span>
              </Button>
            )}
            <p className='hidden sm:inline-block'>
              <strong>Shared:</strong>{' '}
              {format(new Date(data?.uploadedOn ?? ''), 'MMM dd, yyyy')}
            </p>
          </div>
        </div>
        <div className='container mx-auto min-h-screen px-2 py-6 sm:px-4'>
          <div className='grid grid-cols-1 gap-6 lg:grid-cols-3'>
            <div className='space-y-4 lg:col-span-2'>
              <div className='space-y-4'>
                {data && (
                  <VideoPlayerForExternalUser
                    hlsPlaylistUrl={data.hlsPlaylistUrl}
                    videoUrl={data.videoUrl}
                    videoId={videoId}
                    videoProgress={videoProgress}
                    thumbnailUrl={getEffectiveThumbnailUrl(data)}
                    newCommentAdded={newCommentAdded}
                    setNewCommentAdded={setNewCommentAdded}
                    isNotified={isNotified}
                    isCommentsEnabled={data.isCommentsEnabled}
                  />
                )}
              </div>
              {/* <VideoTimeline videoSrc={videoSrc} /> */}
            </div>
            {data?.isCommentsEnabled ? (
              <div className='space-y-6'>
                <CommentSidebar
                  comments={comments?.comments ?? []}
                  isExternalUser={true}
                  setVideoProgress={setVideoProgress}
                />
              </div>
            ) : (
              <div className='flex h-full items-center justify-center'>
                <div className='text-center'>
                  <div className='mb-4 text-6xl opacity-20'>💬</div>
                  <h3 className='mb-2 text-lg font-semibold text-gray-600'>
                    Comments are disabled
                  </h3>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <AlertDialog open={showAlert} onOpenChange={setShowAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              You want to leave without notifying the editor.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setShowAlert(false);
              }}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                void navigate({
                  to: '/shared/$token',
                  params: {
                    token,
                  },
                });
              }}
            >
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
