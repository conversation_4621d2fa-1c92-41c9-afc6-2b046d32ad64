import { useParams } from '@tanstack/react-router';
import Header from '~components/custom/header';

import { FolderNavigation } from '~features/folder/components/folder-navigation';

export const FolderContents = () => {
  const { folderId } = useParams({ from: '/_auth/folder/$folderId' });
  const isRoot = folderId === 'root';

  return (
    <>
      <Header
        title={isRoot ? 'Home' : 'Folder Contents'}
        folderId={folderId}
        showFolderActions={true}
        isRootPage={isRoot}
        shareResourceType={isRoot ? undefined : "folder"}
        shareResourceId={isRoot ? undefined : folderId}
        shareResourceTitle={isRoot ? undefined : 'Folder Contents'}
      />
      <div className="p-4 pt-0 sm:p-10 sm:pt-0">
        <FolderNavigation
          initialFolderId={folderId}
        />
      </div>
    </>
  );
};
