import { useNavigate, useParams } from '@tanstack/react-router';
import { Loader2 } from 'lucide-react';
import Header from '~components/custom/header';

import { useGetFolderQuery } from '~features/folder/api/get-folder';
import { useGetProjectQuery } from '~features/project/api/get-project';
import { FolderNavigation } from '~features/folder/components/folder-navigation';
import { ProjectDetailsWithSubProjects } from '~features/sub-project/components/project-details-with-sub-projects';
import { SubProjectDetails } from '~features/sub-project/components/sub-project-details';

export const FolderOrProjectDetails = () => {
  const { folderId } = useParams({ from: '/_auth/folder/$folderId' });
  const navigate = useNavigate();

  // Try to fetch as folder first
  const { data: folderData, isLoading: isFolderLoading, error: folderError } = useGetFolderQuery(
    { folderId },
    { skip: !folderId }
  );

  // Try to fetch as project if folder fails
  const { data: projectData, isLoading: isProjectLoading, error: projectError } = useGetProjectQuery(
    { projectId: folderId },
    { skip: !folderId || (!folderError && !isFolderLoading) }
  );

  // Handle back navigation for folders
  const handleFolderBackNavigation = () => {
    if (folderData?.parentFolder) {
      // Navigate to parent folder
      void navigate({ to: '/folder/$folderId', params: { folderId: folderData.parentFolder } });
    } else {
      // Navigate to home if no parent folder
      void navigate({ to: '/folders' });
    }
  };

  // Show loading while determining type
  if (isFolderLoading || isProjectLoading) {
    return (
      <>
        <Header title="Loading..." />
        <div className="flex h-[calc(100vh-64px)] w-full items-center justify-center">
          <Loader2 size={48} className="animate-spin" />
        </div>
      </>
    );
  }

  // If it's a folder, check if it's a sub-project (contextType: 'project') or regular folder
  if (folderData && !folderError) {
    // If it's a sub-project (folder with contextType: 'project'), show sub-project details
    if (folderData.contextType === 'project' && folderData.contextId) {
      return <SubProjectDetails subProjectId={folderId} projectId={folderData.contextId} />;
    }

    // Otherwise, show regular folder contents
    return (
      <>
        <Header
          title={folderData.name}
          folderId={folderId}
          showFolderActions={true}
          onBackClick={handleFolderBackNavigation}
          shareResourceType="folder"
          shareResourceId={folderId}
          shareResourceTitle={folderData.name}
        />
        <div className="p-4 pt-0 sm:p-10 sm:pt-0">
          <FolderNavigation initialFolderId={folderId} />
        </div>
      </>
    );
  }

  // If it's a project, show project details with sub-projects
  if (projectData && !projectError) {
    return <ProjectDetailsWithSubProjects />;
  }

  // If neither found, show error
  return (
    <>
      <Header title="Not Found" />
      <div className="flex h-[calc(100vh-64px)] w-full items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Not Found</h2>
          <p className="text-gray-600">The folder or project you're looking for doesn't exist.</p>
        </div>
      </div>
    </>
  );
};
