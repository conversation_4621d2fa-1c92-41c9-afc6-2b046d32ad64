import { Link, useParams } from '@tanstack/react-router';
import { format } from 'date-fns';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useState } from 'react';
import CommentSidebar from '~components/custom/comment-sidebar';
import CustomVideoPlayer from '~components/custom/new-video-player';

import { useGetCommentsQuery } from '~features/comment/api/get-comments';
import { useGetVideoQuery } from '~features/video/api/get-video';
import { getEffectiveThumbnailUrl } from '~features/video/utils/thumbnail-utils';

export default function ReviewVideo() {
  const { videoId } = useParams({ from: '/_auth/review-video/$videoId' });

  const { data, isFetching } = useGetVideoQuery({ videoId });

  const { data: comments } = useGetCommentsQuery({ videoId });

  const [videoProgress, setVideoProgress] = useState(0);

  if (isFetching) {
    return (
      <div className='flex h-screen w-full items-center justify-center'>
        <Loader2 size={48} className='animate-spin' />
      </div>
    );
  }

  if (!data) {
    return <div>Video not found</div>;
  }

  return (
    <div className='flex flex-col'>
      <div className='flex h-16 items-center justify-between gap-1 border-b px-1 sm:px-6'>
        <div className='flex items-center gap-1 sm:gap-4'>
          <Link to='/folder/$folderId' params={{ folderId: data.projectId }}>
            <ArrowLeft />
          </Link>
          <h1 className='text-2xl font-bold'>{data.title}</h1>
        </div>
        <p className=''>
          <strong>Shared:</strong>{' '}
          {format(new Date(data.uploadedOn), 'MMM dd, yyyy')}
        </p>
      </div>
      <div className='container mx-auto h-[calc(100vh-64px)] px-2 py-6 sm:px-4'>
        <div className='grid grid-cols-1 gap-6 lg:grid-cols-3'>
          <div className='space-y-4 lg:col-span-2'>
            <div className='space-y-4'>
              <CustomVideoPlayer
                hlsPlaylistUrl={data.hlsPlaylistUrl}
                videoUrl={data.videoUrl}
                videoId={videoId}
                videoProgress={videoProgress}
                thumbnailUrl={getEffectiveThumbnailUrl(data)}
                isCommentsEnabled={data.isCommentsEnabled}
              />
            </div>
            {/* <VideoTimeline videoSrc={videoSrc} /> */}
          </div>
          {data.isCommentsEnabled ? (
            <div className='space-y-6'>
              <CommentSidebar
                comments={comments?.comments ?? []}
                setVideoProgress={setVideoProgress}
              />
            </div>
          ) : (
            <div className='flex h-full items-center justify-center'>
              <div className='text-center'>
                <div className='mb-4 text-6xl opacity-20'>💬</div>
                <h3 className='mb-2 text-lg font-semibold text-gray-600'>Comments are disabled</h3>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
