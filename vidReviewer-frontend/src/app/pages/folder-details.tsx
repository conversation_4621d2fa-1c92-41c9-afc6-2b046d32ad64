import { Link, useNavigate, useParams } from '@tanstack/react-router';
import { format } from 'date-fns';
import { Archive, ExternalLink, Image, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import FolderMenu from '~components/custom/folder-menu';
import Header from '~components/custom/header';
import { Avatar, AvatarFallback, AvatarImage } from '~components/ui/avatar';
import { Card, CardContent, CardHeader } from '~components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~components/ui/tooltip';
import { cn } from '~lib/utils';

import { useGetProjectQuery } from '~features/project/api/get-project';
import {
  useArchiveVideoMutation,
  useUnArchiveVideoMutation,
} from '~features/video/api/archive-video';
import { useGetVideosQuery } from '~features/video/api/get-videos';
import { type Video } from '~features/video/types/video';
import { getEffectiveThumbnailUrl } from '~features/video/utils/thumbnail-utils';

export const FolderDetails = () => {
  const { folderId } = useParams({ from: '/_auth/folder/$folderId' });
  const navigate = useNavigate();

  const { data, isFetching } = useGetProjectQuery({ projectId: folderId });
  const { data: videos, isFetching: isFetchingVideos } = useGetVideosQuery({
    projectId: folderId,
  });
  const [archiveVideo, { isLoading: isArchivingVideo }] =
    useArchiveVideoMutation();
  const [unArchiveVideo, { isLoading: isUnArchivingVideo }] =
    useUnArchiveVideoMutation();

  const onStorageTypeChange = async (video: Video) => {
    try {
      if (video.storageClass === 'archived') {
        await unArchiveVideo({ videoId: video._id }).unwrap();
      } else {
        await archiveVideo({ videoId: video._id }).unwrap();
      }
      toast.success('Storage type updated successfully');
    } catch (error) {
      console.error(error);
      toast.error('Failed to update storage type');
    }
  };

  const navigateToReviewVideo = (id: string): void => {
    void navigate({ to: '/review-video/$videoId', params: { videoId: id } });
  };

  return (
    <>
      <Header title={data?.title} projectId={folderId} />
      {isFetching || isFetchingVideos ? (
        <div className='flex h-[calc(100vh-64px)] w-full items-center justify-center'>
          <Loader2 size={48} className='animate-spin' />
        </div>
      ) : (
        <div className='relative'>
          {videos && videos.length > 0 ? (
            <div className='mx-auto flex w-full flex-wrap gap-4 p-4 sm:p-10'>
              {videos.map((video, index) => (
                <Card
                  className={cn(
                    'w-full max-w-sm rounded-3xl text-white',
                    video.storageClass === 'archived'
                      ? 'bg-zinc-400'
                      : 'bg-zinc-900',
                  )}
                  key={index}
                >
                  <CardHeader className='relative p-1'>
                    <div
                      className='relative aspect-video w-full cursor-pointer rounded-t-lg p-1'
                      onClick={() => {
                        navigateToReviewVideo(video._id);
                      }}
                    >
                      {video.storageClass === 'archived' && (
                        <div className='absolute inset-0 ml-2 mt-[-20px] w-fit rounded-md bg-zinc-400 px-2'>
                          <p className='relative z-10 text-black'>Archived</p>
                        </div>
                      )}
                      {video.videoThumbnail ? (
                        <div className='relative aspect-video w-full'>
                          <img
                            src={getEffectiveThumbnailUrl(video)}
                            alt='Video thumbnail'
                            className='size-full rounded-2xl object-cover'
                            onError={(e) => {
                              // Fallback to auto thumbnail if custom fails
                              if (video.thumbnailType === 'custom') {
                                e.currentTarget.src = video.videoThumbnail;
                              }
                            }}
                          />
                          {video.storageClass === 'archived' && (
                            <div className='absolute inset-0 size-full rounded-2xl bg-gray-600/80' />
                          )}
                        </div>
                      ) : (
                        <div className='flex size-full items-center justify-center'>
                          <Image size={48} />
                        </div>
                      )}

                      <div className='absolute bottom-4 right-4 flex items-center gap-1 rounded-full bg-black/60 px-2 py-1 text-sm'>
                        <div className='flex -space-x-1'>
                          <Avatar className='size-5 border border-black'>
                            <AvatarImage src='/placeholder.svg?height=20&width=20' />
                            <AvatarFallback>U1</AvatarFallback>
                          </Avatar>
                          <Avatar className='size-5 border border-black'>
                            <AvatarImage src='/placeholder.svg?height=20&width=20' />
                            <AvatarFallback>U2</AvatarFallback>
                          </Avatar>
                        </div>
                        <span>{video.comments.length} comments</span>
                        <ExternalLink className='size-3' />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className='space-y-4 p-4 pt-0'>
                    <div className='flex items-start justify-between'>
                      <Link
                        to='/review-video/$videoId'
                        params={{ videoId: video._id }}
                      >
                        <div>
                          <h3
                            className={cn(
                              'text-lg font-medium',
                              video.storageClass === 'archived' && 'text-black',
                            )}
                          >
                            {video.title}
                          </h3>
                          <p
                            className={cn(
                              'text-sm text-zinc-400',
                              video.storageClass === 'archived' && 'text-black',
                            )}
                          >
                            Uploaded on:{' '}
                            {format(new Date(video.uploadedOn), 'MMM dd, yyyy')}
                          </p>
                        </div>
                      </Link>
                      <div className='flex items-center gap-2'>
                        {video.storageClass === 'archived' && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Archive
                                  size={16}
                                  className={cn('text-black')}
                                />
                              </TooltipTrigger>
                              <TooltipContent>
                                This video is archived
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        <FolderMenu
                          video={video}
                          onStorageTypeChange={onStorageTypeChange}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className='flex h-[calc(100vh-64px)] w-full items-center justify-center text-center'>
              <p className='text-lg font-bold'>
                There are no videos in this project. Please click &apos;Upload
                New Video&apos; to add one.
              </p>
            </div>
          )}
          {isArchivingVideo || isUnArchivingVideo ? (
            <div className='absolute inset-0 flex h-[calc(100vh-64px)] items-center justify-center bg-black/30'>
              <Loader2 size={48} className='animate-spin' />
            </div>
          ) : null}
        </div>
      )}
    </>
  );
};
