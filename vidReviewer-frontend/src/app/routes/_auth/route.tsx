import { createFileRoute, redirect } from '@tanstack/react-router';
import { store } from '~stores/store';

import { setUser } from '~features/user/store/user-slice';

export const Route = createFileRoute('/_auth')({
  beforeLoad: () => {
    const user = localStorage.getItem('user');
    if (user) {
      store.dispatch(setUser({ user: JSON.parse(user) }));
    } else {
      throw redirect({
        to: '/not-authenticated',
        replace: true,
      });
    }
  },
});
