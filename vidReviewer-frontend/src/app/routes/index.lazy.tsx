import { createLazyFileRoute, useNavigate } from '@tanstack/react-router';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';

import { useDecryptSSOMutation } from '~features/auth/api/decrypt-sso';
import { useRefreshAgencyTokenMutation } from '~features/auth/api/refresh-agency-token';
import { useRefreshTokenMutation } from '~features/auth/api/refresh-token';

export const Route = createLazyFileRoute('/')({
  component: Component,
});

function Component() {
  const navigate = useNavigate();
  const [decryptSSO] = useDecryptSSOMutation();
  const [refreshToken] = useRefreshTokenMutation();
  const [refreshAgencyToken] = useRefreshAgencyTokenMutation();

  useEffect(() => {
    const getUserData = async () => {
      const key: string = await new Promise((resolve) => {
        window.parent.postMessage({ message: 'REQUEST_USER_DATA' }, '*');
        window.addEventListener(
          'message',
          ({ data }: { data: { message: string; payload: string } }) => {
            if (data.message === 'REQUEST_USER_DATA_RESPONSE') {
              resolve(data.payload);
            }
          },
        );
      });
      const response = await decryptSSO({ encryptedData: key }).unwrap();
      console.log('🚀 ~ getUserData ~ response:', response);
      localStorage.setItem('userDetails', JSON.stringify(response));
      if (!response.activeLocation) {
        void navigate({ to: '/agency-account' });
        return;
      }

      await refreshAgencyToken({ companyId: response.companyId });
      await refreshToken({ locationId: response.activeLocation });
      void navigate({
        to: '/location/$locationId',
        params: { locationId: response.activeLocation },
      });
    };

    void getUserData();
  }, [decryptSSO, navigate, refreshAgencyToken, refreshToken]);

  return (
    <div className='flex h-screen w-screen items-center justify-center'>
      <Loader2 size={48} className='animate-spin' />
    </div>
  );
}
