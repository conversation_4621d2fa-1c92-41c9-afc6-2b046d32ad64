import { createFileRoute, Outlet, useParams } from '@tanstack/react-router';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';

import { useValidateCustomTokenMutation } from '~features/notification/api/validate-token';

const Component = () => {
  const { collaborateId } = useParams({ from: '/c/$collaborateId' });
  const [validateToken, { isLoading, isError, error }] =
    useValidateCustomTokenMutation();

  useEffect(() => {
    document.title = 'VIDREVIEW';
  }, []);

  useEffect(() => {
    const checkTokenValidity = async () => {
      try {
        const response = await validateToken({
          customToken: collaborateId,
        }).unwrap();
        localStorage.setItem('externalUser', JSON.stringify(response));
      } catch (error) {
        console.error(error);
      }
    };

    void checkTokenValidity();
  }, [collaborateId, validateToken]);

  //   beforeLoad: async () => {
  //     const {collaborateId} = Route.useParams();
  //   }

  if (isLoading) {
    return (
      <div className='flex h-screen w-screen items-center justify-center'>
        <Loader2 size={48} className='animate-spin' />
      </div>
    );
  }

  if (isError) {
    console.error('Error occurred:', error);
    return <div>Error occurred</div>;
  }

  return <Outlet />;
};

export const Route = createFileRoute('/c')({
  component: Component,
});
