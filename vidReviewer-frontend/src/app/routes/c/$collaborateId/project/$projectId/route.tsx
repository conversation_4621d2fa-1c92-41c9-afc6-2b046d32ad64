import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { format } from 'date-fns';
import { ExternalLink, Image, Loader2 } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '~components/ui/avatar';
import { Card, CardContent, CardHeader } from '~components/ui/card';

import { useGetProjectQuery } from '~features/project/api/get-project';
import { useGetVideosQuery } from '~features/video/api/get-videos';
import { getEffectiveThumbnailUrl } from '~features/video/utils/thumbnail-utils';

const Component = () => {
  const { collaborateId, projectId } = Route.useParams();

  const navigate = useNavigate();

  const { data, isFetching } = useGetProjectQuery({ projectId });
  const { data: videos } = useGetVideosQuery({ projectId });

  const navigateToReviewVideo = (id: string): void => {
    void navigate({
      to: '/c/$collaborateId/video/$videoId',
      params: { collaborateId, videoId: id },
    });
  };

  if (isFetching) {
    return (
      <div className='flex h-screen w-screen items-center justify-center'>
        <Loader2 size={48} className='animate-spin' />
      </div>
    );
  }

  return (
    <>
      <header className='flex h-16 items-center justify-between border-b px-6'>
        <div className='flex items-center gap-4'>
          <h1 className='text-2xl font-bold'>{data?.title}</h1>
        </div>
      </header>
      <div>
        {videos && videos.length > 0 ? (
          <div className='mx-auto flex w-full flex-wrap gap-4 p-2 sm:p-10'>
            {videos.map(
              ({ title, uploadedOn, comments, _id, videoThumbnail, customThumbnail, thumbnailType }, index) => (
                <Card
                  className='w-full max-w-sm cursor-pointer rounded-3xl bg-zinc-900 text-white'
                  key={index}
                  onClick={() => {
                    navigateToReviewVideo(_id);
                  }}
                >
                  <CardHeader className='relative p-1'>
                    <div className='relative aspect-video w-full overflow-hidden rounded-t-lg p-1'>
                      {videoThumbnail ? (
                        <img
                          src={getEffectiveThumbnailUrl({ videoThumbnail, customThumbnail, thumbnailType } as any)}
                          alt='Video thumbnail'
                          className='size-full rounded-2xl object-cover'
                          onError={(e) => {
                            // Fallback to auto thumbnail if custom fails
                            if (thumbnailType === 'custom') {
                              e.currentTarget.src = videoThumbnail;
                            }
                          }}
                        />
                      ) : (
                        <div className='flex size-full items-center justify-center'>
                          <Image size={48} />
                        </div>
                      )}
                      <div className='absolute bottom-4 right-4 flex items-center gap-1 rounded-full bg-black/60 px-2 py-1 text-sm'>
                        <div className='flex -space-x-1'>
                          <Avatar className='size-5 border border-black'>
                            <AvatarImage src='/placeholder.svg?height=20&width=20' />
                            <AvatarFallback>U1</AvatarFallback>
                          </Avatar>
                          <Avatar className='size-5 border border-black'>
                            <AvatarImage src='/placeholder.svg?height=20&width=20' />
                            <AvatarFallback>U2</AvatarFallback>
                          </Avatar>
                        </div>
                        <span>{comments.length} comments</span>
                        <ExternalLink className='size-3' />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className='space-y-4 p-4'>
                    <div className='flex items-start justify-between'>
                      <div>
                        <h3 className='text-lg font-medium'>{title}</h3>
                        <p className='text-sm text-zinc-400'>
                          Uploaded on:{' '}
                          {format(new Date(uploadedOn), 'MMM dd, yyyy')}
                        </p>
                      </div>
                    </div>
                    {/* <div className='flex items-center gap-3'>
                      <Avatar>
                        <AvatarImage src='/placeholder.svg?height=40&width=40' />
                        <AvatarFallback>JD</AvatarFallback>
                      </Avatar>
                      <div className='space-y-1'>
                        <p className='font-medium leading-none'>User name</p>
                        <div className='flex items-center gap-2 text-sm text-zinc-400'>
                          <span>{uploadedBy.substring(0, 10)}</span>
                          <span>•</span>
                          <span>Email</span>
                        </div>
                      </div>
                    </div> */}
                  </CardContent>
                </Card>
              ),
            )}
          </div>
        ) : (
          <div className='flex h-[calc(100vh-64px)] w-full items-center justify-center text-center'>
            <p className='text-lg font-bold'>
              There are no videos in this project.
            </p>
          </div>
        )}
      </div>
    </>
  );
};

export const Route = createFileRoute('/c/$collaborateId/project/$projectId')({
  component: Component,
});
