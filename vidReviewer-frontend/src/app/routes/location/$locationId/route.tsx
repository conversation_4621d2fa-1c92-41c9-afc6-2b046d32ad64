import { createFileRoute, Navigate } from '@tanstack/react-router';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useAppDispatch } from '~stores/hooks';

import { useGetUserQuery } from '~features/user/api/get-user';
import { setUser } from '~features/user/store/user-slice';

function Component() {
  const { locationId } = Route.useParams();
  const dispatch = useAppDispatch();

  const { data, error, isFetching } = useGetUserQuery({ locationId });

  useEffect(() => {
    if (data) {
      dispatch(setUser({ user: data }));
      localStorage.setItem('user', JSON.stringify(data));
    }
  }, [data, dispatch]);

  if (isFetching) {
    return (
      <div className='flex h-screen w-screen items-center justify-center'>
        <Loader2 size={48} className='animate-spin' />
      </div>
    );
  }

  if (error) {
    console.error('Error occurred:', error);
    return <div>Error occurred</div>;
  }

  return <Navigate to='/folders' />;
}

export const Route = createFileRoute('/location/$locationId')({
  component: Component,
});
