'use client';

import { Pause, Play, Volume2, VolumeX } from 'lucide-react';
import { useRef, useState } from 'react';
import { Button } from '~components/ui/button';
import { Slider } from '~components/ui/slider';

interface VideoPlayerProps {
  src: string;
  poster: string;
}

export function VideoPlayer({ src, poster }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [progress, setProgress] = useState(0);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        void videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const progress =
        (videoRef.current.currentTime / videoRef.current.duration) * 100;
      setProgress(progress);
    }
  };

  const handleSeek = (value: Array<number>) => {
    if (videoRef.current) {
      const time = (value[0] / 100) * videoRef.current.duration;
      videoRef.current.currentTime = time;
      setProgress(value[0]);
    }
  };

  return (
    <div className='group relative'>
      <video
        ref={videoRef}
        className='aspect-video w-full rounded-lg'
        poster={poster}
        onTimeUpdate={handleTimeUpdate}
      >
        <source src={src} type='video/mp4' />
        Your browser does not support the video tag.
      </video>

      <div className='absolute inset-0 flex items-center justify-center'>
        <Button
          variant='ghost'
          size='icon'
          className='size-20 rounded-full bg-background/20 opacity-0 transition-opacity hover:bg-background/40 group-hover:opacity-100'
          onClick={togglePlay}
        >
          {isPlaying ? (
            <Pause className='size-10' />
          ) : (
            <Play className='size-10' />
          )}
        </Button>
      </div>

      <div className='absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/50 to-transparent p-4 opacity-0 transition-opacity group-hover:opacity-100'>
        <div className='space-y-2'>
          <Slider
            value={[progress]}
            max={100}
            step={0.1}
            onValueChange={handleSeek}
            className='cursor-pointer'
          />
          <div className='flex items-center gap-2'>
            <Button
              variant='ghost'
              size='icon'
              className='text-white hover:text-white'
              onClick={togglePlay}
            >
              {isPlaying ? (
                <Pause className='size-4' />
              ) : (
                <Play className='size-4' />
              )}
            </Button>
            <Button
              variant='ghost'
              size='icon'
              className='text-white hover:text-white'
              onClick={toggleMute}
            >
              {isMuted ? (
                <VolumeX className='size-4' />
              ) : (
                <Volume2 className='size-4' />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
