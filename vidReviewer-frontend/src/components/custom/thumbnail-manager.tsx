import { RotateCcw, Upload as UploadIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '~components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~components/ui/card';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '~components/ui/dialog';

import { CustomThumbnailUpload } from './custom-thumbnail-upload';
import { useResetVideoThumbnailMutation } from '~features/video/api/update-video-thumbnail';
import { type Video } from '~features/video/types/video';
import { 
  getEffectiveThumbnailUrl, 
  hasCustomThumbnail, 
  getThumbnailTypeDisplayName 
} from '~features/video/utils/thumbnail-utils';

interface ThumbnailManagerProps {
  video: Video;
  onVideoUpdate?: (updatedVideo: Video) => void;
  showUploadButton?: boolean;
  showResetButton?: boolean;
  className?: string;
}

export const ThumbnailManager = ({
  video,
  onVideoUpdate,
  showUploadButton = true,
  showResetButton = true,
  className,
}: ThumbnailManagerProps) => {
  const [currentVideo, setCurrentVideo] = useState<Video>(video);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [resetVideoThumbnail, { isLoading: isResetting }] = useResetVideoThumbnailMutation();

  const handleThumbnailUploadSuccess = (updatedVideo: Video) => {
    setCurrentVideo(updatedVideo);
    onVideoUpdate?.(updatedVideo);
    setIsUploadDialogOpen(false);
    toast.success('Custom thumbnail uploaded successfully');
  };

  const handleResetThumbnail = async () => {
    try {
      const result = await resetVideoThumbnail({ videoId: currentVideo._id });
      if (result.data) {
        setCurrentVideo(result.data.video);
        onVideoUpdate?.(result.data.video);
        toast.success('Reset to auto-generated thumbnail successfully');
      }
    } catch (error) {
      toast.error('Failed to reset thumbnail');
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Thumbnail</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="relative">
          <img
            src={getEffectiveThumbnailUrl(currentVideo)}
            alt="Video thumbnail"
            className="w-full aspect-video rounded-lg object-cover"
            onError={(e) => {
              // Fallback to auto thumbnail if custom fails
              if (currentVideo.thumbnailType === 'custom') {
                e.currentTarget.src = currentVideo.videoThumbnail;
              }
            }}
          />
          <div className="absolute top-2 right-2">
            <div className="bg-black/60 text-white px-2 py-1 rounded text-xs">
              {getThumbnailTypeDisplayName(currentVideo)}
            </div>
          </div>
        </div>

        <div className="flex gap-2">
          {showUploadButton && (
            <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex-1">
                  <UploadIcon className="h-4 w-4 mr-2" />
                  Upload Custom
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Upload Custom Thumbnail</DialogTitle>
                </DialogHeader>
                <CustomThumbnailUpload
                  video={currentVideo}
                  onUploadSuccess={handleThumbnailUploadSuccess}
                  onUploadError={(error) => toast.error(error)}
                />
              </DialogContent>
            </Dialog>
          )}

          {showResetButton && hasCustomThumbnail(currentVideo) && (
            <Button
              variant="outline"
              onClick={handleResetThumbnail}
              disabled={isResetting}
              className="flex-1"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              {isResetting ? 'Resetting...' : 'Reset to Auto'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
