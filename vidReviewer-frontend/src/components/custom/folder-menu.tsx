import { CheckCircle, Download, MoreVertical } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import EditVideo from '~components/custom/edit-video';
import { Button } from '~components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~components/ui/dropdown-menu';
import { Progress } from '~components/ui/progress';
import { cn } from '~lib/utils';

import { useDownloadVideoMutation } from '~features/video/api/download-video';
import { type Video } from '~features/video/types/video';

import { DeleteVideoAlertDialog } from './delete-video-alert-dialog';

interface FolderMenuProps {
  video: Video;
  onStorageTypeChange: (video: Video) => void;
}

const FolderMenu = ({ video, onStorageTypeChange }: FolderMenuProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showVideoEditDialog, setShowVideoEditDialog] = useState(false);

  const [downloadVideo] = useDownloadVideoMutation();

  const handleDownload = async () => {
    let toastId: string | number | undefined;

    try {
      toastId = toast.loading('Preparing download...', {
        duration: Number.POSITIVE_INFINITY,
      });

      const response = await downloadVideo({
        videoPath: video.videoUrl,
      }).unwrap();

      const videoResponse = await fetch(response.signedUrl);

      if (!videoResponse.ok) {
        toast.error('Failed to download video');
        throw new Error('Download failed');
      }

      // Extract content length for progress tracking
      const contentLength = videoResponse.headers.get('Content-Length');
      if (!contentLength) throw new Error('Unable to determine file size');

      const totalSize = parseInt(contentLength, 10);
      let loadedSize = 0;

      // Create a stream reader to track progress
      const reader = videoResponse.body?.getReader();
      const chunks: Array<Uint8Array> = [];

      const updateToast = (progress: number) => {
        toast.custom(
          () => (
            <div
              className={`pointer-events-auto flex w-96 rounded-lg bg-white shadow-lg ring-1 ring-black/5`}
            >
              <div className='flex-1 p-4'>
                <div className='flex items-start'>
                  <div className='shrink-0 pt-0.5'>
                    <Download className='size-10 text-blue-600' />
                  </div>
                  <div className='ml-3 flex-1'>
                    <p className='text-sm font-medium text-gray-900'>
                      Downloading video...
                    </p>
                    <p className='mt-1 text-sm text-gray-500'>
                      {progress}% complete
                    </p>
                    <div className='mt-2'>
                      <Progress value={progress} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ),
          {
            id: toastId,
            duration: Number.POSITIVE_INFINITY,
          },
        );
      };

      while (reader) {
        const { done, value } = await reader.read();
        if (done) break;

        chunks.push(value);
        loadedSize += value.length;

        // Calculate and display progress
        const progress = Math.round((loadedSize / totalSize) * 100);

        updateToast(progress);
      }

      const blob = new Blob(chunks);

      // const blob = await videoResponse.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download =
        video.title + '.' + (video.videoUrl.split('.').pop() ?? 'mp4');
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      // Dismiss the download progress toast
      toast.dismiss(toastId);

      // Show a new success toast
      toast.custom(
        () => (
          <div
            className={`pointer-events-auto flex w-96 rounded-lg bg-white shadow-lg ring-1 ring-black/5`}
          >
            <div className='flex-1 p-4'>
              <div className='flex items-start'>
                <div className='shrink-0 pt-0.5'>
                  <CheckCircle className='size-10 text-green-600' />
                </div>
                <div className='ml-3 flex-1'>
                  <p className='text-sm font-medium text-gray-900'>
                    Download complete!
                  </p>
                  <p className='mt-1 text-sm text-gray-500'>
                    Your video has been downloaded successfully.
                  </p>
                </div>
              </div>
            </div>
          </div>
        ),
        { duration: 3000 }, // This toast will auto-close after 3 seconds
      );
    } catch (error) {
      console.error(error);
      toast.error('An error occurred during download', { id: toastId });
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild className='hover:bg-transparent'>
          <Button
            className={cn(
              'w-min p-0 ring-0 hover:bg-transparent hover:text-accent focus-visible:ring-0',
              video.storageClass === 'archived' &&
                'text-black hover:text-black',
            )}
            variant={'ghost'}
          >
            <MoreVertical className='size-5' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className='w-[150px] gap-2 rounded-md border'>
          <DropdownMenuGroup>
            {video.isDownloadable && (
              <>
                <DropdownMenuItem
                  className='cursor-pointer'
                  onClick={handleDownload}
                >
                  Download
                </DropdownMenuItem>
                <DropdownMenuSeparator className='bg-[#D9D9D9]' />
              </>
            )}

            <DropdownMenuItem
              className='cursor-pointer'
              onSelect={() => {
                setShowVideoEditDialog(true);
              }}
            >
              Edit Video Details
            </DropdownMenuItem>
            <DropdownMenuSeparator className='bg-[#D9D9D9]' />
            <DropdownMenuItem
              className='cursor-pointer'
              onSelect={() => {
                setIsOpen(true);
              }}
            >
              Delete Video
            </DropdownMenuItem>
            <DropdownMenuSeparator className='bg-[#D9D9D9]' />
            <DropdownMenuItem
              className='cursor-pointer'
              onSelect={() => {
                onStorageTypeChange(video);
              }}
            >
              {video.storageClass === 'archived' ? 'Unarchive' : 'Archive'}
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
      <EditVideo
        video={video}
        isOpen={showVideoEditDialog}
        setIsOpen={setShowVideoEditDialog}
      />
      <DeleteVideoAlertDialog
        video={video}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
      />
    </>
  );
};

export default FolderMenu;
