import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '~components/ui/button';
import {
  Dialog,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '~components/ui/dialog';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~components/ui/form';
import { Input } from '~components/ui/input';
import { Textarea } from '~components/ui/textarea';
import { useAppSelector } from '~stores/hooks';

import { useCreateProjectMutation } from '~features/project/api/create-project';
import { selectUser } from '~features/user/store/user-slice';

const formSchema = z.object({
  title: z
    .string({ required_error: 'Title is required' })
    .min(1, 'Title is required'),
  description: z
    .string({ required_error: 'Description is required' })
    .min(1, 'Description is required'),
});

type FormData = z.infer<typeof formSchema>;

interface NewProjectDialogProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  folderId?: string;
}

export const NewProjectDialog = ({ isOpen, setIsOpen, folderId }: NewProjectDialogProps) => {
  const user = useAppSelector(selectUser);
  const userDetails = localStorage.getItem('userDetails')
    ? JSON.parse(localStorage.getItem('userDetails') || '{}')
    : null;
  
  const [createProject, { isLoading }] = useCreateProjectMutation();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      description: '',
    },
  });

  const onSubmit = async (data: FormData) => {
    if (!user.user?._id) {
      return;
    }

    try {
      await createProject({
        ...data,
        createdBy: user.user._id,
        name: userDetails?.userName ?? 'Anonymous',
        email: userDetails?.email ?? '<EMAIL>',
        folderId,
      }).unwrap();
      
      form.reset();
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleClose = () => {
    form.reset();
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Title</FormLabel>
                  <Input
                    placeholder="Enter project title"
                    {...field}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Description</FormLabel>
                  <Textarea
                    placeholder="Enter project description"
                    {...field}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Project
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
