import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Check, Cloud, Loader2, Plus } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { But<PERSON> } from '~components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~components/ui/dialog';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~components/ui/form';
import { Input } from '~components/ui/input';
import { Progress } from '~components/ui/progress';
import { Textarea } from '~components/ui/textarea';
import { useAppSelector } from '~stores/hooks';

import { useCreateProjectMutation } from '~features/project/api/create-project';
import { useGetStorageQuery } from '~features/user/api/get-storage';
import { selectUser } from '~features/user/store/user-slice';
import { formatFileSize } from '~features/video/utils/format-file-size';
import { CreateFolderDialog } from '~features/folder/components/create-folder-dialog';

const formSchema = z.object({
  title: z
    .string({ required_error: 'Title is required' })
    .min(1, 'Title is required'),
  description: z
    .string({ required_error: 'Description is required' })
    .min(1, 'Description is required'),
});

type FormData = z.infer<typeof formSchema>;

const totalStorage = 107374182400;

const NewVideo = () => {
  const user = useAppSelector(selectUser);
  const userDetails = localStorage.getItem('userDetails')
    ? JSON.parse(localStorage.getItem('userDetails') || '{}')
    : null;
  const [createProject, { isLoading }] = useCreateProjectMutation();
  const { data: storageData } = useGetStorageQuery({
    locationId: user.user?.locationId ?? '',
  });

  const [isDone, setIsDone] = useState(false);
  const [showCreateFolderDialog, setShowCreateFolderDialog] = useState(false);

  const percentage = useMemo(() => {
    if (!storageData?.storage.normal) {
      return 0;
    }
    return (storageData.storage.normal / totalStorage) * 100;
  }, [storageData?.storage.normal]);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      description: '',
    },
  });

  const onSubmit = async (data: FormData) => {
    if (!user.user?._id) {
      return;
    }

    try {
      await createProject({
        ...data,
        createdBy: user.user._id,
        name: userDetails?.userName ?? 'Anonymous',
        email: userDetails?.email ?? '<EMAIL>',
      });
      form.reset();
      setIsDone(true);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className='px-5'>
      <header className='flex items-center justify-between'>
        {/* <h1 className='text-xl font-semibold leading-5 text-[#1D1D1B]'>
          VidReview

        </h1> */}
        <div className='relative flex h-20 w-16 items-center md:w-44'>
          <img
            src='/logo.png'
            alt='logo'
            className='m-0 hidden h-20 w-44 object-contain p-0 md:block'
          />
          <img
            src='/fevicon.png'
            alt='Favicon'
            className='m-0 block size-5 object-contain p-0 md:hidden'
          />
        </div>
        <div className='flex items-center gap-4 md:max-w-screen-sm'>
          <div className='flex items-center justify-center gap-2 rounded-lg border border-black px-2 py-1'>
            <div className='flex items-center justify-center gap-2'>
              <Cloud className='size-4 md:size-6' />
              <span className='hidden text-sm sm:block md:text-base'>
                Storage
              </span>
            </div>
            <div>
              <Progress value={percentage} className='mb-0.5 h-2 md:h-3' />
              <p className='text-[10px]'>
                {storageData?.storage.normal
                  ? formatFileSize(storageData.storage.normal)
                  : '0 MB'}{' '}
                of 100 GB used
              </p>
            </div>
          </div>
          <Dialog
            onOpenChange={() => {
              form.reset();
              setIsDone(false);
            }}
          >
            <DialogTrigger asChild>
              <Button className='flex items-center gap-2 bg-[#DC2625] text-base font-semibold hover:bg-[#DC2625]'>
                <Plus className='size-5' />
                <span className='hidden sm:block'>New Video Project</span>
              </Button>
            </DialogTrigger>
            <DialogContent className='w-[90vw] rounded-md sm:w-[552px]'>
              <DialogHeader>
                <DialogTitle className='text-left'>
                  Create New Video Project
                </DialogTitle>
                <hr className='h-[2px] bg-[#D9D9D9]' />
              </DialogHeader>
              {isDone ? (
                <>
                  <div className='m-auto flex flex-col gap-6 text-center'>
                    <div className='m-auto flex size-[180px] items-center justify-center rounded-full bg-[#DC26250D]'>
                      <div className='flex size-[60px] items-center justify-center rounded-full bg-[#DC2625]'>
                        <Check className='text-white' strokeWidth={4} />
                      </div>
                    </div>
                    <p className='text-base font-medium leading-5 text-[#1D1D1B]'>
                      Video Project Created Successfully
                    </p>
                  </div>
                  <div className='flex items-end justify-end'>
                    <hr className='h-[2px] bg-[#D9D9D9]' />

                    <DialogFooter className='sm:justify-start'>
                      <DialogClose asChild>
                        <Button
                          className='bg-[#DC2625] text-base font-semibold hover:bg-[#DC2625]'
                          onClick={() => {
                            setIsDone(false);
                          }}
                        >
                          Done
                        </Button>
                      </DialogClose>
                    </DialogFooter>
                  </div>
                </>
              ) : (
                <>
                  <div className='flex flex-col gap-6'>
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className='flex flex-col gap-4'
                      >
                        <div className='flex flex-col gap-1'>
                          <FormField
                            control={form.control}
                            name='title'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel
                                  htmlFor='title'
                                  className='text-sm font-semibold text-[#1D1D1B]'
                                >
                                  Project Title
                                </FormLabel>
                                <Input
                                  {...field}
                                  id='title'
                                  name='title'
                                  type='text'
                                  placeholder='Enter Project Title'
                                />
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className='flex flex-col gap-1'>
                          <FormField
                            control={form.control}
                            name='description'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel
                                  htmlFor='description'
                                  className='text-sm font-semibold text-[#1D1D1B]'
                                >
                                  Project Description
                                </FormLabel>
                                <Textarea
                                  {...field}
                                  id='description'
                                  name='description'
                                  placeholder='Enter Project Description'
                                  className='h-[100px] resize-none'
                                  rows={4}
                                />
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </form>
                    </Form>
                  </div>
                  <div className='flex items-end justify-end'>
                    <hr className='h-[2px] bg-[#D9D9D9]' />
                    <Button
                      className='bg-[#DC2625] text-base font-semibold hover:bg-[#DC2625]'
                      onClick={form.handleSubmit(onSubmit)}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className='mr-2 size-4 animate-spin' />
                          Creating
                        </>
                      ) : (
                        'Create'
                      )}
                    </Button>
                  </div>
                </>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </header>

      {/* Create Folder Dialog */}
      <CreateFolderDialog
        isOpen={showCreateFolderDialog}
        setIsOpen={setShowCreateFolderDialog}
        parentFolderId={undefined} // Root level folder creation
        createdBy={user.user?._id ?? ''}
      />
    </div>
  );
};

export default NewVideo;
