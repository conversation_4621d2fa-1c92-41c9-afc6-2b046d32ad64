import { Avatar, AvatarFallback, AvatarImage } from '~components/ui/avatar';

export function Comments() {
  // Dummy comments data
  const comments = [
    {
      id: 1,
      author: 'User 1',
      avatar: '/placeholder.svg?height=40&width=40',
      content: 'Beautiful sunset! The colors are amazing.',
      time: '2 hours ago',
    },
    {
      id: 2,
      author: 'User 2',
      avatar: '/placeholder.svg?height=40&width=40',
      content: 'The lighting effects are stunning. Great capture!',
      time: '1 hour ago',
    },
    {
      id: 3,
      author: 'User 3',
      avatar: '/placeholder.svg?height=40&width=40',
      content: 'This reminds me of the sunsets in my hometown.',
      time: '30 minutes ago',
    },
    {
      id: 4,
      author: 'User 4',
      avatar: '/placeholder.svg?height=40&width=40',
      content: 'Perfect moment captured. Love the composition!',
      time: '15 minutes ago',
    },
  ];

  return (
    <div className='space-y-6 rounded-lg bg-slate-200/50 p-6'>
      <h2 className='text-xl font-semibold'>Comments</h2>
      <div className='space-y-4'>
        {comments.map((comment) => (
          <div key={comment.id} className='flex gap-4 rounded-lg bg-white p-2'>
            <Avatar>
              <AvatarImage src={comment.avatar} />
              <AvatarFallback>{comment.author[0]}</AvatarFallback>
            </Avatar>
            <div className='space-y-1'>
              <div className='flex items-center gap-2'>
                <span className='font-medium'>{comment.author}</span>
                <span className='text-sm text-muted-foreground'>
                  {comment.time}
                </span>
              </div>
              <p className='text-sm text-muted-foreground'>{comment.content}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
