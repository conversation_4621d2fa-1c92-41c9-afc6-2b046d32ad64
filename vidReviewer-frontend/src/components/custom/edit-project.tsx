import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '~components/ui/button';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '~components/ui/dialog';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~components/ui/form';
import { Input } from '~components/ui/input';
import { Textarea } from '~components/ui/textarea';

import { useUpdateProjectMutation } from '~features/project/api/update-project';
import { Project } from '~features/project/types/project';

interface EditProjectProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  project: Project;
}

const formSchema = z.object({
  title: z
    .string({ required_error: 'Title is required' })
    .min(1, 'Title is required'),
  description: z
    .string({ required_error: 'Description is required' })
    .min(1, 'Description is required'),
  // collaborator: z
  //   .string({ required_error: 'Collaborators is required' })
  //   .min(1, 'Collaborators is required'),
});

type FormData = z.infer<typeof formSchema>;

const EditProject = ({ isOpen, setIsOpen, project }: EditProjectProps) => {
  const [updateProject, { isLoading }] = useUpdateProjectMutation();
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: project.title,
      description: project.description,
    },
  });

  const onSubmit = async (data: FormData) => {
    console.log(data);

    try {
      const result = await updateProject({
        projectId: project._id,
        ...data,
      }).unwrap();
      console.log('🚀 ~ onSubmit ~ result:', result);
      setIsOpen(false);
    } catch (error) {
      console.error(error);
    }
  };
  // const collaborators = [
  //   {
  //     name: 'Jane Doe',
  //     phone: '+12381293',
  //     email: '<EMAIL>',
  //     avatarSrc: '/placeholder.svg',
  //     avatarFallback: 'D',
  //   },
  //   {
  //     name: 'Jane Doe',
  //     phone: '+12381293',
  //     email: '<EMAIL>',
  //     avatarSrc: '/placeholder.svg',
  //     avatarFallback: 'D',
  //   },
  // ];

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className='w-[552px] max-w-none rounded-[30px]'>
        <DialogHeader>
          <DialogTitle>Edit Video Project</DialogTitle>
          <hr className='h-[2px] bg-[#D9D9D9]' />
        </DialogHeader>
        <div className='flex w-[488px] flex-col gap-6'>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className='flex flex-col gap-4'
            >
              <div className='flex flex-col gap-1'>
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel
                        htmlFor='title'
                        className='text-sm font-semibold text-[#1D1D1B]'
                      >
                        Project Title
                      </FormLabel>
                      <Input
                        {...field}
                        id='title'
                        name='title'
                        type='text'
                        placeholder='Enter Video Title'
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className='flex flex-col gap-1'>
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel
                        htmlFor='description'
                        className='text-sm font-semibold text-[#1D1D1B]'
                      >
                        Project Description
                      </FormLabel>
                      <Textarea
                        {...field}
                        id='description'
                        name='description'
                        placeholder='Enter Video Description'
                        className='h-[100px] w-[488px] resize-none'
                        rows={4}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/* <div className='flex flex-col gap-1'>
                <FormField
                  control={form.control}
                  name='collaborator'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel
                        htmlFor='collaborators'
                        className='text-sm font-semibold text-[#1D1D1B]'
                      >
                        Select Collaborators
                      </FormLabel>
                      <Input
                        {...field}
                        id='collaborator'
                        name='collaborator'
                        type='text'
                        placeholder='Search User'
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div> */}
            </form>
          </Form>

          {/* <div className='flex flex-col gap-4'>
            {collaborators.map((collaborator, index) => (
              <div key={index} className='flex items-center justify-between'>
                <div className='flex items-center gap-3'>
                  <Avatar className='h-[32px] w-[32px]'>
                    <AvatarImage
                      src={collaborator.avatarSrc}
                      className='h-full w-full bg-[#925721]'
                    />
                    <AvatarFallback className='bg-[#925721] text-xs text-white'>
                      {collaborator.avatarFallback}
                    </AvatarFallback>
                  </Avatar>
                  <div className='space-y-1'>
                    <p className='text-sm font-normal text-[#1D1D1B]'>
                      {collaborator.name}
                    </p>
                    <div className='flex items-center gap-2 text-xs font-normal text-[#1D1D1B]'>
                      <span>{collaborator.phone}</span>
                      <span>•</span>
                      <span>{collaborator.email}</span>
                    </div>
                  </div>
                </div>
                <CircleX className='h-[19px] w-[19px] text-[#1D1D1B]' />
              </div>
            ))}
          </div> */}
        </div>
        <div className='flex items-end justify-end'>
          <hr className='h-[2px] bg-[#D9D9D9]' />

          <DialogFooter className='sm:justify-start'>
            <DialogClose asChild>
              <Button
                className='bg-[#DC2625] text-base font-semibold hover:bg-[#DC2625]'
                onClick={form.handleSubmit(onSubmit)}
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 size-4 animate-spin' />
                    Updating
                  </>
                ) : (
                  'Update'
                )}
              </Button>
            </DialogClose>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditProject;
