import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~components/ui/alert-dialog';
import { Button } from '~components/ui/button';

import { useDeleteVideoMutation } from '~features/video/api/delete-video';
import type { Video } from '~features/video/types/video';

interface DeleteVideoAlertDialogProps {
  video: Video;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const DeleteVideoAlertDialog = ({
  video,
  isOpen,
  setIsOpen,
}: DeleteVideoAlertDialogProps) => {
  const [deleteVideo, { isLoading }] = useDeleteVideoMutation();

  const onDeleteVideo = async () => {
    try {
      await deleteVideo({ videoId: video._id });
      toast.success('Video Deleted successfully');
    } catch (error) {
      console.error(error);
      toast.error('Failed to delete Video');
    }
    setIsOpen(false);
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      {/* <AlertDialogTrigger asChild>
        <Button
          variant={'ghost'}
          className='w-full cursor-pointer justify-start p-0 pl-2 font-normal hover:bg-transparent'
        >
          Delete Video
        </Button>
      </AlertDialogTrigger> */}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete your
            video.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          {!isLoading && <AlertDialogCancel>Cancel</AlertDialogCancel>}
          <Button
            className='bg-red-600 hover:bg-red-600'
            disabled={isLoading}
            onClick={() => onDeleteVideo()}
          >
            {isLoading ? (
              <>
                <Loader2 className='mr-2 size-4 animate-spin' />
                Deleting
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
