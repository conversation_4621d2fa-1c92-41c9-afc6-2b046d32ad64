import { Link } from '@tanstack/react-router';
import { ArrowLeft, FolderPlus, Plus } from 'lucide-react';
import { useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '~components/ui/avatar';
import { Button } from '~components/ui/button';
import { ShareButton } from '~features/sharing/components/share-button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '~components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~components/ui/tooltip';
import { useAppSelector } from '~stores/hooks';

import { splitNameInitials } from '~features/comment/utils/split-name-initials';
import { useGetCollaboratorsQuery } from '~features/project/api/get-collaborators';
import { selectUser } from '~features/user/store/user-slice';
import { CreateFolderDialog } from '~features/folder/components/create-folder-dialog';
import { NewProjectDialog } from '~components/custom/new-project-dialog';
import { CreateSubProjectDialog } from '~features/sub-project/components/create-sub-project-dialog';

import UploadVideo from './upload-video';

interface HeaderProps {
  title: string | undefined;
  projectId?: string; // Optional for folder pages
  folderId?: string; // Optional for folder pages - when provided, shows folder/project creation buttons
  showFolderActions?: boolean; // Whether to show folder and project creation buttons
  showSubProjectActions?: boolean; // Whether to show sub-project creation button
  showUploadButton?: boolean; // Whether to show upload button (default: true when projectId is provided)
  onBackClick?: () => void; // Custom back navigation handler
  isRootPage?: boolean; // Whether this is the root/home page (makes home button bigger)
  // Dynamic sharing props
  shareResourceType?: 'project' | 'folder'; // What type of resource to share (defaults to 'project' when projectId is provided)
  shareResourceId?: string; // ID of the resource to share (defaults to projectId)
  shareResourceTitle?: string; // Title of the resource to share (defaults to title)
}

// Colors provided by Leeton Deka in presence of JP Lahe
// const colors = ['#B4B8AB', '#153243', '#284B63', '#416165', '#8baaad'];

const colors = [
  '#ff6b35',
  '#9448bc',
  '#edae49',
  '#d81159',
  '#004e89',
  '#7ae582',
  '#1a659e',
];

const Header = ({
  title,
  projectId,
  folderId,
  showFolderActions = false,
  showSubProjectActions = false,
  showUploadButton = true,
  onBackClick,
  isRootPage = false,
  shareResourceType,
  shareResourceId,
  shareResourceTitle
}: HeaderProps) => {
  const user = useAppSelector(selectUser);

  const [isOpen, setIsOpen] = useState(false);
  const [showCreateFolderDialog, setShowCreateFolderDialog] = useState(false);
  const [showCreateProjectDialog, setShowCreateProjectDialog] = useState(false);
  const [showCreateSubProjectDialog, setShowCreateSubProjectDialog] = useState(false);

  const { data } = useGetCollaboratorsQuery(
    {
      projectId: projectId!,
    },
    {
      skip: !projectId, // Skip query if no projectId provided
    }
  );

  return (
    <header className='flex h-16 items-center justify-between border-b px-1 sm:px-6'>
      <div className='flex items-center gap-1 sm:gap-4'>
        {onBackClick ? (
          <button onClick={onBackClick} className={`p-1 hover:bg-gray-100 rounded ${isRootPage ? 'p-2' : ''}`}>
            <ArrowLeft className={isRootPage ? 'h-6 w-6' : 'h-5 w-5'} />
          </button>
        ) : (
          <Link to='/folders' className={`p-1 hover:bg-gray-100 rounded ${isRootPage ? 'p-2' : ''}`}>
            <ArrowLeft className={isRootPage ? 'h-6 w-6' : 'h-5 w-5'} />
          </Link>
        )}
        <h1 className='text-2xl font-bold'>{title}</h1>
      </div>
      <div className='flex gap-2 sm:gap-4'>
        {/* Folder actions - New Folder and New Project buttons */}
        {showFolderActions && (
          <>
            <Button
              onClick={() => setShowCreateFolderDialog(true)}
              className="flex items-center gap-2 px-4 py-2"
              variant="outline"
            >
              <FolderPlus className="h-4 w-4" />
              <span className='hidden sm:block'>New Folder</span>
            </Button>
            <Button
              onClick={() => setShowCreateProjectDialog(true)}
              className='flex items-center gap-2 bg-[#DC2625] text-base font-semibold hover:bg-[#DC2625]'
            >
              <Plus className='size-5' />
              <span className='hidden sm:block'>New Project</span>
            </Button>
          </>
        )}

        {/* Sub-project actions - New Sub-Project button */}
        {showSubProjectActions && projectId && (
          <Button
            onClick={() => setShowCreateSubProjectDialog(true)}
            className="flex items-center gap-2 px-4 py-2"
            variant="outline"
          >
            <FolderPlus className="h-4 w-4" />
            <span className='hidden sm:block'>New Sub-Project</span>
          </Button>
        )}

        {projectId && (
          <div
            className='flex cursor-pointer items-center -space-x-2'
            onClick={() => {
              setIsOpen(true);
            }}
          >
          <div className='flex cursor-pointer items-center -space-x-2 sm:hidden'>
            {data?.slice(0, 2).map((item, index) => {
              return (
                <div key={index} className='relative'>
                  <TooltipProvider delayDuration={200}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Avatar>
                          <AvatarImage src='/placeholder.svg?height=40&width=40' />
                          <AvatarFallback
                            style={{
                              borderColor: colors[index % colors.length],
                              borderWidth: '4px',
                            }}
                          >
                            <span className='text-xs font-semibold'>
                              {splitNameInitials(item.name)}
                            </span>
                          </AvatarFallback>
                        </Avatar>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{item.name}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              );
            })}
            {data && data.length > 2 && (
              <p className='relative left-2.5 pr-1'>+{data.length - 2}</p>
            )}
          </div>
          <div className='hidden cursor-pointer items-center -space-x-2 sm:flex'>
            {data?.slice(0, 5).map((item, index) => {
              return (
                <div key={index} className='relative'>
                  <TooltipProvider delayDuration={200}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Avatar>
                          <AvatarImage src='/placeholder.svg?height=40&width=40' />
                          <AvatarFallback
                            style={{
                              borderColor: colors[index % colors.length],
                              borderWidth: '4px',
                            }}
                          >
                            <span className='text-xs font-semibold'>
                              {splitNameInitials(item.name)}
                            </span>
                          </AvatarFallback>
                        </Avatar>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{item.name}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              );
            })}
            {data && data.length > 5 && (
              <p className='relative left-2.5'>+{data.length - 5} more</p>
            )}
          </div>
        </div>
        )}
        {(shareResourceId || projectId || (folderId && shareResourceType !== undefined)) && (
          <ShareButton
            resourceType={shareResourceType || (projectId ? 'project' : 'folder')}
            resourceId={shareResourceId || projectId || folderId || ''}
            resourceTitle={shareResourceTitle || title || (shareResourceType === 'folder' ? 'Folder' : 'Project')}
            variant="secondary"
            className='bg-[#DC2625] text-sm font-semibold text-white hover:bg-[#DC2625]'
          />
        )}

        {/* Only show upload button when in a project context and showUploadButton is true */}
        {projectId && showUploadButton && <UploadVideo projectId={projectId} />}
      </div>

      {projectId && (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className='max-h-[calc(100vh-100px)] w-96 overflow-y-auto'>
          <DialogHeader>
            <DialogTitle>{title} is shared with</DialogTitle>
          </DialogHeader>
          <ul className='flex flex-col gap-2'>
            {data?.map((item, index) => {
              return (
                <li key={item.name} className='flex items-center gap-2'>
                  <Avatar className='size-8'>
                    <AvatarImage src='/placeholder.svg?height=40&width=40' />
                    <AvatarFallback
                      style={{
                        borderColor: colors[index % colors.length],
                        borderWidth: '4px',
                      }}
                    >
                      <span className='text-xs font-semibold'>
                        {splitNameInitials(item.name)}
                      </span>
                    </AvatarFallback>
                  </Avatar>
                  <span>{item.name}</span>
                </li>
              );
            })}
          </ul>
        </DialogContent>
      </Dialog>
      )}

      {/* Folder creation dialogs */}
      {showFolderActions && (
        <>
          <CreateFolderDialog
            isOpen={showCreateFolderDialog}
            setIsOpen={setShowCreateFolderDialog}
            parentFolderId={folderId}
            createdBy={user.user?._id ?? ''}
          />
          <NewProjectDialog
            isOpen={showCreateProjectDialog}
            setIsOpen={setShowCreateProjectDialog}
            folderId={folderId}
          />
        </>
      )}

      {/* Sub-project creation dialog */}
      {showSubProjectActions && projectId && (
        <CreateSubProjectDialog
          isOpen={showCreateSubProjectDialog}
          setIsOpen={setShowCreateSubProjectDialog}
          projectId={projectId}
        />
      )}
    </header>
  );
};

export default Header;
