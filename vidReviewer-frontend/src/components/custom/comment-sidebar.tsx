import { useMemo } from 'react';
import { ScrollArea } from '~components/ui/scroll-area';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '~components/ui/tabs';

import { type Comment } from '~features/comment/types/comment';

import CommentCard from './comment-card';

const tabList = [
  { label: 'All', value: 'all' },
  // { label: 'Unread', value: 'unread' },
  // { label: 'Unresolved', value: 'unResolve' },
  // { label: 'Resolved', value: 'resolved' },
];

interface CommentSidebarProps {
  comments: Array<Comment>;
  isExternalUser?: boolean;
  setVideoProgress: React.Dispatch<React.SetStateAction<number>>;
}

// Function to convert "time" string (HH:MM:SS or MM:SS) into seconds
const timeToSeconds = (timeStr: string) => {
  const timeParts = timeStr.split(':').map(Number);

  if (timeParts.length === 3) {
    // Format: HH:MM:SS → Convert to seconds
    const [hours, minutes, seconds] = timeParts;
    return hours * 3600 + minutes * 60 + seconds;
  } else if (timeParts.length === 2) {
    // Format: MM:SS → Convert to seconds
    const [minutes, seconds] = timeParts;
    return minutes * 60 + seconds;
  } else {
    return 0; // Default fallback
  }
};

const CommentSidebar = ({
  comments,
  isExternalUser,
  setVideoProgress,
}: CommentSidebarProps) => {
  // Memoized sorted comments to avoid re-sorting on every render
  const sortedComments = useMemo(
    () =>
      [...comments].sort(
        (a, b) => timeToSeconds(a.time) - timeToSeconds(b.time),
      ),
    [comments],
  );

  const onTimeStampClick = (time: string) => {
    setVideoProgress(timeToSeconds(time));
    // console.log('time value', time);
  };

  return (
    <div className='w-full rounded-2xl border border-[#D9D9D9] p-4 sm:w-[400px]'>
      <h1 className='text-base font-semibold text-[#1D1D1B]'>Comments</h1>
      <div className='my-2'>
        <Tabs defaultValue='all'>
          <TabsList className='gap-2 bg-white text-[#1D1D1B]'>
            {tabList.map(({ label, value }) => (
              <TabsTrigger
                key={value}
                className='rounded-none data-[state=active]:border-b data-[state=active]:border-b-red-600 data-[state=active]:text-red-600 data-[state=active]:shadow-none'
                value={value}
              >
                {label}
              </TabsTrigger>
            ))}
          </TabsList>
          <ScrollArea className='h-[calc(100vh-64px-24px-24px-16px-16px-24px-24px-36px-8px-8px)]'>
            {tabList.map(({ value }) => (
              <TabsContent
                key={value}
                value={value}
                className='flex flex-col gap-4'
              >
                {value === 'all' && comments.length ? (
                  sortedComments.map((comment) => (
                    <CommentCard
                      key={comment._id}
                      comment={comment}
                      isActive={false}
                      isExternalUser={isExternalUser}
                      onTimeStampClick={onTimeStampClick}
                    />
                  ))
                ) : (
                  <div>No comments added</div>
                )}
                {/* {value === 'unread' && <p>Unread tab</p>}
              {value === 'unResolve' && <p>Unresolved tab</p>}
              {value === 'resolved' && <p>Resolved tab</p>} */}
              </TabsContent>
            ))}
          </ScrollArea>
        </Tabs>
      </div>
    </div>
  );
};

export default CommentSidebar;
