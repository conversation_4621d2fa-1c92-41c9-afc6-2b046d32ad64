import {
  FastForward,
  Loader2,
  Maximize,
  MessageSquareText,
  Minimize,
  Pause,
  Play,
  Volume2,
  VolumeX,
} from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import ReactPlayer from 'react-player';
import { type OnProgressProps } from 'react-player/base';
import { AddCommentForExternalUser } from '~components/custom/add-comment-for-external-user';
import { Button } from '~components/ui/button';
import { cn } from '~lib/utils';

interface CustomVideoPlayerProps {
  hlsPlaylistUrl: string;
  videoUrl: string;
  videoId: string;
  videoProgress: number;
  thumbnailUrl: string;
  newCommentAdded: boolean;
  setNewCommentAdded: React.Dispatch<React.SetStateAction<boolean>>;
  isNotified: boolean;
  isCommentsEnabled?: boolean;
}

export const VideoPlayerForExternalUser = ({
  hlsPlaylistUrl,
  videoUrl,
  videoId,
  videoProgress,
  thumbnailUrl,
  setNewCommentAdded,
  isCommentsEnabled = true,
}: CustomVideoPlayerProps) => {
  const [playing, setPlaying] = useState(false);
  const [volume, setVolume] = useState(0.8);
  const [muted, setMuted] = useState(false);
  const [played, setPlayed] = useState(0);
  const [showForwardIndicator, setShowForwardIndicator] = useState(false);
  const [showRewindIndicator, setShowRewindIndicator] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);

  // const [duration, setDuration] = useState(0);
  // const [currentTime, setCurrentTime] = useState(0);
  // TODO: Implement frame extraction

  // const [frames, setFrames] = useState<string[]>([]);
  const [showAddCommentDialog, setShowAddCommentDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isBuffering, setIsBuffering] = useState(false);
  const [, setHasError] = useState(false);
  const [currentVideoSource, setCurrentVideoSource] = useState(hlsPlaylistUrl);
  const [hasTriedFallback, setHasTriedFallback] = useState(false);

  const playerRef = useRef<ReactPlayer | null>(null);
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const loadingTimeoutRef = useRef<number | null>(null);

  useEffect(() => {
    playerRef.current?.seekTo(videoProgress, 'seconds');
  }, [videoProgress]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  const handleError = useCallback(() => {
    // Clear loading timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }

    setIsLoading(false);

    // Try fallback to direct video URL if HLS fails and we haven't tried it yet
    if (
      currentVideoSource === hlsPlaylistUrl &&
      !hasTriedFallback &&
      videoUrl
    ) {
      setCurrentVideoSource(videoUrl);
      setHasTriedFallback(true);
      setHasError(false);
      setIsLoading(true);

      // Set new timeout for fallback
      loadingTimeoutRef.current = setTimeout(() => {
        setIsLoading(false);
        setHasError(true);
      }, 3000);
    } else {
      setHasError(true);
    }
  }, [currentVideoSource, hlsPlaylistUrl, hasTriedFallback, videoUrl]);

  // Reset video source when URLs change
  useEffect(() => {
    setCurrentVideoSource(hlsPlaylistUrl);
    setHasTriedFallback(false);
    setHasError(false);
    setIsLoading(true);

    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    // Set a timeout to detect stuck loading
    loadingTimeoutRef.current = setTimeout(() => {
      handleError();
    }, 3000); // 3 second timeout
  }, [handleError, hlsPlaylistUrl, videoUrl]);

  const handleReady = () => {
    setIsLoading(false);

    // Clear loading timeout since video is ready
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }
  };

  const handlePlayPause = () => {
    setPlaying(!playing);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (newVolume === 0) {
      setMuted(true);
    } else if (muted) {
      setMuted(false);
    }
  };

  const handleToggleMute = () => {
    setMuted(!muted);
  };

  const handleProgress = (state: OnProgressProps) => {
    setPlayed(state.played);
    // setCurrentTime(Math.floor(state.playedSeconds));
  };

  const handleRewind = () => {
    playerRef.current?.seekTo(playerRef.current.getCurrentTime() - 10);
    setShowForwardIndicator(false);
    // Show the forward indicator
    setShowRewindIndicator(true);
    // Hide it after 1 second
    setTimeout(() => {
      setShowRewindIndicator(false);
    }, 1000);
  };

  const handleForward = () => {
    playerRef.current?.seekTo(playerRef.current.getCurrentTime() + 10);
    setShowRewindIndicator(false);
    // Show the forward indicator
    setShowForwardIndicator(true);
    // Hide it after 1 second
    setTimeout(() => {
      setShowForwardIndicator(false);
    }, 1000);
  };

  const toggleFullScreen = () => {
    if (playerContainerRef.current) {
      if (document.fullscreenElement) {
        void document.exitFullscreen();
        setIsFullScreen(false);
      } else {
        void playerContainerRef.current.requestFullscreen();
        setIsFullScreen(true);
      }
    }
  };

  const handleSeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = parseFloat(e.target.value);
    setPlayed(time);
    playerRef.current?.seekTo(time);
  };

  const formatTime = (seconds: number) => {
    const date = new Date(seconds * 1000);
    const hh = date.getUTCHours();
    const mm = date.getUTCMinutes();
    const ss = date.getUTCSeconds().toString().padStart(2, '0');
    if (hh) {
      return `${hh}:${mm.toString().padStart(2, '0')}:${ss}`;
    }
    return `${mm}:${ss}`;
  };

  return (
    <>
      <div className='flex flex-col gap-6'>
        <div
          className={cn(
            'w-full overflow-hidden rounded-xl',
            isLoading ? 'hidden' : 'block',
          )}
        >
          <div ref={playerContainerRef} className='group relative'>
            <ReactPlayer
              ref={playerRef}
              url={currentVideoSource}
              width='100%'
              height='100%'
              playing={playing}
              volume={volume}
              muted={muted}
              onProgress={handleProgress}
              onBuffer={() => {
                setIsBuffering(true);
              }}
              onBufferEnd={() => {
                setIsBuffering(false);
              }}
              onReady={handleReady}
              onError={handleError}
              className='aspect-video'
              // onDuration={(d) => setDuration(Math.floor(d))}
              onEnded={handlePlayPause}
              progressInterval={0.01}
              config={{
                file: {
                  attributes: {
                    playsInline: true,
                    webkitPlaysinline: 'true',
                    preload: 'metadata',
                    'x-webkit-airplay': 'allow',
                  },
                },
              }}
            />

            {/* <div ref={triggerRef}></div> */}

            {isBuffering && (
              <div className='absolute inset-0 flex items-center justify-center bg-black/30'>
                <div className='animate-spin'>
                  <Loader2 size={48} color='white' />
                </div>
              </div>
            )}

            {/* Forward Indicator */}
            {showForwardIndicator && (
              <div className='absolute inset-0 flex items-center justify-center'>
                <div className='flex items-center justify-center rounded-full bg-black/50 p-4 backdrop-blur-sm'>
                  <FastForward size={48} className='text-white' />
                  <span className='ml-2 font-medium text-white'>+10s</span>
                </div>
              </div>
            )}

            {/* rewind Indicator */}
            {showRewindIndicator && (
              <div className='absolute inset-0 flex items-center justify-center'>
                <div className='flex items-center justify-center rounded-full bg-black/50 p-4 backdrop-blur-sm'>
                  <span className='rotate-180'>
                    <FastForward size={48} className='text-white' />
                  </span>
                  <span className='ml-2 font-medium text-white'>-10s</span>
                </div>
              </div>
            )}

            <div className='absolute inset-x-0 bottom-0 flex h-16 flex-col items-center bg-[#1D1D1B4D]/30 opacity-0 backdrop-blur-sm transition-opacity duration-300 group-hover:opacity-100'>
              {/* Progress bar */}
              {isCommentsEnabled && (
                <div className='relative'>
                  <div ref={triggerRef}></div>
                  <AddCommentForExternalUser
                    triggerRef={triggerRef}
                    open={showAddCommentDialog}
                    onOpenChange={setShowAddCommentDialog}
                    time={formatTime(
                      played * (playerRef.current?.getDuration() ?? 0),
                    )}
                    videoId={videoId}
                    setNewCommentAdded={setNewCommentAdded}
                  />
                </div>
              )}
              <input
                type='range'
                min={0}
                max={0.999999}
                step='any'
                value={played}
                onChange={handleSeekChange}
                className='mb-2 h-1 w-full cursor-pointer appearance-none rounded-lg bg-gray-400'
              />

              <div className='flex w-full items-center justify-between px-4'>
                {/* Volume controls with hover effect */}
                <div className='group/volume relative flex h-full items-center'>
                  <button
                    onClick={handleToggleMute}
                    className='text-white transition-colors hover:text-blue-400'
                  >
                    {muted || volume === 0 ? (
                      <VolumeX size={24} />
                    ) : (
                      <Volume2 size={24} />
                    )}
                  </button>
                  {/* Volume slider with absolute positioning and hidden thumb by default */}
                  <div className='absolute left-8'>
                    <input
                      type='range'
                      min={0}
                      max={1}
                      step='any'
                      value={volume}
                      onChange={handleVolumeChange}
                      className='h-1 w-0 cursor-pointer appearance-none rounded-lg bg-gray-400 transition-all duration-300 group-hover/volume:w-24 [&::-moz-range-thumb]:size-0 [&::-moz-range-thumb]:appearance-none [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:border-0 [&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:transition-all group-hover/volume:[&::-moz-range-thumb]:size-3 [&::-webkit-slider-thumb]:size-0 [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:transition-all group-hover/volume:[&::-webkit-slider-thumb]:size-3'
                    />
                  </div>
                </div>

                <div className='flex items-center space-x-2'>
                  {/* backward button */}
                  <button
                    onClick={handleRewind}
                    className='rotate-180 text-white transition-colors hover:text-blue-400'
                  >
                    <FastForward size={24} />
                  </button>

                  {/* Play/Pause button */}
                  <button
                    onClick={handlePlayPause}
                    className='text-white transition-colors hover:text-blue-400'
                  >
                    {playing ? (
                      <Pause size={24} />
                    ) : (
                      <Play size={24} fill='white' />
                    )}
                  </button>

                  {/* forward button */}
                  <button
                    onClick={handleForward}
                    className='text-white transition-colors hover:text-blue-400'
                  >
                    <FastForward size={24} />
                  </button>
                </div>

                {/* Fullscreen button */}
                <button
                  onClick={() => {
                    // Handle fullscreen
                    toggleFullScreen();
                  }}
                  className='text-white transition-colors hover:text-blue-400'
                >
                  {isFullScreen ? <Minimize /> : <Maximize />}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Placeholder */}
        {isLoading && (
          <div
            className='relative flex aspect-video w-full items-center justify-center overflow-hidden rounded-xl bg-gray-200'
            // style={{ width: `${width}px`, height: `${height}px` }}
          >
            <img
              src={thumbnailUrl}
              alt='Video thumbnail'
              className='aspect-video w-full rounded-xl object-contain'
            />
            <div className='absolute inset-0 flex items-center justify-center bg-black/30'>
              <div className='animate-spin'>
                <Loader2 size={48} color='white' />
              </div>
            </div>
          </div>
        )}

        <div className='w-full rounded-xl bg-[#1D1D1B]'>
          <div className='py-4 text-center text-white'>
            {formatTime(played * (playerRef.current?.getDuration() ?? 0))}
          </div>

          {isCommentsEnabled && (
            <div className='flex justify-center py-6'>
              <Button
                variant={'outline'}
                className='border-[#FFFFFF33]/20 bg-transparent font-semibold text-white'
                onClick={() => {
                  setPlaying(false);
                  setShowAddCommentDialog(true);
                }}
              >
                <MessageSquareText className='mr-2' />
                Add Comment
              </Button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
