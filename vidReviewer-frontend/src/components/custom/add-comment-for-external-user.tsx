import { useParams } from '@tanstack/react-router';
import { Loader2, SendHorizontal } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Avatar, AvatarFallback } from '~components/ui/avatar';
import { Badge } from '~components/ui/badge';
import { Button } from '~components/ui/button';
import { Card, CardContent } from '~components/ui/card';
import { Dialog, DialogContentWithoutOverlay } from '~components/ui/dialog';
import { Textarea } from '~components/ui/textarea';

import { useAddCommentMutation } from '~features/comment/api/add-comment';
import { splitNameInitials } from '~features/comment/utils/split-name-initials';
import { useGetSharedResourceQuery } from '~features/sharing/api/get-shared-resource';

interface AddCommentDialogProps {
  triggerRef: React.RefObject<HTMLElement>;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  time: string;
  videoId: string;
  setNewCommentAdded: React.Dispatch<React.SetStateAction<boolean>>;
}

export function AddCommentForExternalUser({
  triggerRef,
  open,
  onOpenChange,
  time,
  videoId,
  setNewCommentAdded,
}: AddCommentDialogProps) {
  const { token } = useParams({ from: '/shared_/$token/$videoId' });

  const { data } = useGetSharedResourceQuery(token);

  const [addComment, { isLoading }] = useAddCommentMutation();

  const [dialogStyle, setDialogStyle] = useState<React.CSSProperties>({});
  const [content, setContent] = useState('');

  useEffect(() => {
    if (open && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setDialogStyle({
        position: 'absolute',
        top: `${rect.bottom + window.scrollY}px`,
        left: `${rect.left + window.scrollX}px`,
        display: 'block',
      });
    }
  }, [open, triggerRef]);

  const onAddComment = async () => {
    if (!data) {
      return;
    }

    try {
      await addComment({
        content,
        videoId,
        parentCommentId: null,
        userId: data.sharedToId,
        time,
        addedBy: data.sharedTo,
      });
      setContent('');
      onOpenChange(false);
      setNewCommentAdded(true);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContentWithoutOverlay
        style={dialogStyle}
        className='hidden max-w-fit border-none bg-transparent p-0 shadow-none'
      >
        <div className='flex w-[95vw] flex-col items-center gap-2 sm:w-[500px] sm:flex-row sm:items-start sm:gap-4'>
          <div>
            <Badge className='rounded-[30px] bg-[#DC2625] px-2 text-sm font-medium text-white hover:bg-[#DC2625]'>
              {time}
            </Badge>
          </div>
          <div className='w-full flex-1'>
            <Card>
              <CardContent className='flex gap-2 p-2 sm:p-4'>
                <Avatar className='size-6'>
                  <AvatarFallback className='bg-[#1FAE47] text-xs text-white'>
                    {splitNameInitials(data ? data.sharedTo : 'Anonymous')}
                  </AvatarFallback>
                </Avatar>
                <div className='relative flex-1'>
                  <Textarea
                    placeholder='Enter your comment'
                    rows={4}
                    className='w-full flex-1'
                    value={content}
                    onChange={(e) => {
                      setContent(e.target.value);
                    }}
                  />
                  <Button
                    variant={'ghost'}
                    className='absolute bottom-2 right-2 h-fit p-0 [&_svg]:size-6 sm:[&_svg]:size-4'
                    onClick={() => {
                      void onAddComment();
                    }}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className='mr-2 size-4 animate-spin' />
                    ) : (
                      <SendHorizontal className='' size={24} />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContentWithoutOverlay>
    </Dialog>
  );
}
