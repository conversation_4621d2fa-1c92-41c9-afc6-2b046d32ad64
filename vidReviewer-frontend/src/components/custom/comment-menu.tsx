import { MoreVertical } from 'lucide-react';
import { Button } from '~components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~components/ui/dropdown-menu';

const CommentMenu = () => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className='hover:bg-transparent'>
        <Button
          className='w-min p-0 ring-0 hover:bg-transparent focus-visible:ring-0'
          variant={'ghost'}
        >
          <MoreVertical className='size-3 p-0' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-fit rounded-md border border-[#D9D9D9]'>
        <DropdownMenuGroup>
          <DropdownMenuItem>Edit</DropdownMenuItem>
          <DropdownMenuSeparator className='bg-[#D9D9D9]' />
          <DropdownMenuItem>Delete</DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CommentMenu;
