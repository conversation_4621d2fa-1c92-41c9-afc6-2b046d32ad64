import { Loader2, SendH<PERSON>zon<PERSON> } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '~components/ui/avatar';
import { Badge } from '~components/ui/badge';
import { Button } from '~components/ui/button';
import { Card, CardContent } from '~components/ui/card';
import { Dialog, DialogContentWithoutOverlay } from '~components/ui/dialog';
import { Textarea } from '~components/ui/textarea';
import { useAppSelector } from '~stores/hooks';

import { useAddCommentMutation } from '~features/comment/api/add-comment';
import { selectUser } from '~features/user/store/user-slice';

interface AddCommentDialogProps {
  triggerRef: React.RefObject<HTMLElement>;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  time: string;
  videoId: string;
}

export function AddCommentDialog({
  triggerRef,
  open,
  onOpenChange,
  time,
  videoId,
}: AddCommentDialogProps) {
  const userDetails = localStorage.getItem('userDetails')
    ? JSON.parse(localStorage.getItem('userDetails') || '{}')
    : null;
  const user = useAppSelector(selectUser);
  const [addComment, { isLoading }] = useAddCommentMutation();

  const [dialogStyle, setDialogStyle] = useState<React.CSSProperties>({});
  const [content, setContent] = useState('');

  useEffect(() => {
    if (open && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setDialogStyle({
        position: 'absolute',
        top: `${rect.bottom + window.scrollY}px`,
        left: `${rect.left + window.scrollX}px`,
        display: 'block',
      });
    }
  }, [open, triggerRef]);

  const onAddComment = async () => {
    if (!user.user) {
      return;
    }

    try {
      const result = await addComment({
        content,
        videoId,
        parentCommentId: null,
        userId: user.user?._id,
        time,
        addedBy: userDetails ? userDetails.userName : 'Anonymous',
      });
      console.log('🚀 ~ onAddComment ~ result:', result);
      setContent('');
      onOpenChange(false);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContentWithoutOverlay
        style={dialogStyle}
        className='hidden max-w-fit border-none bg-transparent p-0 shadow-none'
      >
        <div className='flex w-[95vw] flex-col items-center gap-2 sm:w-[500px] sm:flex-row sm:items-start sm:gap-4'>
          <div>
            <Badge className='rounded-[30px] bg-[#DC2625] px-2 text-sm font-medium text-white hover:bg-[#DC2625]'>
              {time}
            </Badge>
          </div>
          <div className='w-full flex-1'>
            <Card>
              <CardContent className='flex gap-2 p-2 sm:p-4'>
                <Avatar className='size-6'>
                  <AvatarImage
                    src='/placeholder.svg'
                    alt='User Avatar'
                    className='bg-[#1FAE47]'
                  />
                  <AvatarFallback className='bg-[#1FAE47] text-xs text-white'>
                    JD
                  </AvatarFallback>
                </Avatar>
                <div className='relative flex-1'>
                  <Textarea
                    placeholder='Enter your comment'
                    rows={4}
                    className='w-full flex-1'
                    value={content}
                    onChange={(e) => {
                      setContent(e.target.value);
                    }}
                  />
                  <Button
                    variant={'ghost'}
                    className='absolute bottom-2 right-2 h-fit p-0 [&_svg]:size-6 sm:[&_svg]:size-4'
                    onClick={() => {
                      void onAddComment();
                    }}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className='mr-2 animate-spin' />
                    ) : (
                      <SendHorizontal className='' />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContentWithoutOverlay>
    </Dialog>
  );
}
