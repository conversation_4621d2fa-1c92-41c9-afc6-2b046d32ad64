import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import Select from 'react-select';
import { toast } from 'sonner';
import { But<PERSON> } from '~components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '~components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~components/ui/dropdown-menu';
import { Input } from '~components/ui/input';
import { cn } from '~lib/utils';
import { useAppSelector } from '~stores/hooks';

import { useGetContactsMutation } from '~features/contacts/api/get-contacts';
import { useCreateCustomTokenMutation } from '~features/notification/api/create-custom-token';
import { useSendNotificationMutation } from '~features/notification/api/send-notification';
import { selectUser } from '~features/user/store/user-slice';

const frontendUrl = 'https://review.vidlead.com';

interface ShareProjectDialogProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  title: string | undefined;
  projectId: string;
}

// Add type for the select options
interface SelectOption {
  label: string;
  value: string;
  email: string;
  firstName: string;
}

export const ShareProjectDialog = ({
  isOpen,
  setIsOpen,
  title,
  projectId,
}: ShareProjectDialogProps) => {
  const { locationId } = useAppSelector(selectUser);
  const [data, setData] = useState<Array<SelectOption>>([]);
  const [selectedContacts, setSelectedContacts] = useState<Array<SelectOption>>(
    [],
  );
  const [shareableLink, setShareableLink] = useState('');
  const [linkCopyDialogOpen, setLinkCopyDialogOpen] = useState(false);

  const [getContacts] = useGetContactsMutation();
  const [sendNotification, { isLoading: isSendNotificationLoading }] =
    useSendNotificationMutation();
  const [createCustomToken] = useCreateCustomTokenMutation();

  // Load initial contacts
  useEffect(() => {
    const loadContacts = async () => {
      try {
        const response = await getContacts({
          locationId: locationId!,
        }).unwrap();

        const formattedContacts = response.contacts.map((contact) => ({
          label: `${
            contact.firstNameLowerCase && contact.firstNameLowerCase.length > 1
              ? contact.firstNameLowerCase.substring(0, 1).toUpperCase() +
                contact.firstNameLowerCase.substring(1)
              : (contact.firstNameLowerCase?.toUpperCase() ?? '')
          } ${
            contact.lastNameLowerCase && contact.lastNameLowerCase.length > 1
              ? contact.lastNameLowerCase.substring(0, 1).toUpperCase() +
                contact.lastNameLowerCase.substring(1)
              : (contact.lastNameLowerCase?.toUpperCase() ?? '')
          }${contact.email ? ` • ${contact.email}` : ''}${contact.phone ? ` • ${contact.phone}` : ''}`,
          value: contact.id,
          email: contact.email ?? '',
          firstName:
            contact.firstNameLowerCase && contact.firstNameLowerCase.length > 1
              ? contact.firstNameLowerCase.substring(0, 1).toUpperCase() +
                contact.firstNameLowerCase.substring(1)
              : (contact.firstNameLowerCase?.toUpperCase() ?? ''),
        }));

        setData(formattedContacts);
      } catch (error) {
        console.error('Error loading contacts:', error);
        toast.error('Failed to load contacts');
      }
    };

    if (isOpen && locationId) {
      void loadContacts();
    }
  }, [getContacts, locationId, isOpen]);

  const onSearch = async (inputValue: string) => {
    if (inputValue.length > 0 && inputValue.length < 3) {
      toast.info('Please enter at least 3 characters to search');
      return;
    }

    try {
      const response = await getContacts({
        locationId: locationId!,
        query: inputValue,
      }).unwrap();

      const formattedContacts = response.contacts.map((contact) => ({
        label: `${
          contact.firstNameLowerCase && contact.firstNameLowerCase.length > 1
            ? contact.firstNameLowerCase.substring(0, 1).toUpperCase() +
              contact.firstNameLowerCase.substring(1)
            : (contact.firstNameLowerCase?.toUpperCase() ?? '')
        } ${
          contact.lastNameLowerCase && contact.lastNameLowerCase.length > 1
            ? contact.lastNameLowerCase.substring(0, 1).toUpperCase() +
              contact.lastNameLowerCase.substring(1)
            : (contact.lastNameLowerCase?.toUpperCase() ?? '')
        }${contact.email ? ` • ${contact.email}` : ''}${contact.phone ? ` • ${contact.phone}` : ''}`,
        value: contact.id,
        email: contact.email ?? '',
        firstName:
          contact.firstNameLowerCase && contact.firstNameLowerCase.length > 1
            ? contact.firstNameLowerCase.substring(0, 1).toUpperCase() +
              contact.firstNameLowerCase.substring(1)
            : (contact.firstNameLowerCase?.toUpperCase() ?? ''),
      }));

      setData(formattedContacts);
    } catch (error) {
      console.error('Error searching contacts:', error);
      toast.error('Failed to search contacts');
    }
  };

  const onSendNotification = async (type: 'Email' | 'SMS') => {
    try {
      const notificationPromises = selectedContacts.map(async (contact) => {
        try {
          // Create custom token
          const tokenResponse = await createCustomToken({
            projectId,
            name: contact.label.split(' • ')[0],
            email: contact.email,
          }).unwrap();

          // Send notification using the custom token
          const customTokenLink = `${frontendUrl}/c/${tokenResponse.token}`;
          await sendNotification({
            locationId: locationId!,
            contactId: contact.value,
            emailTo: contact.email,
            firstName: contact.firstName,
            link: customTokenLink,
            type,
          }).unwrap();

          return { contact, success: true };
        } catch (error) {
          console.error('Error processing contact:', contact, error);
          return { contact, success: false, error };
        }
      });

      const results = await Promise.all(notificationPromises);

      // Check overall success and notify user
      const successCount = results.filter((result) => result.success).length;
      if (successCount === selectedContacts.length) {
        toast.success(
          `All ${type === 'Email' ? 'emails' : 'SMS'} sent successfully`,
        );
        setIsOpen(false);
        setSelectedContacts([]);
      } else {
        toast.error('Some notifications failed. Check logs for details.');
      }
    } catch (globalError) {
      console.error(
        'Unexpected error during notification process:',
        globalError,
      );
      toast.error('An unexpected error occurred. Please try again.');
    }
  };

  const onCopyLink = async () => {
    try {
      // Create custom token
      const tokenResponse = await createCustomToken({
        projectId,
        name: selectedContacts[0].label.split(' • ')[0],
        email: selectedContacts[0].email,
      }).unwrap();

      // Generate custom token link
      const customTokenLink = `${frontendUrl}/c/${tokenResponse.token}`;

      // Copy link to clipboard
      // await navigator.clipboard.writeText(customTokenLink);
      toast.success('Link Created Successfully!');
      setShareableLink(customTokenLink);
      setLinkCopyDialogOpen(true);

      setIsOpen(false);
      setSelectedContacts([]);
    } catch (error) {
      console.error('Error creating custom token or copying link:', error);
      toast.error('Failed to copy link. Please try again.');
    }
  };

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={() => {
          setIsOpen(false);
          setSelectedContacts([]);
        }}
      >
        <DialogContent className='w-[calc(100vw-2rem)] max-w-[800px]'>
          <DialogHeader className='mb-4'>
            <DialogTitle>Share {title}</DialogTitle>
          </DialogHeader>

          <div className='space-y-4'>
            <Select
              isMulti
              options={data}
              value={selectedContacts}
              onChange={(newValue) => {
                setSelectedContacts((newValue || []) as Array<SelectOption>);
              }}
              onInputChange={(newValue) => {
                void onSearch(newValue);
              }}
              isLoading={false}
              placeholder='Search contacts'
              noOptionsMessage={() => 'No contacts found'}
              classNames={{
                control: () => 'min-h-10 rounded-md border border-input',
                menu: () =>
                  'mt-2 rounded-md border bg-popover text-popover-foreground shadow-md',
                option: () => 'cursor-pointer px-2 py-1.5 hover:bg-accent',
                multiValue: () =>
                  'bg-secondary text-secondary-foreground rounded-sm px-1 py-0.5 mr-1',
                multiValueRemove: () => 'ml-1 hover:text-destructive',
              }}
            />
          </div>

          <DialogFooter className='mt-96 flex justify-end'>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  type='button'
                  variant='secondary'
                  className='bg-[#DC2625] text-sm font-semibold text-white hover:bg-[#DC2625]'
                  disabled={isSendNotificationLoading}
                >
                  {isSendNotificationLoading ? (
                    <>
                      <Loader2 className='mr-2 size-4 animate-spin' />
                      Sharing
                    </>
                  ) : (
                    'Share'
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className='w-[120px] gap-2 rounded-md border'>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    className={cn(selectedContacts.length !== 1 && 'hidden')}
                    onClick={onCopyLink}
                  >
                    <span className='text-sm font-normal text-[#1D1D1B]'>
                      Copy Link
                    </span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator
                    className={cn(
                      selectedContacts.length !== 1 && 'hidden',
                      'bg-[#D9D9D9]',
                    )}
                  />

                  <DropdownMenuItem onClick={() => onSendNotification('Email')}>
                    <span className='text-sm font-normal text-[#1D1D1B]'>
                      Share Email
                    </span>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator className={cn('bg-[#D9D9D9]')} />

                  <DropdownMenuItem onClick={() => onSendNotification('SMS')}>
                    <span className='text-sm font-normal text-[#1D1D1B]'>
                      Send SMS
                    </span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog
        open={linkCopyDialogOpen}
        onOpenChange={() => {
          setLinkCopyDialogOpen(false);
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Copy your shareable link!</DialogTitle>
          </DialogHeader>

          <div className='flex space-x-2'>
            <Input
              readOnly
              value={shareableLink}
              className='font-mono text-sm ring-1 ring-ring'
            />
            {/* <Button
              variant='secondary'
              size='icon'
              onClick={() => copyToClipboard()}
              aria-label='Copy registration link'
            >
              <Copy className='size-4' />
            </Button> */}
          </div>
          <DialogFooter>
            <Button
              onClick={() => {
                setLinkCopyDialogOpen(false);
                setShareableLink('');
              }}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
