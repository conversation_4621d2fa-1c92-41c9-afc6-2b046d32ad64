import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON>ader2, <PERSON>an, Server, RotateCcw, Upload as UploadIcon } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { Button } from '~components/ui/button';
import { Card, CardContent, CardHeader } from '~components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~components/ui/dialog';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~components/ui/form';
import { Input } from '~components/ui/input';
import { Switch } from '~components/ui/switch';
import { Textarea } from '~components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~components/ui/tabs';
import { cn } from '~lib/utils';

import { CustomThumbnailUpload } from '~components/custom/custom-thumbnail-upload';
import { useUpdateVideoMutation } from '~features/video/api/update-video';
import { useResetVideoThumbnailMutation } from '~features/video/api/update-video-thumbnail';
import { type Video } from '~features/video/types/video';
import {
  getEffectiveThumbnailUrl,
  hasCustomThumbnail,
  getThumbnailTypeDisplayName
} from '~features/video/utils/thumbnail-utils';
import { formatFileSize } from '~features/video/utils/format-file-size';

const formSchema = z.object({
  title: z
    .string({ required_error: 'Title is required' })
    .min(1, 'Title is required'),
  description: z
    .string()
    .optional(),
  isDownloadable: z.boolean(),
  isCommentsEnabled: z.boolean(),
});

type FormData = z.infer<typeof formSchema>;

interface EditVideoProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  video: Video;
}

const EditVideo = ({ isOpen, setIsOpen, video }: EditVideoProps) => {
  const [currentVideo, setCurrentVideo] = useState<Video>(video);
  const [updateVideo, { isLoading }] = useUpdateVideoMutation();
  const [resetVideoThumbnail, { isLoading: isResetting }] = useResetVideoThumbnailMutation();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: currentVideo.title,
      description: currentVideo.description || '',
      isDownloadable: currentVideo.isDownloadable,
      isCommentsEnabled: currentVideo.isCommentsEnabled,
    },
  });

  const handleThumbnailUploadSuccess = (updatedVideo: Video) => {
    setCurrentVideo(updatedVideo);
  };

  const handleResetThumbnail = async () => {
    try {
      const result = await resetVideoThumbnail({ videoId: currentVideo._id });
      if (result.data) {
        setCurrentVideo(result.data.video);
        toast.success('Reset to auto-generated thumbnail successfully');
      }
    } catch {
      toast.error('Failed to reset thumbnail');
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      const result = await updateVideo({
        videoId: currentVideo._id,
        status: currentVideo.status,
        ...data,
        description: data.description || '',
      });
      if (result.data) {
        setCurrentVideo(result.data);
      }
      toast.success('Video updated successfully');
    } catch (error) {
      console.error(error);
      toast.error('Failed to update video');
    }
    setIsOpen(false);
    setCurrentVideo(video);
    form.reset();
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={() => {
        setIsOpen(false);
        setCurrentVideo(video);
        form.reset();
      }}
    >
      <DialogContent className={cn('w-[1000px] max-w-none rounded-[30px]')}>
        <DialogHeader>
          <DialogTitle>Edit Video</DialogTitle>
          <hr className='h-[2px] bg-[#D9D9D9]' />
        </DialogHeader>
        <div className='flex w-[920px] gap-6 px-2'>
          <div className='w-[400px]'>
            <Card className='max-w-md rounded-3xl bg-zinc-900 text-white'>
              <CardHeader className='relative p-1'>
                <div className='w-full overflow-hidden rounded-t-lg p-1'>
                  <img
                    src={getEffectiveThumbnailUrl(currentVideo)}
                    alt='Video thumbnail'
                    className='h-[229px] w-[384px] rounded-2xl object-cover'
                  />
                </div>
              </CardHeader>
              <CardContent className='mb-5 space-y-4 px-4 py-1'>
                <div className='mb-2 flex flex-col items-start gap-2'>
                  <h3 className='text-lg font-medium'>{currentVideo.title}</h3>
                  <div className='flex items-center gap-3'>
                    <Scan className='size-4' />
                    <p className='text-sm font-normal'>{currentVideo.width}x{currentVideo.height}px</p>
                  </div>
                  <div className='flex items-center gap-3'>
                    <Server className='size-4' />
                    <p className='text-sm font-normal'>{formatFileSize(currentVideo.size)}</p>
                  </div>
                  <div className='flex items-center gap-3'>
                    <UploadIcon className='size-4' />
                    <p className='text-sm font-normal'>{getThumbnailTypeDisplayName(currentVideo)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          <div className='flex w-[488px] flex-col gap-6'>
            <Tabs defaultValue="details" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="details">Video Details</TabsTrigger>
                <TabsTrigger value="thumbnail">Thumbnail</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4">
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className='flex flex-col gap-4'
                  >
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem className='flex flex-col gap-1'>
                      <FormLabel
                        htmlFor='title'
                        className='text-sm font-semibold text-[#1D1D1B]'
                      >
                        Video Title
                      </FormLabel>
                      <Input
                        {...field}
                        id='title'
                        name='title'
                        type='text'
                        placeholder='Enter Video Title'
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem className='flex flex-col gap-1'>
                      <FormLabel
                        htmlFor='description'
                        className='text-sm font-semibold text-[#1D1D1B]'
                      >
                        Video Description
                      </FormLabel>
                      <Textarea
                        {...field}
                        id='description'
                        name='description'
                        placeholder='Enter Video Description'
                        className='h-[100px] w-[488px] resize-none'
                        rows={4}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='isDownloadable'
                  render={({ field }) => (
                    <FormItem className='flex flex-col gap-1 text-center'>
                      <div className='flex gap-2'>
                        <FormLabel
                          htmlFor='isDownloadable'
                          className='text-sm font-semibold text-[#1D1D1B]'
                        >
                          Allow Download
                        </FormLabel>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='isCommentsEnabled'
                  render={({ field }) => (
                    <FormItem className='flex flex-col gap-1 text-center'>
                      <div className='flex gap-2'>
                        <FormLabel
                          htmlFor='isCommentsEnabled'
                          className='text-sm font-semibold text-[#1D1D1B]'
                        >
                          Enable Comments
                        </FormLabel>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </div>
                    </FormItem>
                  )}
                />
                  </form>
                </Form>
              </TabsContent>

              <TabsContent value="thumbnail" className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Thumbnail Management</h3>
                    {hasCustomThumbnail(currentVideo) && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleResetThumbnail}
                        disabled={isResetting}
                      >
                        {isResetting ? (
                          <span className='flex items-center gap-2'>
                            <Loader2 className='animate-spin size-4' />
                            Resetting...
                          </span>
                        ) : (
                          <span className='flex items-center gap-2'>
                            <RotateCcw className='size-4' />
                            Reset to Auto
                          </span>
                        )}
                      </Button>
                    )}
                  </div>

                  <CustomThumbnailUpload
                    video={currentVideo}
                    onUploadSuccess={handleThumbnailUploadSuccess}
                    onUploadError={(error) => toast.error(error)}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
        <hr className='h-[2px] bg-[#D9D9D9]' />

        <div className='flex items-end justify-end'>
          <DialogFooter className='sm:justify-start'>
            <Button
              className='bg-[#DC2625] font-semibold hover:bg-[#DC2625]'
              type='button'
              onClick={form.handleSubmit(onSubmit)}
            >
              {isLoading ? (
                <span className='flex items-center gap-2'>
                  <Loader2 className='animate-spin' /> Updating
                </span>
              ) : (
                'Update'
              )}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditVideo;
