import { zodResolver } from '@hookform/resolvers/zod';
import {
  Check,
  CircleAlert,
  Image as ImageIcon,
  Loader2,
  Scan,
  Server,
  Upload,
} from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { Button } from '~components/ui/button';
import { Card, CardContent, CardHeader } from '~components/ui/card';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~components/ui/dialog';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~components/ui/form';
import { ScrollArea } from '~components/ui/scroll-area';
import { Input } from '~components/ui/input';
import { Progress } from '~components/ui/progress';
import { Switch } from '~components/ui/switch';
import { Textarea } from '~components/ui/textarea';
import { useAppSelector } from '~stores/hooks';

import { selectUser } from '~features/user/store/user-slice';
import { useGetSignedUrlMutation } from '~features/video/api/get-signed-url';
import { useGetThumbnailSignedUrlMutation } from '~features/video/api/get-thumbnail-signed-url';
import { useUpdateVideoThumbnailMutation } from '~features/video/api/update-video-thumbnail';
import { useUploadVideoMutation } from '~features/video/api/upload-video';
import { formatFileSize } from '~features/video/utils/format-file-size';
import {
  isValidThumbnailFileType,
  isValidThumbnailFileSize,
  createFilePreviewUrl,
  cleanupFilePreviewUrl
} from '~features/video/utils/thumbnail-utils';

const formSchema = z.object({
  title: z
    .string({ required_error: 'Title is required' })
    .min(1, 'Title is required'),
  description: z
    .string()
    .optional(),
  isDownloadable: z.boolean(),
  isCommentsEnabled: z.boolean(),
});

type FormData = z.infer<typeof formSchema>;

// const FOUR_GB = 4 * 1024 * 1024 * 1024;

interface UploadVideoProps {
  projectId?: string;
  projectFolderId?: string | null;
}

const UploadVideo = ({ projectId, projectFolderId }: UploadVideoProps) => {
  const user = useAppSelector(selectUser);
  const [file, setFile] = useState<File | null>(null);
  const [isCreated, setIsCreated] = useState(false);
  const [isFileUploaded, setIsFileUploaded] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [videoUploadInProgress, setVideoUploadInProgress] = useState(false);
  const [uploadAbortController, setUploadAbortController] = useState<AbortController | null>(null);
  const [customThumbnailFile, setCustomThumbnailFile] = useState<File | null>(null);

  // Ref for scroll area to enable auto-scroll functionality
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Function to scroll to bottom of the scroll area
  const scrollToBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (viewport) {
        viewport.scrollTo({
          top: viewport.scrollHeight,
          behavior: 'smooth'
        });
      }
    }
  }, []);

  const [showThumbnailUpload, setShowThumbnailUpload] = useState(false);
  const [videoInfo, setVideoInfo] = useState<{
    size: string;
    width: number;
    height: number;
    duration: number;
    name: string;
  }>();

  useEffect(() => {
    const videoResolution = async () => {
      if (file) {
        try {
          const resolution = await getVideoResolution(file);
          setVideoInfo(
            resolution as {
              size: string;
              width: number;
              height: number;
              duration: number;
              name: string;
            },
          );
        } catch (err) {
          console.error(err);
        }
      }
    };
    if (file) {
      void videoResolution();
    }
  }, [file]);

  // Auto-scroll when upload progress starts
  useEffect(() => {
    if (videoUploadInProgress) {
      scrollToBottom();
    }
  }, [videoUploadInProgress, scrollToBottom]);

  const [uploadVideo, { isLoading }] = useUploadVideoMutation();
  const [getSignedUrl, { isLoading: getSignedUrlLoading }] =
    useGetSignedUrlMutation();
  const [getThumbnailSignedUrl] = useGetThumbnailSignedUrlMutation();
  const [updateVideoThumbnail] = useUpdateVideoThumbnailMutation();

  const onDrop = useCallback((acceptedFiles: Array<File>) => {
    if (acceptedFiles.length > 0) {
      setFile(acceptedFiles[0]);
    }
  }, []);

  const onThumbnailDrop = useCallback((acceptedFiles: Array<File>) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];

      // Validate file type
      if (!isValidThumbnailFileType(file.type)) {
        toast.error('Please select a valid image file (JPEG, PNG, or WebP)');
        return;
      }

      // Validate file size
      if (!isValidThumbnailFileSize(file.size)) {
        toast.error('File size must be less than 5MB');
        return;
      }

      setCustomThumbnailFile(file);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false, // Allow only one file to be dropped
  });

  const {
    getRootProps: getThumbnailRootProps,
    getInputProps: getThumbnailInputProps,
    isDragActive: isThumbnailDragActive
  } = useDropzone({
    onDrop: onThumbnailDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp'],
    },
    multiple: false,
    maxSize: 5 * 1024 * 1024, // 5MB
  });

  const getVideoResolution = (file: File) => {
    return new Promise((resolve, reject) => {
      try {
        const video = document.createElement('video');
        video.preload = 'metadata';

        video.onloadedmetadata = () => {
          window.URL.revokeObjectURL(video.src);
          resolve({
            width: video.videoWidth,
            height: video.videoHeight,
            duration: video.duration,
            name: file.name,
            size: (file.size / (1024 * 1024)).toFixed(2), // Convert to MB
          });
        };

        video.onerror = () => {
          reject(new Error('Failed to load video metadata'));
        };

        video.src = URL.createObjectURL(file);
      } catch {
        reject(new Error(`Failed to load video metadata`));
      }
    });
  };

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      description: '',
      isDownloadable: false,
      isCommentsEnabled: true,
    },
  });

  const onSubmit = async (data: FormData) => {
    if (!projectId) {
      toast.error('Project ID is required for video upload');
      return;
    }

    const body = {
      title: data.title,
      description: data.description || '',
      projectId: projectId,
      projectFolderId: projectFolderId || null,
      uploadedBy: user.user?._id ?? '',
      gcpFileName: '',
      isDownloadable: data.isDownloadable,
      isCommentsEnabled: data.isCommentsEnabled,
      height: videoInfo?.height,
      width: videoInfo?.width,
      size: file ? file.size : 0,
    };

    if (file) {
      try {
        const result = await getSignedUrl({
          fileName: file.name,
          fileType: file.type,
        });

        if (!result.data) {
          toast.error('Some error occurred, please try again');
          return;
        }

        const { url, fileName } = result.data;

        setVideoUploadInProgress(true);

        // Scroll to bottom to show upload progress
        setTimeout(() => scrollToBottom(), 100);

        // Create abort controller for cancellation
        const abortController = new AbortController();
        setUploadAbortController(abortController);

        // Upload with progress tracking
        await new Promise<void>((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          xhr.open('PUT', url, true);
          xhr.setRequestHeader('Content-Type', file.type);

          // Handle abort signal
          abortController.signal.addEventListener('abort', () => {
            xhr.abort();
            reject(new DOMException('Upload cancelled', 'AbortError'));
          });

          xhr.upload.onprogress = (e) => {
            if (e.lengthComputable) {
              const percentComplete = Math.round((e.loaded / e.total) * 100);
              setUploadProgress(percentComplete);
            }
          };

          xhr.onload = () => {
            if (xhr.status === 200) {
              resolve();
            } else {
              reject(new Error(`Upload failed with status: ${xhr.status}`));
            }
          };

          xhr.onerror = () => {
            reject(new Error('Upload failed'));
          };

          xhr.onabort = () => {
            reject(new DOMException('Upload cancelled', 'AbortError'));
          };

          xhr.send(file);
        });
        body.gcpFileName = fileName;

        const videoResult = await uploadVideo(body);

        // Upload custom thumbnail if provided
        if (customThumbnailFile && videoResult.data) {
          try {
            // Get signed URL for thumbnail
            const thumbnailSignedUrlResult = await getThumbnailSignedUrl({
              fileName: customThumbnailFile.name,
              fileType: customThumbnailFile.type,
              videoId: videoResult.data._id,
            });

            if (thumbnailSignedUrlResult.data) {
              const { url: thumbnailUrl, fileName: thumbnailFileName } = thumbnailSignedUrlResult.data;

              // Upload thumbnail to GCS
              const thumbnailUploadResponse = await fetch(thumbnailUrl, {
                method: 'PUT',
                headers: {
                  'Content-Type': customThumbnailFile.type,
                },
                body: customThumbnailFile,
              });

              if (thumbnailUploadResponse.ok) {
                // Update video with custom thumbnail
                await updateVideoThumbnail({
                  videoId: videoResult.data._id,
                  gcpFileName: thumbnailFileName,
                });
                toast.success('Video and custom thumbnail uploaded successfully');
              } else {
                toast.warning('Video uploaded but thumbnail upload failed');
              }
            }
          } catch (thumbnailError) {
            console.error('Thumbnail upload error:', thumbnailError);
            toast.warning('Video uploaded but thumbnail upload failed');
          }
        }

        setVideoUploadInProgress(false);
        setUploadProgress(0);
        setUploadAbortController(null);
        handleChange();
      } catch (error) {
        console.error(error);
        setVideoUploadInProgress(false);
        setUploadProgress(0);
        setUploadAbortController(null);

        if (error instanceof DOMException && error.name === 'AbortError') {
          toast.info('Upload cancelled');
        } else {
          toast.error('Some error occurred, please try again');
        }
      }
    }
  };

  const handleChange = () => {
    setIsCreated(true);
  };

  const reset = () => {
    // Cancel any ongoing upload
    if (uploadAbortController) {
      uploadAbortController.abort();
      setUploadAbortController(null);
    }

    form.reset();
    setFile(null);
    setCustomThumbnailFile(null);
    setShowThumbnailUpload(false);
    setIsCreated(false);
    setIsFileUploaded(false);
    setVideoUploadInProgress(false);
    setUploadProgress(0);
  };

  return (
    <Dialog onOpenChange={reset}>
      <DialogTrigger asChild>
        <Button className='items-center gap-2 bg-[#2B5DE6] text-base font-semibold text-white hover:bg-[#2B5DE6]'>
          <span className='flex items-center gap-2'>
            <Upload className='size-5' />
            <span className='hidden sm:block'>Upload New Video</span>
          </span>
        </Button>
      </DialogTrigger>

      <DialogContent className='mx-2 h-[90vh] w-[95vw] max-w-[400px] rounded-lg p-2 sm:h-[700px] lg:mx-6 lg:max-w-[800px] lg:p-6'>
        {isFileUploaded ? (
          isCreated ? (
            <>
              {/* /// */}
              <div className='m-auto flex flex-col text-center'>
                <div className='m-auto flex size-[180px] items-center justify-center rounded-full bg-[#DC26250D]'>
                  <div className='flex size-[60px] items-center justify-center rounded-full bg-[#DC2625] sm:items-center'>
                    <Check className='text-white' strokeWidth={4} />
                  </div>
                </div>
                <p className='m-0 text-base font-medium leading-5 text-[#1D1D1B]'>
                  Video Uploaded Successfully
                </p>
              </div>
              <div className='flex items-end justify-center sm:justify-end'>
                <hr className='h-[2px] bg-[#D9D9D9]' />

                <DialogFooter className='justify-end sm:justify-center'>
                  <DialogClose asChild>
                    <Button
                      className='h-10 bg-[#DC2625] text-base font-semibold hover:bg-[#DC2625] sm:w-[100px]'
                      onClick={() => {
                        reset();
                      }}
                    >
                      Done
                    </Button>
                  </DialogClose>
                </DialogFooter>
              </div>
            </>
          ) : (
            <>
              <DialogHeader>
                <DialogTitle>Upload Video</DialogTitle>
                <hr className='fixed bg-[#D9D9D9]' />
              </DialogHeader>

              <ScrollArea ref={scrollAreaRef} className='mx-auto max-h-[calc(90vh-180px)] w-full max-w-[920px] px-2 sm:max-h-[calc(700px-180px)]'>
                <div className='flex flex-col gap-6'>
                <div className='flex flex-col gap-6 lg:flex-row'>
                  <div className='max-w-[400px]'>
                    <Card className='max-w-md rounded-3xl bg-zinc-900 text-white'>
                      <CardHeader className='relative p-1'>
                        <div className='w-full overflow-hidden rounded-t-lg p-1'>
                          <img
                            src='/card-img1.jpg'
                            alt='Video thumbnail'
                            className='h-[229px] w-[384px] rounded-2xl object-cover'
                          />
                        </div>
                      </CardHeader>
                      <CardContent className='space-y-4 px-4 pb-2 pt-1'>
                        <div className='mb-2 flex flex-col items-start gap-2'>
                          <h3 className='text-lg font-medium'>{file?.name}</h3>
                          <div className='flex items-center gap-3'>
                            <Scan size={16} />
                            <p className='text-sm font-normal'>
                              {videoInfo?.width}x{videoInfo?.height}px
                            </p>
                          </div>
                          <div className='flex items-center gap-3'>
                            <Server size={16} />
                            <p className='text-sm font-normal'>
                              {formatFileSize(file?.size ?? 0)}
                            </p>
                          </div>
                          {/* <div className='flex items-center gap-3'>
                        <Link className='h-[16px] w-[16px]' />
                        <p className='text-sm font-normal underline'>
                          video link
                        </p>
                      </div> */}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  <div className='flex max-w-[488px] flex-1 flex-col gap-6'>
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className='flex flex-col gap-4'
                      >
                        <FormField
                          control={form.control}
                          name='title'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel
                                htmlFor='title'
                                className='text-sm font-semibold text-[#1D1D1B]'
                              >
                                Video Title
                              </FormLabel>
                              <Input
                                {...field}
                                id='title'
                                name='title'
                                type='text'
                                placeholder='Enter Video Title'
                                className='w-full'
                              />
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name='description'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel
                                htmlFor='description'
                                className='text-sm font-semibold text-[#1D1D1B]'
                              >
                                Video Description
                              </FormLabel>
                              <Textarea
                                {...field}
                                id='description'
                                name='description'
                                placeholder='Enter Video Description'
                                className='h-[100px] resize-none'
                                rows={4}
                              />
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name='isDownloadable'
                          render={({ field }) => (
                            <FormItem className='flex flex-col gap-1 text-center'>
                              <div className='flex gap-2'>
                                <FormLabel
                                  htmlFor='isDownloadable'
                                  className='text-sm font-semibold text-[#1D1D1B]'
                                >
                                  Allow Download
                                </FormLabel>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </div>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name='isCommentsEnabled'
                          render={({ field }) => (
                            <FormItem className='flex flex-col gap-1 text-center'>
                              <div className='flex gap-2'>
                                <FormLabel
                                  htmlFor='isCommentsEnabled'
                                  className='text-sm font-semibold text-[#1D1D1B]'
                                >
                                  Enable Comments
                                </FormLabel>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </div>
                            </FormItem>
                          )}
                        />
                      </form>
                    </Form>

                    {/* Custom Thumbnail Upload Section */}
                    <div className='space-y-3'>
                      <div className='flex items-center justify-between'>
                        <label className='text-sm font-semibold text-[#1D1D1B]'>
                          Custom Thumbnail (Optional)
                        </label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setShowThumbnailUpload(!showThumbnailUpload)}
                        >
                          {showThumbnailUpload ? 'Hide' : 'Add Custom Thumbnail'}
                        </Button>
                      </div>

                      {showThumbnailUpload && (
                        <div className='space-y-3'>
                          {!customThumbnailFile ? (
                            <div
                              {...getThumbnailRootProps()}
                              className={`cursor-pointer rounded-lg border-2 border-dashed p-4 text-center transition-colors ${
                                isThumbnailDragActive
                                  ? 'border-primary bg-primary/10'
                                  : 'border-gray-300 hover:border-primary'
                              }`}
                            >
                              <input {...getThumbnailInputProps()} />
                              <ImageIcon className='mx-auto size-8 text-gray-400' />
                              <p className='mt-2 text-xs text-gray-600'>
                                {isThumbnailDragActive
                                  ? 'Drop the image here'
                                  : 'Drag & drop an image, or click to select'}
                              </p>
                              <p className='text-xs text-gray-500'>
                                JPEG, PNG, WebP (max 5MB)
                              </p>
                            </div>
                          ) : (
                            <div className='space-y-2'>
                              <div className='relative'>
                                <img
                                  src={createFilePreviewUrl(customThumbnailFile)}
                                  alt="Custom thumbnail preview"
                                  className="w-full h-24 object-cover rounded-lg"
                                />
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="sm"
                                  className="absolute top-1 right-1 h-6 w-6 p-0"
                                  onClick={() => {
                                    if (customThumbnailFile) {
                                      cleanupFilePreviewUrl(createFilePreviewUrl(customThumbnailFile));
                                    }
                                    setCustomThumbnailFile(null);
                                  }}
                                >
                                  ×
                                </Button>
                              </div>
                              <p className='text-xs text-gray-600'>
                                {customThumbnailFile.name} ({formatFileSize(customThumbnailFile.size)})
                              </p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    <div className='flex gap-2 rounded-lg bg-yellow-300 p-2 text-sm'>
                      <CircleAlert />
                      Uploading Pro-Res videos might lead to unexpected
                      behaviour
                    </div>
                    {/* {Boolean(file && file.size > FOUR_GB) && (
                      <div className='text-red-600'>
                        Your video must be 4GB or smaller to upload
                      </div>
                    )} */}
                  </div>
                </div>
                {videoUploadInProgress && (
                  <div className='flex flex-col items-center justify-center gap-3'>
                    <div className='text-sm font-medium'>Uploading video...</div>
                    <div className='text-sm text-gray-500'>{uploadProgress}% complete</div>
                    <Progress className='w-full' value={uploadProgress} />
                    <Button
                      onClick={() => {
                        if (uploadAbortController) {
                          uploadAbortController.abort();
                          setUploadAbortController(null);
                        }
                      }}
                      variant='outline'
                      size='sm'
                      className='h-8 px-3 text-xs'
                    >
                      Cancel
                    </Button>
                  </div>
                )}
                </div>
              </ScrollArea>
              <div className='flex items-end justify-end'>
                <hr className='h-[2px] bg-[#D9D9D9]' />
                <Button
                  type='button'
                  className='bg-[#DC2625] text-base font-semibold hover:bg-[#DC2625]'
                  onClick={() => {
                    scrollToBottom();
                    form.handleSubmit(onSubmit)();
                  }}
                  disabled={
                    isLoading || getSignedUrlLoading || videoUploadInProgress
                    // Boolean(file && file.size > FOUR_GB)
                  }
                >
                  {isLoading || getSignedUrlLoading ? (
                    <>
                      <Loader2 className='mr-2 size-4 animate-spin' />
                      Publishing
                    </>
                  ) : (
                    'Publish'
                  )}
                </Button>
              </div>
            </>
          )
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>Upload Video</DialogTitle>
              <hr className='h-[2px] bg-[#D9D9D9]' />
            </DialogHeader>
            <div className='mx-auto flex w-full max-w-md flex-col gap-4'>
              <div
                {...getRootProps()}
                className={`cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                  isDragActive
                    ? 'border-primary bg-primary/10'
                    : 'border-gray-300 hover:border-primary'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className='mx-auto size-12 text-gray-400' />
                <p className='mt-2 text-sm text-gray-600'>
                  {isDragActive
                    ? 'Drop the file here'
                    : 'Drag and drop, or click to select a file to upload'}
                </p>
              </div>
              {file && (
                <p className='mt-4 text-center text-sm'>
                  Selected file: <strong>{file.name}</strong>
                </p>
              )}
              <Button
                disabled={!file}
                onClick={() => {
                  setIsFileUploaded(true);
                }}
              >
                Upload file
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default UploadVideo;
