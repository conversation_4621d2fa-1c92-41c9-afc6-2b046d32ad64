import { MoreVertical, FolderPlus } from 'lucide-react';
import { useState } from 'react';
import { DeleteProjectAlertDialog } from '~components/custom/delete-project-alert-dialog';
import EditProject from '~components/custom/edit-project';
import { Button } from '~components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~components/ui/dropdown-menu';

import { type Project } from '~features/project/types/project';
import { CreateSubProjectDialog } from '~features/sub-project/components/create-sub-project-dialog';



interface FoldersMenuProps {
  project: Project;
}

const FoldersMenu = ({ project }: FoldersMenuProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isCreateSubProjectOpen, setIsCreateSubProjectOpen] = useState(false);

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild className=''>
          <Button
            className='w-min p-0 ring-0 hover:bg-transparent hover:text-accent focus-visible:ring-0'
            variant={'ghost'}
          >
            <MoreVertical className='size-5' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className='w-[150px] gap-2 rounded-md border'>
          <DropdownMenuGroup>
            <DropdownMenuItem
              className='cursor-pointer'
              onClick={() => setIsCreateSubProjectOpen(true)}
            >
              <FolderPlus className="mr-2 h-4 w-4" />
              <span>Create Folder</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator className='bg-[#D9D9D9]' />
            <DropdownMenuItem
              className='cursor-pointer'
              asChild
              onClick={() => {
                setIsOpen(true);
              }}
            >
              <span>Edit Project Details</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator className='bg-[#D9D9D9]' />
            <DropdownMenuItem asChild className='cursor-pointer'>
              <DeleteProjectAlertDialog project={project} />
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
      <EditProject isOpen={isOpen} setIsOpen={setIsOpen} project={project} />
      <CreateSubProjectDialog
        isOpen={isCreateSubProjectOpen}
        setIsOpen={setIsCreateSubProjectOpen}
        projectId={project._id}
      />
    </div>
  );
};

export default FoldersMenu;
