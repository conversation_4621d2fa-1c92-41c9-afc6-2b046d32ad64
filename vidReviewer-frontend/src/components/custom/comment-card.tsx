import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'lucide-react';
import { useState } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '~components/ui/accordion';
import { Avatar, AvatarFallback } from '~components/ui/avatar';
import { Button } from '~components/ui/button';
import { Textarea } from '~components/ui/textarea';
import { cn } from '~lib/utils';
import { useAppSelector } from '~stores/hooks';

import { useAddCommentMutation } from '~features/comment/api/add-comment';
import { CommentReply } from '~features/comment/components/comment-reply';
import { type Comment } from '~features/comment/types/comment';
import { formatDate } from '~features/comment/utils/format-date';
import { splitNameInitials } from '~features/comment/utils/split-name-initials';
import { selectUser } from '~features/user/store/user-slice';

interface commentProps {
  comment: Comment;
  isActive: boolean;
  isExternalUser?: boolean;
  onTimeStampClick: (time: string) => void;
}

const CommentCard = ({
  comment,
  isActive,
  isExternalUser,
  onTimeStampClick,
}: commentProps) => {
  const user = useAppSelector(selectUser);

  const userDetails = localStorage.getItem('userDetails')
    ? JSON.parse(localStorage.getItem('userDetails') || '{}')
    : null;

  const externalUser = localStorage.getItem('externalUser')
    ? JSON.parse(localStorage.getItem('externalUser') || '{}')
    : null;

  const [reply, setReply] = useState('');
  const [addComment, { isLoading }] = useAddCommentMutation();

  const handleAddComment = async () => {
    if (reply) {
      try {
        const response = await addComment({
          videoId: comment.videoId,
          userId: isExternalUser ? externalUser._id : user.user?._id,
          content: reply,
          parentCommentId: comment._id,
          time: comment.time,
          addedBy: isExternalUser
            ? externalUser.name
            : userDetails
              ? userDetails.userName
              : 'Anonymous',
        });
        console.log('🚀 ~ handleAddComment ~ response:', response);
        setReply('');
      } catch (error) {
        console.error(error);
      }
    }
  };

  const handleEnter = async (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        return;
      }
      e.preventDefault();
      await handleAddComment();
    }
  };

  return (
    <div
      className={cn(
        isActive
          ? 'border-[#DC26251A] bg-[#DC26250D]'
          : 'border-[#F9F9F9] bg-[#F9F9F9]',
        'rounded-[20px] border-2 p-4 sm:w-[calc(400px-16px-16px)] sm:p-6',
      )}
    >
      <div className='mb-2 flex justify-between'>
        <div className='relative flex items-center'>
          <Avatar className='size-6'>
            <AvatarFallback className='bg-[#1FAE47] text-xs text-white'>
              {splitNameInitials(comment.addedBy)}
            </AvatarFallback>
          </Avatar>
          {/* <Avatar className='absolute left-[17px] h-[30px] w-[30px] border-[3px] border-white'>
            <AvatarImage
              src='/placeholder.svg'
              alt='Collaborator Avatar'
              className='bg-red-600'
            />
            <AvatarFallback className='bg-red-600 text-xs text-white'>
              D
            </AvatarFallback>
          </Avatar> */}
        </div>
        <CircleCheck className='text-[#1D1D1B]' />
      </div>

      <div className='mb-2 flex gap-1.5'>
        <h3 className='text-sm font-semibold'>{comment.addedBy}</h3>
        <p className='text-sm font-medium text-muted-foreground'>
          {formatDate(comment.createdAt)}
        </p>
      </div>

      <p className='mb-2 h-auto whitespace-pre-wrap break-words text-left text-sm font-normal text-[#1D1D1B]'>
        {comment.content}
      </p>

      <div className='text-sm font-medium text-muted-foreground'>
        <Accordion type='single' collapsible>
          <AccordionItem value='replies'>
            <div className='flex items-center justify-between gap-4 sm:gap-9'>
              <Button
                className='rounded-full bg-[#DC26251A] px-2 text-sm font-medium text-[#DC2625] hover:bg-[#DC26251A]'
                onClick={() => {
                  onTimeStampClick(comment.time);
                }}
              >
                {comment.time}
              </Button>
              <AccordionTrigger>
                {comment.replies.length} replies
              </AccordionTrigger>
              <CommentReply comment={comment} isExternalUser={isExternalUser} />
            </div>
            <AccordionContent>
              {comment.replies.map((reply, index) => (
                <div
                  key={reply._id}
                  className={cn(
                    comment.replies.length - 1 === index ? '' : 'mb-2',
                    'bg-[#F9F9F9] px-4 py-1',
                  )}
                >
                  <div className='flex justify-between'>
                    <div className='mb-1 flex items-center gap-2'>
                      <div className='flex items-center gap-2'>
                        <Avatar className='size-6'>
                          <AvatarFallback className='bg-[#1FAE47] text-xs text-white'>
                            {splitNameInitials(reply.addedBy)}
                          </AvatarFallback>
                        </Avatar>
                        <h3 className='text-sm font-semibold'>
                          {reply.addedBy}
                        </h3>
                      </div>
                      <p className='text-sm font-medium text-muted-foreground'>
                        {formatDate(reply.createdAt)}
                      </p>
                    </div>
                  </div>
                  <p className='mb-2 h-auto whitespace-pre-wrap break-words'>
                    {reply.content}
                  </p>
                </div>
              ))}
              <div className='relative p-2'>
                <Textarea
                  placeholder='Reply'
                  rows={2}
                  className='mt-2 resize-none border-none bg-white'
                  value={reply}
                  onChange={(e) => {
                    setReply(e.target.value);
                  }}
                  onKeyDown={handleEnter}
                />
                <Button
                  variant={'ghost'}
                  className='absolute bottom-2 right-2 h-fit p-1'
                  onClick={handleAddComment}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className='mr-2 size-4 animate-spin' />
                  ) : (
                    <SendHorizontal
                      // className='text-[#8A8A8A]'
                      size={24}
                      // fill='#8A8A8A'
                    />
                  )}
                </Button>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
};

export default CommentCard;
