import {
  CircleChevronLeft,
  CircleChevronRight,
  SendHorizontal,
  ThumbsUp,
} from 'lucide-react';
import { useState } from 'react';
import { Avatar, AvatarFallback } from '~components/ui/avatar';
import { Button } from '~components/ui/button';
import { Dialog, DialogContent } from '~components/ui/dialog';
import { Textarea } from '~components/ui/textarea';

import CommentMenu from './comment-menu';

interface CommentSectionProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
}

const CommentSection = ({ isOpen, setIsOpen }: CommentSectionProps) => {
  const [isLike, setIsLike] = useState([
    { id: 1, liked: false },
    { id: 2, liked: false },
    { id: 3, liked: false },
  ]);

  const handleLike = (id: number) => {
    setIsLike((prev) =>
      prev.map((c) => (c.id === id ? { ...c, liked: !c.liked } : c)),
    );
  };

  const [comment, setComment] = useState('');

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className='w-[400px] rounded-3xl border border-[#D9D9D9] bg-white py-2'>
        {/* <DialogHeader className='hidden'>
          <DialogTitle>Comments</DialogTitle>
        </DialogHeader> */}
        <div className='flex justify-between p-3'>
          <div className='flex gap-2'>
            <Button variant='ghost' className='h-fit p-0'>
              <CircleChevronLeft />
            </Button>
            <Button variant='ghost' className='h-fit p-0'>
              <CircleChevronRight />
            </Button>
          </div>
          {/* <div className='flex gap-2'>
            <Button variant='ghost' className='h-fit p-0'>
              <CircleCheck strokeWidth='2px' />
            </Button>
            <Button variant='ghost' className='h-fit p-0'>
              <CircleX />
            </Button>
          </div> */}
        </div>
        <hr className='my-[6px] h-[2px] bg-[#D9D9D9]' />
        {isLike.map((item) => (
          <div key={item.id} className='flex gap-3 p-3'>
            <div>
              <Avatar className='size-6'>
                <AvatarFallback className='bg-[#1FAE47] text-xs text-white'>
                  JD
                </AvatarFallback>
              </Avatar>
            </div>
            <div>
              <div className='flex justify-between'>
                <div className='mb-1 flex gap-1.5'>
                  <h3 className='text-sm font-semibold'>Jane Doe</h3>
                  <p className='text-sm font-medium text-muted-foreground'>
                    20 mins ago
                  </p>
                </div>
                <CommentMenu />
              </div>
              <div className='flex gap-2'>
                <p className='mb-2 text-sm font-normal text-[#1D1D1B]'>
                  Can you try to improve the video in this part?
                </p>
                <button
                  onClick={() => {
                    handleLike(item.id);
                  }}
                >
                  {item.liked ? (
                    <ThumbsUp strokeWidth='1px' fill='#DC2625' />
                  ) : (
                    <ThumbsUp />
                  )}
                </button>
              </div>
            </div>
          </div>
        ))}
        <div className='flex gap-3 p-3'>
          <Avatar className='size-6'>
            <AvatarFallback className='bg-[#1FAE47] text-xs text-white'>
              JD
            </AvatarFallback>
          </Avatar>

          <div className='relative'>
            <Textarea
              placeholder='Reply'
              rows={4}
              className='w-[290px] resize-none border-none bg-[#F6F8FA]'
              value={comment}
              onChange={(e) => {
                setComment(e.target.value);
              }}
            />
            {comment.length < 1 ? (
              <Button
                variant={'ghost'}
                className='absolute bottom-2 right-5 h-fit p-0'
              >
                <SendHorizontal
                  className='text-[#8A8A8A]'
                  size={24}
                  fill='#8A8A8A'
                />
              </Button>
            ) : (
              <div className='bottom-0 flex gap-1'>
                <Button variant='outline'>Cancel</Button>
                <Button className='bg-[#2B5DE6] hover:bg-[#2B5DE6]'>
                  Send
                </Button>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CommentSection;
