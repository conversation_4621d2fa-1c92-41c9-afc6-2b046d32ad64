import { Upload, X } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';

import { Button } from '~components/ui/button';
import { Progress } from '~components/ui/progress';
import { cn } from '~lib/utils';

import { useGetThumbnailSignedUrlMutation } from '~features/video/api/get-thumbnail-signed-url';
import { useUpdateVideoThumbnailMutation } from '~features/video/api/update-video-thumbnail';
import { type Video } from '~features/video/types/video';
import {
  isValidThumbnailFileType,
  isValidThumbnailFileSize,
  formatThumbnailFileSize,
  createFilePreviewUrl,
  cleanupFilePreviewUrl,
} from '~features/video/utils/thumbnail-utils';

interface CustomThumbnailUploadProps {
  video: Video;
  onUploadSuccess?: (updatedVideo: Video) => void;
  onUploadError?: (error: string) => void;
  className?: string;
}

export const CustomThumbnailUpload = ({
  video,
  onUploadSuccess,
  onUploadError,
  className,
}: CustomThumbnailUploadProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadAbortController, setUploadAbortController] = useState<AbortController | null>(null);

  const [getThumbnailSignedUrl] = useGetThumbnailSignedUrlMutation();
  const [updateVideoThumbnail] = useUpdateVideoThumbnailMutation();

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      
      // Validate file type
      if (!isValidThumbnailFileType(file.type)) {
        toast.error('Please select a valid image file (JPEG, PNG, or WebP)');
        return;
      }

      // Validate file size
      if (!isValidThumbnailFileSize(file.size)) {
        toast.error('File size must be less than 5MB');
        return;
      }

      // Clean up previous preview URL
      if (previewUrl) {
        cleanupFilePreviewUrl(previewUrl);
      }

      setSelectedFile(file);
      setPreviewUrl(createFilePreviewUrl(file));
    }
  }, [previewUrl]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp'],
    },
    multiple: false,
    maxSize: 5 * 1024 * 1024, // 5MB
  });

  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Create abort controller for cancellation
      const abortController = new AbortController();
      setUploadAbortController(abortController);

      // Step 1: Get signed URL
      const signedUrlResult = await getThumbnailSignedUrl({
        fileName: selectedFile.name,
        fileType: selectedFile.type,
        videoId: video._id,
      });

      if (!signedUrlResult.data) {
        throw new Error('Failed to get upload URL');
      }

      const { url, fileName } = signedUrlResult.data;

      // Step 2: Upload file to GCS
      const uploadResponse = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': selectedFile.type,
        },
        body: selectedFile,
        signal: abortController.signal,
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload thumbnail');
      }

      setUploadProgress(50);

      // Step 3: Update video with custom thumbnail
      const updateResult = await updateVideoThumbnail({
        videoId: video._id,
        gcpFileName: fileName,
      });

      if (!updateResult.data) {
        throw new Error('Failed to update video thumbnail');
      }

      setUploadProgress(100);
      toast.success('Custom thumbnail uploaded successfully');
      
      // Clean up
      if (previewUrl) {
        cleanupFilePreviewUrl(previewUrl);
      }
      setSelectedFile(null);
      setPreviewUrl(null);
      
      onUploadSuccess?.(updateResult.data.video);
    } catch (error: any) {
      if (error.name === 'AbortError') {
        toast.info('Upload cancelled');
      } else {
        const errorMessage = error.message || 'Failed to upload thumbnail';
        toast.error(errorMessage);
        onUploadError?.(errorMessage);
      }
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      setUploadAbortController(null);
    }
  };

  const handleCancel = () => {
    if (uploadAbortController) {
      uploadAbortController.abort();
    }
    
    if (previewUrl) {
      cleanupFilePreviewUrl(previewUrl);
    }
    
    setSelectedFile(null);
    setPreviewUrl(null);
    setIsUploading(false);
    setUploadProgress(0);
  };

  return (
    <div className={cn('space-y-4', className)}>
      {!selectedFile ? (
        <div
          {...getRootProps()}
          className={cn(
            'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
            isDragActive
              ? 'border-primary bg-primary/5'
              : 'border-gray-300 hover:border-primary hover:bg-gray-50'
          )}
        >
          <input {...getInputProps()} />
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-sm text-gray-600 mb-2">
            {isDragActive
              ? 'Drop the image here...'
              : 'Drag & drop an image here, or click to select'}
          </p>
          <p className="text-xs text-gray-500">
            Supports JPEG, PNG, WebP (max 5MB)
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="relative">
            <img
              src={previewUrl!}
              alt="Thumbnail preview"
              className="w-full h-48 object-cover rounded-lg"
            />
            {!isUploading && (
              <Button
                variant="destructive"
                size="sm"
                className="absolute top-2 right-2"
                onClick={handleCancel}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{selectedFile.name}</span>
            <span>{formatThumbnailFileSize(selectedFile.size)}</span>
          </div>

          {isUploading && (
            <div className="space-y-2">
              <Progress value={uploadProgress} className="w-full" />
              <div className="flex items-center justify-between text-sm">
                <span>Uploading thumbnail...</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  disabled={!uploadAbortController}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {!isUploading && (
            <div className="flex gap-2">
              <Button onClick={handleUpload} className="flex-1">
                <Upload className="h-4 w-4 mr-2" />
                Upload Thumbnail
              </Button>
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
