import { <PERSON><PERSON>he<PERSON> } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '~components/ui/avatar';
import { Button } from '~components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~components/ui/dialog';

interface ShareProjectProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
}

const collaborators = [
  {
    name: '<PERSON>',
    collaboratorsCount: 4,
    phoneNumber: '999839183',
    email: '<PERSON><PERSON>.<PERSON><PERSON>@gmail.com',
    isShared: false,
  },
  {
    name: '<PERSON><PERSON>',
    collaboratorsCount: 5,
    phoneNumber: '998735636289',
    email: '<EMAIL>',
    isShared: true,
  },
  {
    name: '<PERSON>',
    collaboratorsCount: 6,
    phoneNumber: '99999999999',
    email: '<PERSON>.<PERSON><PERSON>@gmail.com',
    isShared: false,
  },
  {
    name: '<PERSON><PERSON>',
    collaboratorsCount: 7,
    phoneNumber: '123456789',
    email: '<EMAIL>',
    isShared: false,
  },
];

const CollaboratorItem = ({
  name,
  phoneNumber,
  email,
  isShared,
}: {
  name: string;
  phoneNumber: string;
  email: string;
  isShared: boolean;
}) => (
  <div className='flex items-center justify-between gap-4'>
    <div className='flex items-center gap-3'>
      <Avatar className='h-8 w-8'>
        <AvatarImage
          src='/placeholder.svg?height=40&width=40'
          className='bg-[#925721]'
        />
        <AvatarFallback className='bg-[#925721] text-white'>JD</AvatarFallback>
      </Avatar>
      <div className='space-y-1'>
        <p className='text-sm font-medium text-[#1D1D1B]'>{name}</p>
        <div className='flex items-center gap-2 text-xs text-[#1D1D1B]'>
          <span>{phoneNumber}</span>
          <span>•</span>
          <span>{email}</span>
        </div>
      </div>
    </div>
    {isShared ? (
      <Button className='flex items-center gap-1 rounded-md'>
        <CircleCheck />
        Shared
      </Button>
    ) : (
      <Button
        variant='outline'
        className='rounded-md border border-[#1D1D1B] text-sm font-semibold'
      >
        Share
      </Button>
    )}
  </div>
);

const ShareProject = ({ isOpen, setIsOpen }: ShareProjectProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className='w-[552px] max-w-none rounded-[30px]'>
        <DialogHeader>
          <DialogTitle>Share “Project 1”</DialogTitle>
          <hr className='my-2 h-[2px] bg-[#D9D9D9]' />
        </DialogHeader>

        <div className='flex flex-col gap-6'>
          {/* Header Section */}
          <div className='flex items-center justify-between'>
            <h3 className='text-base font-semibold'>
              Collaborators ({collaborators.length})
            </h3>
            <Button
              variant='destructive'
              className='h-8 px-4 text-sm font-medium text-white'
            >
              Share to all
            </Button>
          </div>

          {/* Collaborators List */}
          <div className='space-y-4'>
            {collaborators.map((collaborator, index) => (
              <CollaboratorItem key={index} {...collaborator} />
            ))}
          </div>
        </div>

        <hr className='my-4 h-[2px] bg-[#D9D9D9]' />

        {/* Footer Section */}
        <DialogFooter className='flex justify-end'>
          <DialogClose asChild>
            <Button
              type='button'
              variant='secondary'
              className='h-10 w-24 bg-[#DC2625] text-sm font-semibold text-white hover:bg-[#DC2625]'
            >
              Done
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ShareProject;
