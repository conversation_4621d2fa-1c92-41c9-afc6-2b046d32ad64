import { useEffect, useRef, useState } from 'react';

interface Thumbnail {
  time: number;
  url: string;
}

export function VideoTimeline({ videoSrc }: { videoSrc: string }) {
  const [thumbnails, setThumbnails] = useState<Thumbnail[]>([]);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    if (!video || !canvas) return;

    const generateThumbnails = () => {
      const duration = video.duration;
      const thumbnailCount = 6;
      const interval = duration / thumbnailCount;
      const newThumbnails: Thumbnail[] = [];

      for (let i = 0; i < thumbnailCount; i++) {
        const time = i * interval;
        video.currentTime = time;
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        canvas
          .getContext('2d')
          ?.drawImage(video, 0, 0, canvas.width, canvas.height);
        newThumbnails.push({
          time,
          url: canvas.toDataURL('image/jpeg'),
        });
      }

      setThumbnails(newThumbnails);
    };

    video.addEventListener('loadedmetadata', generateThumbnails);
    return () =>
      video.removeEventListener('loadedmetadata', generateThumbnails);
  }, [videoSrc]);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className='relative'>
      <div className='flex gap-2 overflow-x-auto pb-4'>
        {thumbnails.map((thumbnail, index) => (
          <div key={index} className='relative flex-none'>
            <img
              src={thumbnail.url}
              alt={`Thumbnail ${index + 1}`}
              width={160}
              height={90}
              className='aspect-video w-40 rounded-lg object-cover'
            />
            <div className='absolute -top-2 left-1/2 -translate-x-1/2'>
              <div className='h-2 w-2 rounded-full bg-primary' />
            </div>
            <div className='absolute bottom-1 right-1 rounded bg-black/60 px-1 text-xs text-white'>
              {formatTime(thumbnail.time)}
            </div>
          </div>
        ))}
      </div>
      <video ref={videoRef} src={videoSrc} className='hidden' />
      <canvas ref={canvasRef} className='hidden' />
    </div>
  );
}
