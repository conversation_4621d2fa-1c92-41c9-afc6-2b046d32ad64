import { Upload } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '~components/ui/button';

export function FileDropzone() {
  const [file, setFile] = useState<File | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      setFile(acceptedFiles[0]);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false, // Allow only one file to be dropped
  });

  return (
    <div className='mx-auto w-full max-w-md'>
      <div
        {...getRootProps()}
        className={`cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
          isDragActive
            ? 'border-primary bg-primary/10'
            : 'border-gray-300 hover:border-primary'
        }`}
      >
        <input {...getInputProps()} />
        <Upload className='mx-auto h-12 w-12 text-gray-400' />
        <p className='mt-2 text-sm text-gray-600'>
          {isDragActive
            ? 'Drop the file here'
            : 'Drag and drop a file here, or click to select a file'}
        </p>
        <Button className='mt-4' onClick={(e) => e.stopPropagation()}>
          Select File
        </Button>
      </div>
      {file && (
        <p className='mt-4 text-center text-sm'>
          Selected file: <strong>{file.name}</strong>
        </p>
      )}
    </div>
  );
}
