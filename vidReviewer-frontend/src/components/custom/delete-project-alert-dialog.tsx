import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '~components/ui/alert-dialog';
import { Button } from '~components/ui/button';

import { useDeleteProjectMutation } from '~features/project/api/delete-project';
import type { Project } from '~features/project/types/project';

interface DeleteProjectAlertDialogProps {
  project: Project;
}

export function DeleteProjectAlertDialog({
  project,
}: DeleteProjectAlertDialogProps) {
  const [deleteProject, { isLoading }] = useDeleteProjectMutation();

  const onDeleteProject = async () => {
    try {
      await deleteProject({ projectId: project._id }).unwrap();
      toast.success('Project Deleted successfully');
    } catch (error) {
      console.error(error);
      toast.error('Failed to delete project');
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant={'ghost'}
          className='w-full cursor-pointer justify-start p-0 pl-2 font-normal hover:bg-transparent'
        >
          Delete Project
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete your
            project.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          {!isLoading && <AlertDialogCancel>Cancel</AlertDialogCancel>}
          <Button
            className='bg-red-600 hover:bg-red-600'
            disabled={isLoading}
            onClick={() => onDeleteProject()}
          >
            {isLoading ? (
              <>
                <Loader2 className='mr-2 size-4 animate-spin' />
                Deleting
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
