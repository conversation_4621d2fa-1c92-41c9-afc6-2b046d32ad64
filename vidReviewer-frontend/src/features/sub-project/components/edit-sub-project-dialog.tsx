import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '~components/ui/button';
import {
  Di<PERSON>,
  DialogClose,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '~components/ui/dialog';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~components/ui/form';
import { Input } from '~components/ui/input';
import { Textarea } from '~components/ui/textarea';

import { useUpdateSubProjectMutation } from '~features/sub-project/api/update-sub-project';
import { type SubProject } from '~features/sub-project/types/sub-project';

const formSchema = z.object({
  name: z.string().min(1, 'Folder name is required').max(100, 'Folder name is too long'),
  description: z.string().max(500, 'Description is too long').optional(),
});

type FormData = z.infer<typeof formSchema>;

interface EditSubProjectDialogProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  subProject: SubProject;
  projectId: string;
}

export const EditSubProjectDialog = ({ 
  isOpen, 
  setIsOpen, 
  subProject,
  projectId
}: EditSubProjectDialogProps) => {
  const [updateSubProject, { isLoading }] = useUpdateSubProjectMutation();
  
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: subProject.name,
      description: subProject.description || '',
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      await updateSubProject({
        projectId,
        subProjectId: subProject._id,
        name: data.name,
        description: data.description,
      }).unwrap();
      
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to update folder:', error);
    }
  };

  const handleClose = () => {
    form.reset({
      name: subProject.name,
      description: subProject.description || '',
    });
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Folder</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Folder Name</FormLabel>
                  <Input
                    placeholder="Enter folder name"
                    {...field}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <Textarea
                    placeholder="Enter folder description"
                    {...field}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Update Folder
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
