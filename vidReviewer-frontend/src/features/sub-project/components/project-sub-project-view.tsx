import { useNavigate } from '@tanstack/react-router';
import {
  ArrowRight,
  Calendar,
  FolderPlus,
  Plus,
  Video as VideoIcon,
} from 'lucide-react';
import { useState } from 'react';
import UploadVideo from '~components/custom/upload-video';
import { Button } from '~components/ui/button';

import { type SubProject } from '~features/sub-project/types/sub-project';
import { type Video } from '~features/video/types/video';

import { CreateSubProjectDialog } from './create-sub-project-dialog';
import { DraggableVideoItem } from './draggable-video-item';

interface ProjectSubProjectViewProps {
  subProjects: Array<SubProject>;
  videos: Array<Video>;
  projectId: string;
  projectTitle: string;
}

export const ProjectSubProjectView = ({
  subProjects,
  videos,
  projectId,
  projectTitle: _projectTitle,
}: ProjectSubProjectViewProps) => {
  const navigate = useNavigate();
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const goToSubProject = (subProjectId: string): void => {
    void navigate({
      to: '/folder/$folderId',
      params: { folderId: subProjectId },
    });
  };

  const goToVideo = (videoId: string): void => {
    void navigate({ to: '/review-video/$videoId', params: { videoId } });
  };

  // Removed sub-project limit validation

  return (
    <div className='space-y-8'>
      {/* Project Header */}
      <div className='flex items-start justify-between'>
        <div>
          <p className='text-gray-600'>
            {subProjects.length} folder{subProjects.length !== 1 ? 's' : ''},{' '}
            {videos.length} video{videos.length !== 1 ? 's' : ''}
          </p>
        </div>

        <div className='flex items-center gap-3'>
          <Button
            onClick={() => {
              setShowCreateDialog(true);
            }}
            variant='outline'
            className='flex items-center gap-2'
          >
            <Plus className='size-4' />
            New Folder
          </Button>

          <UploadVideo projectId={projectId} />
        </div>
      </div>

      {/* Folders Section */}
      {subProjects.length > 0 && (
        <div>
          <h2 className='mb-6 text-xl font-semibold text-gray-900'>Folders</h2>
          <div className='space-y-4'>
            {subProjects.map((subProject) => (
              <div
                key={subProject._id}
                className='cursor-pointer rounded-lg border border-gray-200 bg-white p-6 transition-shadow hover:shadow-md'
                onClick={() => {
                  goToSubProject(subProject._id);
                }}
              >
                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-4'>
                    {/* Thumbnail placeholder */}
                    <div className='flex size-16 items-center justify-center rounded-lg bg-gray-100'>
                      <FolderPlus className='size-8 text-gray-400' />
                    </div>

                    {/* Content */}
                    <div>
                      <h3 className='mb-1 text-lg font-semibold text-gray-900'>
                        {subProject.name}
                      </h3>
                      {subProject.description && (
                        <p className='mb-2 text-sm text-gray-600'>
                          {subProject.description}
                        </p>
                      )}
                      <div className='flex items-center gap-4 text-sm text-gray-500'>
                        <div className='flex items-center gap-1'>
                          <VideoIcon className='size-4' />
                          {subProject.videoCount ?? 0} videos
                        </div>
                        <div className='flex items-center gap-1'>
                          <Calendar className='size-4' />
                          {new Date(subProject.createdAt).toLocaleDateString(
                            'en-US',
                            {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric',
                            },
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action */}
                  <div className='flex items-center gap-2 text-blue-600 hover:text-blue-700'>
                    <span className='text-sm font-medium'>View Project</span>
                    <ArrowRight className='size-4' />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Videos Section */}
      {videos.length > 0 && (
        <div>
          <h2 className='mb-6 text-xl font-semibold text-gray-900'>Videos</h2>
          <div className='grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
            {videos.map((video) => (
              <DraggableVideoItem
                key={video._id}
                video={video}
                onClick={() => {
                  goToVideo(video._id);
                }}
              />
            ))}
          </div>
        </div>
      )}

      {/* Empty state */}
      {subProjects.length === 0 && videos.length === 0 && (
        <div className='py-20 text-center'>
          <div className='mx-auto mb-6 flex size-24 items-center justify-center rounded-full bg-gray-100'>
            <FolderPlus className='size-8 text-gray-400' />
          </div>
          <h3 className='mb-3 text-lg font-medium text-gray-900'>
            No content yet
          </h3>
          <p className='mb-6 text-gray-500'>
            Create folders to organize your videos or upload videos directly to
            this project.
          </p>
          <div className='flex items-center justify-center gap-4'>
            <Button
              onClick={() => {
                setShowCreateDialog(true);
              }}
              variant='outline'
              className='flex items-center gap-2'
            >
              <Plus className='size-4' />
              New Folder
            </Button>
            <UploadVideo projectId={projectId} />
          </div>
        </div>
      )}

      {/* Create Sub-Project Dialog */}
      <CreateSubProjectDialog
        isOpen={showCreateDialog}
        setIsOpen={setShowCreateDialog}
        projectId={projectId}
      />
    </div>
  );
};
