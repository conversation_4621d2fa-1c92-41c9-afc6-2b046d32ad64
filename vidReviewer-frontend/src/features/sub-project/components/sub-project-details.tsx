import { useNavigate } from '@tanstack/react-router';
import { Loader2, FolderPlus, Plus, ArrowRight, Calendar, Video as VideoIcon } from 'lucide-react';
import { useState } from 'react';
import Header from '~components/custom/header';
import { Button } from '~components/ui/button';
import UploadVideo from '~components/custom/upload-video';

import { useGetFolderQuery } from '~features/folder/api/get-folder';
import { useGetFoldersQuery } from '~features/folder/api/get-folders';
import { useGetProjectQuery } from '~features/project/api/get-project';
import { useGetSubProjectContentsQuery } from '~features/sub-project/api/get-sub-project-contents';
import { useGetSubProjectTreeQuery } from '~features/sub-project/api/get-sub-project-tree';
import { useAppSelector } from '~stores/hooks';
import { selectUser } from '~features/user/store/user-slice';
import { ProjectBreadcrumbs } from '~features/project/components/project-breadcrumbs';
import { buildSubProjectBreadcrumbs } from '~features/project/utils/project-utils';

import { CreateSubProjectDialog } from './create-sub-project-dialog';
import { DraggableVideoItem } from './draggable-video-item';

interface SubProjectDetailsProps {
  subProjectId: string;
  projectId: string;
}

export const SubProjectDetails = ({ subProjectId, projectId }: SubProjectDetailsProps) => {
  const navigate = useNavigate();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const user = useAppSelector(selectUser);

  // Get sub-project info
  const { data: subProjectData, isFetching: isFetchingSubProject } = useGetFolderQuery(
    { folderId: subProjectId },
    { skip: !subProjectId }
  );

  // Get project info
  const { data: project, isFetching: isFetchingProject } = useGetProjectQuery({
    projectId,
  });

  // Get parent folder if project is in a folder
  const { data: parentFolder } = useGetFolderQuery(
    { folderId: project?.folderId || '' },
    { skip: !project?.folderId }
  );

  // Get all folders for breadcrumb building
  const { data: allFoldersData } = useGetFoldersQuery(
    { userId: user.user?._id || '' },
    { skip: !user.user?._id }
  );

  // Get sub-project tree for breadcrumb building
  const { data: subProjectTree } = useGetSubProjectTreeQuery({
    projectId,
    includeVideos: false,
  });

  // Debug the raw sub-project tree data
  console.log('🌳 Raw subProjectTree data:', subProjectTree);

  // Get sub-project contents
  const { data: contents, isFetching: isFetchingContents } = useGetSubProjectContentsQuery({
    projectId,
    subProjectId,
  });

  const isLoading = isFetchingSubProject || isFetchingContents || isFetchingProject;

  const handleBackNavigation = () => {
    // If this sub-project has a parent sub-project, navigate to it
    // Otherwise, navigate back to the parent project
    if (subProjectData?.parentFolder) {
      void navigate({ to: '/folder/$folderId', params: { folderId: subProjectData.parentFolder } });
    } else {
      void navigate({ to: '/folder/$folderId', params: { folderId: projectId } });
    }
  };

  if (isLoading) {
    return (
      <>
        <Header title="Loading..." />
        <div className="flex h-[calc(100vh-64px)] w-full items-center justify-center">
          <Loader2 size={48} className="animate-spin" />
        </div>
      </>
    );
  }

  if (!subProjectData) {
    return (
      <>
        <Header title="Sub-Project Not Found" />
        <div className="flex h-[calc(100vh-64px)] w-full items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Sub-Project Not Found</h2>
            <p className="text-gray-600">The sub-project you're looking for doesn't exist or has been deleted.</p>
          </div>
        </div>
      </>
    );
  }

  const subProjects = contents?.subProjects || [];
  const videos = contents?.videos || [];

  // Convert folder data to sub-project format for breadcrumb building
  const subProjectForBreadcrumb = subProjectData && subProjectData.contextType === 'project' ? {
    _id: subProjectData._id,
    name: subProjectData.name,
    description: subProjectData.description,
    createdBy: subProjectData.createdBy,
    contextType: 'project' as const,
    contextId: subProjectData.contextId || projectId,
    parentFolder: subProjectData.parentFolder,
    path: subProjectData.path,
    level: subProjectData.level,
    createdAt: subProjectData.createdAt,
    updatedAt: subProjectData.updatedAt,
  } : null;

  // Flatten sub-project tree for breadcrumb building
  const flattenSubProjects = (subProjects: any[]): any[] => {
    console.log('🔄 Flattening sub-projects:', subProjects);
    const flattened: any[] = [];
    for (const sp of subProjects) {
      console.log('📁 Adding sub-project to flattened:', { id: sp._id, name: sp.name, path: sp.path });
      flattened.push(sp);
      if (sp.children && sp.children.length > 0) {
        console.log('👶 Found children, recursing:', sp.children.length);
        flattened.push(...flattenSubProjects(sp.children));
      }
    }
    console.log('✅ Final flattened result:', flattened.map(sp => ({ id: sp._id, name: sp.name, path: sp.path })));
    return flattened;
  };

  // Build breadcrumbs
  const flattenedSubProjects = flattenSubProjects(subProjectTree?.folderTree || []);

  // Debug logging
  console.log('🔍 Breadcrumb Debug Info:');
  console.log('Current sub-project:', subProjectForBreadcrumb);
  console.log('Flattened sub-projects:', flattenedSubProjects);
  console.log('Project:', project);

  const breadcrumbs = project && subProjectForBreadcrumb ? buildSubProjectBreadcrumbs(
    project,
    subProjectForBreadcrumb,
    parentFolder || null,
    allFoldersData?.folders || [],
    flattenedSubProjects
  ) : [];

  console.log('Built breadcrumbs:', breadcrumbs);

  // Handle breadcrumb navigation
  const handleBreadcrumbNavigate = (id: string | null, type: string) => {
    if (type === 'home' || id === null) {
      void navigate({ to: '/folders' });
    } else if (type === 'folder') {
      void navigate({ to: '/folder/$folderId', params: { folderId: id } });
    } else if (type === 'project') {
      void navigate({ to: '/folder/$folderId', params: { folderId: id } });
    } else if (type === 'sub-project') {
      void navigate({ to: '/folder/$folderId', params: { folderId: id } });
    }
  };

  return (
    <>
      <Header
        title={subProjectData.name}
        projectId={projectId}
        showSubProjectActions={false}
        showUploadButton={false}
        onBackClick={handleBackNavigation}
        shareResourceType="folder"
        shareResourceId={subProjectId}
        shareResourceTitle={subProjectData.name}
      />
      <div className="p-6 pt-8 sm:p-12 sm:pt-12">
        <div className="space-y-8">
          {/* Breadcrumbs */}
          <div className="mb-6">
            <ProjectBreadcrumbs
              breadcrumbs={breadcrumbs}
              onNavigate={handleBreadcrumbNavigate}
            />
          </div>
          {/* Sub-Project Header */}
          <div className="flex items-start justify-between">
            <div>
              {subProjectData.description && (
                <p className="text-gray-600 mb-2">{subProjectData.description}</p>
              )}
              <p className="text-gray-600">
                {subProjects.length} folder{subProjects.length !== 1 ? 's' : ''}, {videos.length} video{videos.length !== 1 ? 's' : ''}
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button
                onClick={() => setShowCreateDialog(true)}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                New Folder
              </Button>

              <UploadVideo projectId={projectId} projectFolderId={subProjectId} />
            </div>
          </div>

          {/* Folders Section */}
          {subProjects.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Folders</h2>
              <div className="space-y-4">
                {subProjects.map((subProject) => (
                  <div
                    key={subProject._id}
                    className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                      void navigate({ to: '/folder/$folderId', params: { folderId: subProject._id } });
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        {/* Thumbnail placeholder */}
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                          <FolderPlus className="h-8 w-8 text-gray-400" />
                        </div>

                        {/* Content */}
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-1">
                            {subProject.name}
                          </h3>
                          {subProject.description && (
                            <p className="text-gray-600 text-sm mb-2">
                              {subProject.description}
                            </p>
                          )}
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <VideoIcon className="h-4 w-4" />
                              {subProject.videoCount || 0} videos
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {new Date(subProject.createdAt).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                              })}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Action */}
                      <div className="flex items-center gap-2 text-blue-600 hover:text-blue-700">
                        <span className="text-sm font-medium">View Project</span>
                        <ArrowRight className="h-4 w-4" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Videos Section */}
          {videos.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Videos</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {videos.map((video) => (
                  <DraggableVideoItem
                    key={video._id}
                    video={video}
                    onClick={() => {
                      void navigate({ to: '/review-video/$videoId', params: { videoId: video._id } });
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Empty state */}
          {subProjects.length === 0 && videos.length === 0 && (
            <div className="text-center py-20">
              <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
                <FolderPlus className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">No content yet</h3>
              <p className="text-gray-500 mb-6">
                Create nested folders to organize your videos or upload videos directly to this folder.
              </p>
              <div className="flex items-center justify-center gap-4">
                <Button
                  onClick={() => setShowCreateDialog(true)}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  New Folder
                </Button>
                <UploadVideo projectId={projectId} projectFolderId={subProjectId} />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create sub-project dialog */}
      <CreateSubProjectDialog
        isOpen={showCreateDialog}
        setIsOpen={setShowCreateDialog}
        projectId={projectId}
        parentSubProjectId={subProjectId}
      />
    </>
  );
};
