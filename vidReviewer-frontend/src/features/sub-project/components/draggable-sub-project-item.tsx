import { Folder } from 'lucide-react';
import { Card, CardContent, CardHeader } from '~components/ui/card';
import { cn } from '~lib/utils';

import { type SubProject } from '~features/sub-project/types/sub-project';
import { SubProjectContextMenu } from './sub-project-context-menu';
import { ShareButton } from '~features/sharing/components/share-button';

interface DraggableSubProjectItemProps {
  subProject: SubProject;
  projectId: string;
  isDragging?: boolean;
  isDragOver?: boolean;
  onDragStart?: (item: { id: string; type: 'subproject' }) => void;
  onDragEnd?: () => void;
  onDragOver?: (targetId: string) => void;
  onDragLeave?: () => void;
  onDrop?: (targetId: string) => void;
  onClick?: () => void;
}

export const DraggableSubProjectItem = ({
  subProject,
  projectId,
  isDragging = false,
  isDragOver = false,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDragLeave,
  onDrop,
  onClick,
}: DraggableSubProjectItemProps) => {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.effectAllowed = 'move';
    onDragStart?.({ id: subProject._id, type: 'subproject' });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    onDragOver?.(subProject._id);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Only trigger if leaving the card itself, not child elements
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      onDragLeave?.();
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    onDrop?.(subProject._id);
  };

  const videoCount = subProject.videos?.length || 0;
  const subSubProjectCount = subProject.children?.length || 0;

  return (
    <Card
      className={cn(
        'group relative cursor-pointer transition-all duration-200 hover:shadow-lg hover:shadow-blue-100',
        isDragging && 'opacity-50 scale-95',
        isDragOver && 'ring-2 ring-blue-500 ring-offset-2',
        'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-blue-200 min-h-[140px]'
      )}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={onDragEnd}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md">
              <Folder className="h-6 w-6 text-white" />
            </div>
            <div className="space-y-1 flex-1">
              <h3 className="font-semibold text-gray-900 line-clamp-1 text-base">
                {subProject.name}
              </h3>
              <p className="text-sm text-gray-500 line-clamp-2 min-h-[2.5rem] flex items-start">
                {subProject.description || 'No description'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity" onClick={(e) => e.stopPropagation()}>
            <ShareButton
              resourceType="folder"
              resourceId={subProject._id}
              resourceTitle={subProject.name}
              variant="ghost"
              size="sm"
              showText={false}
              className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
            />
            <SubProjectContextMenu subProject={subProject} projectId={projectId} />
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center gap-4">
            {subSubProjectCount > 0 && (
              <span>{subSubProjectCount} sub-project{subSubProjectCount !== 1 ? 's' : ''}</span>
            )}
            <span>{videoCount} video{videoCount !== 1 ? 's' : ''}</span>
          </div>
          <span className="text-xs">
            {new Date(subProject.createdAt).toLocaleDateString()}
          </span>
        </div>
      </CardContent>
    </Card>
  );
};
