import { Loader2, Trash2 } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~components/ui/alert-dialog';

import { useDeleteSubProjectMutation } from '~features/sub-project/api/delete-sub-project';
import { type SubProject } from '~features/sub-project/types/sub-project';

interface DeleteSubProjectAlertDialogProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  subProject: SubProject;
  projectId: string;
}

export const DeleteSubProjectAlertDialog = ({
  isOpen,
  setIsOpen,
  subProject,
  projectId,
}: DeleteSubProjectAlertDialogProps) => {
  const [deleteSubProject, { isLoading }] = useDeleteSubProjectMutation();

  const handleDelete = async () => {
    try {
      await deleteSubProject({
        projectId,
        subProjectId: subProject._id,
      }).unwrap();
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to delete folder:', error);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-600" />
            Delete Folder
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the folder "{subProject.name}"?
            This action will also delete all videos and nested folders within it.
            This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Delete Folder
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
