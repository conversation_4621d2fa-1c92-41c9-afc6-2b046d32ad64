import { useNavigate, useParams } from '@tanstack/react-router';
import { Loader2 } from 'lucide-react';
import Header from '~components/custom/header';
import { useAppSelector } from '~stores/hooks';

import { useGetFolderQuery } from '~features/folder/api/get-folder';
import { useGetFoldersQuery } from '~features/folder/api/get-folders';
import { useGetProjectQuery } from '~features/project/api/get-project';
import { ProjectBreadcrumbs } from '~features/project/components/project-breadcrumbs';
import { buildProjectBreadcrumbs } from '~features/project/utils/project-utils';
import { useGetSubProjectContentsQuery } from '~features/sub-project/api/get-sub-project-contents';
import { selectUser } from '~features/user/store/user-slice';

import { ProjectSubProjectView } from './project-sub-project-view';

export const ProjectDetailsWithSubProjects = () => {
  const navigate = useNavigate();
  const { folderId } = useParams({ from: '/_auth/folder/$folderId' });
  const user = useAppSelector(selectUser);

  const { data: project, isFetching: isFetchingProject } = useGetProjectQuery({
    projectId: folderId,
  });

  const { data: contents, isFetching: isFetchingContents } =
    useGetSubProjectContentsQuery({
      projectId: folderId,
    });

  // Get parent folder if project is in a folder
  const { data: parentFolder } = useGetFolderQuery(
    { folderId: project?.folderId || '' },
    { skip: !project?.folderId },
  );

  // Get all folders for breadcrumb building
  const { data: allFoldersData } = useGetFoldersQuery(
    { userId: user.user?._id ?? '' },
    { skip: !user.user?._id },
  );

  const isLoading = isFetchingProject || isFetchingContents;

  if (isLoading) {
    return (
      <>
        <Header title='Loading...' />
        <div className='flex h-[calc(100vh-64px)] w-full items-center justify-center'>
          <Loader2 size={48} className='animate-spin' />
        </div>
      </>
    );
  }

  if (!project) {
    return (
      <>
        <Header title='Project Not Found' />
        <div className='flex h-[calc(100vh-64px)] w-full items-center justify-center'>
          <div className='text-center'>
            <h2 className='mb-2 text-xl font-semibold text-gray-900'>
              Project Not Found
            </h2>
            <p className='text-gray-600'>
              The project you&apos;re looking for doesn&apos;t exist or has been
              deleted.
            </p>
          </div>
        </div>
      </>
    );
  }

  // Build breadcrumbs
  const breadcrumbs = buildProjectBreadcrumbs(
    project,
    parentFolder ?? null,
    allFoldersData?.folders ?? [],
  );

  // Handle breadcrumb navigation
  const handleBreadcrumbNavigate = (id: string | null, type: string) => {
    if (type === 'home' || id === null) {
      void navigate({ to: '/folders' });
    } else if (type === 'folder') {
      void navigate({ to: '/folder/$folderId', params: { folderId: id } });
    } else if (type === 'project') {
      void navigate({ to: '/folder/$folderId', params: { folderId: id } });
    }
  };

  return (
    <>
      <Header
        title={project.title}
        projectId={folderId}
        showSubProjectActions={false}
        showUploadButton={false}
      />
      <div className='p-6 pt-8 sm:p-12'>
        {/* Breadcrumbs */}
        <div className='mb-6'>
          <ProjectBreadcrumbs
            breadcrumbs={breadcrumbs}
            onNavigate={handleBreadcrumbNavigate}
          />
        </div>

        <ProjectSubProjectView
          subProjects={contents?.subProjects ?? []}
          videos={contents?.videos ?? []}
          projectId={folderId}
          projectTitle={project.title}
        />
      </div>
    </>
  );
};
