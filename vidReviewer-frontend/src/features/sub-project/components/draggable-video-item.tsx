import { Play, Clock } from 'lucide-react';
import { Card, CardContent, CardHeader } from '~components/ui/card';
import { cn } from '~lib/utils';

import { type Video } from '~features/video/types/video';
import { getEffectiveThumbnailUrl } from '~features/video/utils/thumbnail-utils';
import { ShareButton } from '~features/sharing/components/share-button';

interface DraggableVideoItemProps {
  video: Video;
  isDragging?: boolean;
  onDragStart?: (item: { id: string; type: 'video' }) => void;
  onDragEnd?: () => void;
  onClick?: () => void;
}

export const DraggableVideoItem = ({
  video,
  isDragging = false,
  onDragStart,
  onDragEnd,
  onClick,
}: DraggableVideoItemProps) => {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.effectAllowed = 'move';
    onDragStart?.({ id: video._id, type: 'video' });
  };

  const thumbnailUrl = getEffectiveThumbnailUrl(video);
  const isArchived = video.storageClass === 'archived';

  return (
    <Card
      className={cn(
        'group relative cursor-pointer transition-all duration-200 hover:shadow-md',
        isDragging && 'opacity-50 scale-95',
        isArchived ? 'bg-gray-100 border-gray-300' : 'bg-white'
      )}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={onDragEnd}
      onClick={onClick}
    >
      <CardHeader className="p-0">
        <div className="relative aspect-video w-full overflow-hidden rounded-t-lg">
          {thumbnailUrl ? (
            <img
              src={thumbnailUrl}
              alt={video.title}
              className={cn(
                'h-full w-full object-cover',
                isArchived && 'grayscale'
              )}
            />
          ) : (
            <div className={cn(
              'flex h-full w-full items-center justify-center',
              isArchived ? 'bg-gray-200' : 'bg-gray-100'
            )}>
              <Play className={cn(
                'h-8 w-8',
                isArchived ? 'text-gray-400' : 'text-gray-500'
              )} />
            </div>
          )}
          
          {/* Play overlay */}
          <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex items-center justify-center w-12 h-12 bg-white/90 rounded-full">
              <Play className="h-5 w-5 text-gray-900 ml-0.5" />
            </div>
          </div>

          {/* Duration badge */}
          {video.duration && video.duration > 0 && (
            <div className="absolute bottom-2 right-2 flex items-center gap-1 bg-black/70 text-white text-xs px-2 py-1 rounded">
              <Clock className="h-3 w-3" />
              {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}
            </div>
          )}

          {/* Share button */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity" onClick={(e) => e.stopPropagation()}>
            <ShareButton
              resourceType="video"
              resourceId={video._id}
              resourceTitle={video.title}
              variant="ghost"
              size="sm"
              showText={false}
              className="h-6 w-6 p-0 text-white hover:text-gray-200 hover:bg-black/50"
            />
          </div>


        </div>
      </CardHeader>
      
      <CardContent className="p-3">
        <div className="space-y-2">
          <h3 className={cn(
            'font-medium line-clamp-2 text-sm',
            isArchived ? 'text-gray-600' : 'text-gray-900'
          )}>
            {video.title}
          </h3>
          
          {video.description && (
            <p className={cn(
              'text-xs line-clamp-2',
              isArchived ? 'text-gray-500' : 'text-gray-600'
            )}>
              {video.description}
            </p>
          )}
          
          <div className="flex items-center justify-end text-xs text-gray-500">
            <span>{new Date(video.uploadedOn).toLocaleDateString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
