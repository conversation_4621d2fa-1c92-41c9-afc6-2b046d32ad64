import { Edit, FolderPlus, MoreVertical, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { Button } from '~components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~components/ui/dropdown-menu';

import { type SubProject } from '~features/sub-project/types/sub-project';

import { CreateSubProjectDialog } from './create-sub-project-dialog';
import { EditSubProjectDialog } from './edit-sub-project-dialog';
import { DeleteSubProjectAlertDialog } from './delete-sub-project-alert-dialog';

interface SubProjectContextMenuProps {
  subProject: SubProject;
  projectId: string;
}

export const SubProjectContextMenu = ({ subProject, projectId }: SubProjectContextMenuProps) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-48">
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={() => setShowCreateDialog(true)}>
              <FolderPlus className="mr-2 h-4 w-4" />
              Create Sub-Folder
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setShowEditDialog(true)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Folder
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setShowDeleteDialog(true)}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete Folder
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <CreateSubProjectDialog
        isOpen={showCreateDialog}
        setIsOpen={setShowCreateDialog}
        projectId={projectId}
        parentSubProjectId={subProject._id}
      />

      <EditSubProjectDialog
        isOpen={showEditDialog}
        setIsOpen={setShowEditDialog}
        subProject={subProject}
        projectId={projectId}
      />

      <DeleteSubProjectAlertDialog
        isOpen={showDeleteDialog}
        setIsOpen={setShowDeleteDialog}
        subProject={subProject}
        projectId={projectId}
      />
    </>
  );
};
