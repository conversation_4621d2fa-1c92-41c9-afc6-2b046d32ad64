import { baseAPI, TAGS } from '~stores/base-api';

import { type SubProject, type UpdateSubProjectParams } from '~features/sub-project/types/sub-project';

export const updateSubProjectAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    updateSubProject: build.mutation<SubProject, UpdateSubProjectParams>({
      invalidatesTags: [TAGS.FOLDERS],
      query: ({ projectId, subProjectId, ...body }) => ({
        url: `/projects/${projectId}/folders/${subProjectId}`,
        method: 'PUT',
        body,
      }),
    }),
  }),
});

export const { useUpdateSubProjectMutation } = updateSubProjectAPI;
