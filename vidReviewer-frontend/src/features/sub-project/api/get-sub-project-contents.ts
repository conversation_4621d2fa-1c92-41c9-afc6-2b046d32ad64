import { baseAPI, TAGS } from '~stores/base-api';

import { type GetSubProjectContentsParams, type GetSubProjectContentsResponse } from '~features/sub-project/types/sub-project';

export const getSubProjectContentsAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getSubProjectContents: build.query<GetSubProjectContentsResponse, GetSubProjectContentsParams>({
      providesTags: [TAGS.FOLDERS, TAGS.PROJECTS],
      query: ({ projectId, subProjectId }) => ({
        url: subProjectId 
          ? `/projects/${projectId}/folders/${subProjectId}/contents`
          : `/projects/${projectId}/folders/root/contents`,
      }),
    }),
  }),
});

export const { useGetSubProjectContentsQuery } = getSubProjectContentsAPI;
