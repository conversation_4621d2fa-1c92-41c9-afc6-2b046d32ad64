import { baseAPI, TAGS } from '~stores/base-api';

import { type SubProject, type CreateSubProjectParams } from '~features/sub-project/types/sub-project';

export const createSubProjectAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    createSubProject: build.mutation<SubProject, CreateSubProjectParams>({
      invalidatesTags: [TAGS.FOLDERS, TAGS.PROJECTS],
      query: ({ projectId, ...body }) => ({
        url: `/projects/${projectId}/folders`,
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const { useCreateSubProjectMutation } = createSubProjectAPI;
