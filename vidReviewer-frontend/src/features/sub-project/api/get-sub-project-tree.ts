import { baseAPI, TAGS } from '~stores/base-api';

import { type GetSubProjectTreeParams, type GetSubProjectTreeResponse } from '~features/sub-project/types/sub-project';

export const getSubProjectTreeAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getSubProjectTree: build.query<GetSubProjectTreeResponse, GetSubProjectTreeParams>({
      providesTags: [TAGS.FOLDERS, TAGS.PROJECTS],
      query: ({ projectId, includeVideos = false }) => ({
        url: `/projects/${projectId}/folders/tree`,
        params: { includeVideos: includeVideos.toString() },
      }),
    }),
  }),
});

export const { useGetSubProjectTreeQuery } = getSubProjectTreeAPI;
