import { baseAPI, TAGS } from '~stores/base-api';

import { type DeleteSubProjectParams, type DeleteSubProjectResponse } from '~features/sub-project/types/sub-project';

export const deleteSubProjectAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    deleteSubProject: build.mutation<DeleteSubProjectResponse, DeleteSubProjectParams>({
      invalidatesTags: [TAGS.FOLDERS, TAGS.PROJECTS],
      query: ({ projectId, subProjectId }) => ({
        url: `/projects/${projectId}/folders/${subProjectId}`,
        method: 'DELETE',
      }),
    }),
  }),
});

export const { useDeleteSubProjectMutation } = deleteSubProjectAPI;
