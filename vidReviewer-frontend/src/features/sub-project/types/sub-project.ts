import { type Video } from '~features/video/types/video';

export interface SubProject {
  _id: string;
  name: string;
  description?: string;
  createdBy: string;
  contextType: 'project';
  contextId: string; // Project ID
  parentFolder?: string | null;
  path: string;
  level: number;
  createdAt: string;
  updatedAt: string;
  children?: SubProject[];
  videos?: Video[];
  videoCount?: number; // Number of videos in this sub-project
}

export interface SubProjectTree extends SubProject {
  children: SubProjectTree[];
}

// API Request/Response interfaces
export interface GetSubProjectsParams {
  projectId: string;
}

export interface GetSubProjectsResponse {
  subProjects: SubProject[];
}

export interface GetSubProjectTreeParams {
  projectId: string;
  includeVideos?: boolean;
}

export interface GetSubProjectTreeResponse {
  folderTree: SubProjectTree[];
  rootVideos?: Video[];
}

export interface GetSubProjectParams {
  projectId: string;
  subProjectId: string;
}

export interface GetSubProjectContentsParams {
  projectId: string;
  subProjectId?: string;
}

export interface GetSubProjectContentsResponse {
  subProjects: SubProject[];
  videos: Video[];
}

export interface CreateSubProjectParams {
  projectId: string;
  name: string;
  description?: string;
  parentFolder?: string;
}

export interface UpdateSubProjectParams {
  projectId: string;
  subProjectId: string;
  name: string;
  description?: string;
}

export interface MoveSubProjectParams {
  projectId: string;
  subProjectId: string;
  parentFolder?: string | null;
}

export interface DeleteSubProjectParams {
  projectId: string;
  subProjectId: string;
}

export interface DeleteSubProjectResponse {
  message: string;
  deletedSubProject: SubProject;
  deletedVideosCount: number;
  deletedSubSubProjectsCount: number;
}

export interface SubProjectLimitError {
  error: string;
  code: 'SUB_PROJECT_LIMIT_EXCEEDED';
}

// Constants
// Removed MAX_SUB_PROJECTS_PER_PROJECT limit
