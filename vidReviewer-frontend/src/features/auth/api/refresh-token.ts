import { baseAPI } from '~stores/base-api';

interface RefreshTokenParams {
  locationId: string;
}

export const refreshTokenAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    refreshToken: build.mutation<unknown, RefreshTokenParams>({
      // invalidatesTags: (_result, _error, { videoId }) => [
      //   { type: TAGS.COMMENTS, id: videoId },
      // ],
      query: ({ locationId }) => ({
        url: `/auth/refresh/${locationId}`,
        method: 'POST',
      }),
    }),
  }),
});

export const { useRefreshTokenMutation } = refreshTokenAPI;
