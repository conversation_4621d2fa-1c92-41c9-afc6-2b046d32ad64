import { baseAPI } from '~stores/base-api';

interface RefreshAgencyTokenParams {
  companyId: string;
}

export const refreshAgencyTokenAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    refreshAgencyToken: build.mutation<unknown, RefreshAgencyTokenParams>({
      query: ({ companyId }) => ({
        url: `/auth/refresh/agency/${companyId}`,
        method: 'POST',
      }),
    }),
  }),
});

export const { useRefreshAgencyTokenMutation } = refreshAgencyTokenAPI;
