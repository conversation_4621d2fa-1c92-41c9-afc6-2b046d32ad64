import { baseAPI } from '~stores/base-api';

interface DecryptSSOParams {
  encryptedData: string;
}

export interface DecryptSSOResponse {
  activeLocation: string;
  userId: string;
  companyId: string;
  role: string;
  type: string;
  userName: string;
  email: string;
}

export const decryptSSOAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    decryptSSO: build.mutation<DecryptSSOResponse, DecryptSSOParams>({
      // invalidatesTags: (_result, _error, { videoId }) => [
      //   { type: TAGS.COMMENTS, id: videoId },
      // ],
      query: ({ encryptedData }) => ({
        url: '/decrypt-sso',
        method: 'POST',
        body: { encryptedData },
      }),
    }),
  }),
});

export const { useDecryptSSOMutation } = decryptSSOAPI;
