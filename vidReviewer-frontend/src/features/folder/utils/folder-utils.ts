import { type Folder, type FolderTree } from '~features/folder/types/folder';

export interface BreadcrumbItem {
  id: string | null;
  name: string;
  path?: string;
}

/**
 * Build breadcrumb items from a folder path
 */
export const buildBreadcrumbs = (
  currentFolder: Folder | null,
  allFolders: Folder[]
): BreadcrumbItem[] => {
  if (!currentFolder) {
    return [];
  }

  const breadcrumbs: BreadcrumbItem[] = [];
  const pathParts = currentFolder.path.split('/').filter(Boolean);
  
  // Build breadcrumbs by traversing the path
  let currentPath = '';
  for (const part of pathParts) {
    currentPath += `/${part}`;
    const folder = allFolders.find(f => f.path === currentPath);
    if (folder) {
      breadcrumbs.push({
        id: folder._id,
        name: folder.name,
        path: folder.path,
      });
    }
  }

  return breadcrumbs;
};

/**
 * Find a folder by ID in a folder tree
 */
export const findFolderInTree = (
  tree: FolderTree[],
  folderId: string
): FolderTree | null => {
  for (const folder of tree) {
    if (folder._id === folderId) {
      return folder;
    }
    if (folder.children) {
      const found = findFolderInTree(folder.children, folderId);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

/**
 * Get all parent folder IDs for a given folder
 */
export const getParentFolderIds = (
  folder: Folder,
  allFolders: Folder[]
): string[] => {
  const parentIds: string[] = [];
  const pathParts = folder.path.split('/').filter(Boolean);
  
  let currentPath = '';
  for (let i = 0; i < pathParts.length - 1; i++) {
    currentPath += `/${pathParts[i]}`;
    const parentFolder = allFolders.find(f => f.path === currentPath);
    if (parentFolder) {
      parentIds.push(parentFolder._id);
    }
  }
  
  return parentIds;
};

/**
 * Check if a folder can be moved to a target parent (prevents circular references)
 */
export const canMoveFolder = (
  sourceFolder: Folder,
  targetParentId: string | null,
  allFolders: Folder[]
): boolean => {
  if (!targetParentId) {
    return true; // Moving to root is always allowed
  }

  const targetParent = allFolders.find(f => f._id === targetParentId);
  if (!targetParent) {
    return false;
  }

  // Check if target parent is a descendant of source folder
  return !targetParent.path.startsWith(sourceFolder.path + '/') && 
         targetParent._id !== sourceFolder._id;
};

/**
 * Flatten a folder tree into a flat array
 */
export const flattenFolderTree = (tree: FolderTree[]): Folder[] => {
  const result: Folder[] = [];
  
  const traverse = (folders: FolderTree[]) => {
    for (const folder of folders) {
      result.push(folder);
      if (folder.children) {
        traverse(folder.children);
      }
    }
  };
  
  traverse(tree);
  return result;
};
