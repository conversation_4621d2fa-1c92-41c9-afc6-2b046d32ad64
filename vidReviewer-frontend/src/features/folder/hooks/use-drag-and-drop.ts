import { useState } from 'react';
import { toast } from 'sonner';

import { useMoveFolderMutation } from '~features/folder/api/move-folder';
import { useMoveProjectMutation } from '~features/project/api/move-project';

export interface DragItem {
  id: string;
  type: 'folder' | 'project';
  name: string;
}

export const useDragAndDrop = () => {
  const [draggedItem, setDraggedItem] = useState<DragItem | null>(null);
  const [dragOverTarget, setDragOverTarget] = useState<string | null>(null);

  const [moveFolder] = useMoveFolderMutation();
  const [moveProject] = useMoveProjectMutation();

  const handleDragStart = (item: DragItem) => {
    setDraggedItem(item);
  };

  const handleDragEnd = () => {
    setDraggedItem(null);
    setDragOverTarget(null);
  };

  const handleDragOver = (e: React.DragEvent, targetId: string | null) => {
    e.preventDefault();
    setDragOverTarget(targetId);
  };

  const handleDragLeave = () => {
    setDragOverTarget(null);
  };

  const handleDrop = async (e: React.DragEvent, targetFolderId: string | null) => {
    e.preventDefault();
    
    if (!draggedItem) return;

    // Prevent dropping on itself
    if (draggedItem.id === targetFolderId) {
      setDraggedItem(null);
      setDragOverTarget(null);
      return;
    }

    try {
      if (draggedItem.type === 'folder') {
        await moveFolder({
          folderId: draggedItem.id,
          parentFolder: targetFolderId,
        }).unwrap();
        toast.success(`Folder "${draggedItem.name}" moved successfully`);
      } else if (draggedItem.type === 'project') {
        await moveProject({
          projectId: draggedItem.id,
          folderId: targetFolderId,
        }).unwrap();
        toast.success(`Project "${draggedItem.name}" moved successfully`);
      }
    } catch (error) {
      console.error('Failed to move item:', error);
      toast.error(`Failed to move ${draggedItem.type}`);
    }

    setDraggedItem(null);
    setDragOverTarget(null);
  };

  return {
    draggedItem,
    dragOverTarget,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop,
  };
};
