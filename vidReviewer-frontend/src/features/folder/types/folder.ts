export interface Folder {
  _id: string;
  name: string;
  description?: string;
  createdBy: string;
  contextType?: 'user' | 'project';
  contextId?: string | null;
  parentFolder?: string | null;
  path: string;
  level: number;
  createdAt: string;
  updatedAt: string;
  children?: Folder[];
  projects?: Array<{
    _id: string;
    title: string;
    description: string;
    createdBy: string;
    name: string;
    email: string;
    videoCount: number;
    thumbnails: Array<{ videoThumbnail?: string }>;
    folderId?: string | null;
    createdAt: string;
  }>;
}

export interface FolderTree extends Folder {
  children: FolderTree[];
}

// API Request/Response interfaces
export interface GetFoldersParams {
  userId: string;
}

export interface GetFoldersResponse {
  folders: Folder[];
}

export interface GetFolderTreeParams {
  userId: string;
  includeProjects?: boolean;
}

export interface GetFolderTreeResponse {
  folderTree: FolderTree[];
  rootProjects?: Array<{
    _id: string;
    title: string;
    description: string;
    createdBy: string;
    name: string;
    email: string;
    videoCount: number;
    thumbnails: Array<{ videoThumbnail?: string }>;
    folderId?: string | null;
    createdAt: string;
  }>;
}

export interface GetFolderParams {
  folderId: string;
}

export interface GetFolderContentsParams {
  folderId?: string;
  userId: string;
}

export interface GetFolderContentsResponse {
  subfolders: Folder[];
  projects: Array<{
    _id: string;
    title: string;
    description: string;
    createdBy: string;
    name: string;
    email: string;
    videoCount: number;
    thumbnails: Array<{ videoThumbnail?: string }>;
    folderId?: string | null;
    createdAt: string;
  }>;
}

export interface CreateFolderParams {
  name: string;
  description?: string;
  createdBy: string;
  parentFolder?: string;
}

export interface UpdateFolderParams {
  folderId: string;
  name: string;
  description?: string;
}

export interface MoveFolderParams {
  folderId: string;
  parentFolder?: string | null;
}

export interface DeleteFolderParams {
  folderId: string;
}

export interface DeleteFolderResponse {
  message: string;
  deletedFolder: Folder;
  deletedProjectsCount: number;
  deletedSubfoldersCount: number;
}
