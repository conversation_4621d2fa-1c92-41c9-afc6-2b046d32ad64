import { baseAPI, TAGS } from '~stores/base-api';

import { type DeleteFolderParams, type DeleteFolderResponse } from '~features/folder/types/folder';

export const deleteFolderAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    deleteFolder: build.mutation<DeleteFolderResponse, DeleteFolderParams>({
      invalidatesTags: [TAGS.FOLDERS, TAGS.PROJECTS],
      query: ({ folderId }) => ({
        url: `/folders/${folderId}`,
        method: 'DELETE',
      }),
    }),
  }),
});

export const { useDeleteFolderMutation } = deleteFolderAPI;
