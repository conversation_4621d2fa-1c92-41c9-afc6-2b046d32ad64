import { baseAPI, TAGS } from '~stores/base-api';

import { type GetFoldersParams, type GetFoldersResponse } from '~features/folder/types/folder';

export const getFoldersAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getFolders: build.query<GetFoldersResponse, GetFoldersParams>({
      providesTags: [TAGS.FOLDERS],
      query: ({ userId }) => ({
        url: `/folders/user/${userId}`,
      }),
    }),
  }),
});

export const { useGetFoldersQuery } = getFoldersAPI;
