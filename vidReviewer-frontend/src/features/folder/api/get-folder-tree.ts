import { baseAPI, TAGS } from '~stores/base-api';

import { type GetFolderTreeParams, type GetFolderTreeResponse } from '~features/folder/types/folder';

export const getFolderTreeAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getFolderTree: build.query<GetFolderTreeResponse, GetFolderTreeParams>({
      providesTags: [TAGS.FOLDERS, TAGS.PROJECTS],
      query: ({ userId, includeProjects = false }) => ({
        url: `/folders/tree/${userId}`,
        params: { includeProjects: includeProjects.toString() },
      }),
    }),
  }),
});

export const { useGetFolderTreeQuery } = getFolderTreeAPI;
