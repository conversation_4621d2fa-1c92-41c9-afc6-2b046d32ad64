import { baseAPI, TAGS } from '~stores/base-api';

import { type Folder, type UpdateFolderParams } from '~features/folder/types/folder';

export const updateFolderAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    updateFolder: build.mutation<Folder, UpdateFolderParams>({
      invalidatesTags: [TAGS.FOLDERS],
      query: ({ folderId, name, description }) => ({
        url: `/folders/${folderId}`,
        method: 'PUT',
        body: { name, description },
      }),
    }),
  }),
});

export const { useUpdateFolderMutation } = updateFolderAPI;
