import { baseAPI, TAGS } from '~stores/base-api';

import { type Folder, type MoveFolderParams } from '~features/folder/types/folder';

export const moveFolderAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    moveFolder: build.mutation<Folder, MoveFolderParams>({
      invalidatesTags: [TAGS.FOLDERS],
      query: ({ folderId, parentFolder }) => ({
        url: `/folders/${folderId}/move`,
        method: 'PATCH',
        body: { parentFolder },
      }),
    }),
  }),
});

export const { useMoveFolderMutation } = moveFolderAPI;
