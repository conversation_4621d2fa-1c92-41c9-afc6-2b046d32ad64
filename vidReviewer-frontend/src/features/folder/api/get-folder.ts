import { baseAPI, TAGS } from '~stores/base-api';

import { type Folder, type GetFolderParams } from '~features/folder/types/folder';

export const getFolderAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getFolder: build.query<Folder, GetFolderParams>({
      providesTags: (_result, _error, { folderId }) => [
        { type: TAGS.FOLDERS, id: folderId },
      ],
      query: ({ folderId }) => ({
        url: `/folders/${folderId}`,
      }),
    }),
  }),
});

export const { useGetFolderQuery } = getFolderAPI;
