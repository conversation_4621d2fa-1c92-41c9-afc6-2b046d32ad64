import { baseAPI, TAGS } from '~stores/base-api';

import { type Folder, type CreateFolderParams } from '~features/folder/types/folder';

export const createFolderAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    createFolder: build.mutation<Folder, CreateFolderParams>({
      invalidatesTags: [TAGS.FOLDERS],
      query: (body) => ({
        url: '/folders',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const { useCreateFolderMutation } = createFolderAPI;
