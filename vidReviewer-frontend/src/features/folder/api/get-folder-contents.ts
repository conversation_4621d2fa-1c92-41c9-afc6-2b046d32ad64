import { baseAPI, TAGS } from '~stores/base-api';

import { type GetFolderContentsParams, type GetFolderContentsResponse } from '~features/folder/types/folder';

export const getFolderContentsAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getFolderContents: build.query<GetFolderContentsResponse, GetFolderContentsParams>({
      providesTags: [TAGS.FOLDERS, TAGS.PROJECTS],
      query: ({ folderId, userId }) => ({
        url: folderId ? `/folders/${folderId}/contents` : '/folders/root/contents',
        params: { userId },
      }),
    }),
  }),
});

export const { useGetFolderContentsQuery } = getFolderContentsAPI;
