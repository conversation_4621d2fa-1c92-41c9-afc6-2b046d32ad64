import { Edit, FolderPlus, MoreVertical, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { Button } from '~components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~components/ui/dropdown-menu';

import { type Folder } from '~features/folder/types/folder';

import { CreateFolderDialog } from './create-folder-dialog';
import { EditFolderDialog } from './edit-folder-dialog';
import { DeleteFolderAlertDialog } from './delete-folder-alert-dialog';

interface FolderContextMenuProps {
  folder: Folder;
  createdBy: string;
}

export const FolderContextMenu = ({ folder, createdBy }: FolderContextMenuProps) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-48">
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={() => setShowCreateDialog(true)}>
              <FolderPlus className="mr-2 h-4 w-4" />
              Create Subfolder
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setShowEditDialog(true)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Folder
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={() => setShowDeleteDialog(true)}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete Folder
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <CreateFolderDialog
        isOpen={showCreateDialog}
        setIsOpen={setShowCreateDialog}
        parentFolderId={folder._id}
        createdBy={createdBy}
      />

      <EditFolderDialog
        isOpen={showEditDialog}
        setIsOpen={setShowEditDialog}
        folder={folder}
      />

      <DeleteFolderAlertDialog
        isOpen={showDeleteDialog}
        setIsOpen={setShowDeleteDialog}
        folder={folder}
      />
    </>
  );
};
