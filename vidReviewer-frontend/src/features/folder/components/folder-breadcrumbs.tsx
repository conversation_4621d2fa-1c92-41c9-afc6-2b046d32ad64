import { ChevronRight, Home } from 'lucide-react';
import { Button } from '~components/ui/button';
import { cn } from '~lib/utils';

interface BreadcrumbItem {
  id: string | null;
  name: string;
  path?: string;
}

interface FolderBreadcrumbsProps {
  breadcrumbs: BreadcrumbItem[];
  onNavigate: (folderId: string | null) => void;
  className?: string;
}

export const FolderBreadcrumbs = ({ 
  breadcrumbs, 
  onNavigate, 
  className 
}: FolderBreadcrumbsProps) => {
  return (
    <nav className={cn('flex items-center space-x-1 text-base text-gray-600', className)}>
      <Button
        variant="ghost"
        size="sm"
        className="h-10 px-3 text-base text-gray-600 hover:text-gray-900"
        onClick={() => onNavigate(null)}
      >
        <Home className="h-5 w-5 mr-2" />
        Home
      </Button>
      
      {breadcrumbs.map((item, index) => (
        <div key={item.id || 'root'} className="flex items-center">
          <ChevronRight className="h-5 w-5 text-gray-400" />
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              'h-10 px-3 text-base text-gray-600 hover:text-gray-900',
              index === breadcrumbs.length - 1 && 'text-gray-900 font-medium'
            )}
            onClick={() => onNavigate(item.id)}
            disabled={index === breadcrumbs.length - 1}
          >
            {item.name}
          </Button>
        </div>
      ))}
    </nav>
  );
};
