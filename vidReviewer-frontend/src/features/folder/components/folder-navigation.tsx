import { useEffect, useState } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { Loader2 } from 'lucide-react';
import { useAppSelector } from '~stores/hooks';

import { selectUser } from '~features/user/store/user-slice';
import { useGetFolderContentsQuery } from '~features/folder/api/get-folder-contents';
import { useGetFoldersQuery } from '~features/folder/api/get-folders';

import { FolderBreadcrumbs } from './folder-breadcrumbs';
import { FolderView } from './folder-view';
import { buildBreadcrumbs } from '../utils/folder-utils';

interface FolderNavigationProps {
  initialFolderId?: string | null;
  onFolderChange?: (folderId: string | null) => void;
}

export const FolderNavigation = ({
  initialFolderId = null,
  onFolderChange
}: FolderNavigationProps) => {
  const user = useAppSelector(selectUser);
  const navigate = useNavigate();
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(initialFolderId);

  // Get all folders for breadcrumb building
  const { data: allFoldersData } = useGetFoldersQuery(
    { userId: user.user?._id ?? '' },
    { skip: !user.user?._id }
  );

  // Get current folder contents
  const { data: folderContents, isFetching } = useGetFolderContentsQuery(
    { 
      folderId: currentFolderId || undefined, 
      userId: user.user?._id ?? '' 
    },
    { skip: !user.user?._id }
  );

  useEffect(() => {
    setCurrentFolderId(initialFolderId);
  }, [initialFolderId]);

  const handleFolderNavigate = (folderId: string | null) => {
    if (folderId) {
      void navigate({ to: '/folder/$folderId', params: { folderId } });
    } else {
      void navigate({ to: '/folders' });
    }
    setCurrentFolderId(folderId);
    onFolderChange?.(folderId);
  };

  // Build breadcrumbs
  const breadcrumbs = currentFolderId && allFoldersData?.folders
    ? buildBreadcrumbs(
        allFoldersData.folders.find(f => f._id === currentFolderId) || null,
        allFoldersData.folders
      )
    : [];

  if (isFetching) {
    return (
      <div className="flex h-64 w-full items-center justify-center">
        <Loader2 size={48} className="animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Breadcrumbs */}
      <FolderBreadcrumbs
        breadcrumbs={breadcrumbs}
        onNavigate={handleFolderNavigate}
      />

      {/* Folder and project view */}
      {folderContents && (
        <FolderView
          folders={folderContents.subfolders}
          projects={folderContents.projects}
          createdBy={user.user?._id ?? ''}
          isRootLevel={currentFolderId === null}
        />
      )}
    </div>
  );
};
