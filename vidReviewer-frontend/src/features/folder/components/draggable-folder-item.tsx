import { Folder } from 'lucide-react';
import { Card, CardContent, CardHeader } from '~components/ui/card';
import { cn } from '~lib/utils';

import { type Folder as FolderType } from '~features/folder/types/folder';
import { ShareButton } from '~features/sharing/components/share-button';

import { FolderContextMenu } from './folder-context-menu';

interface DraggableFolderItemProps {
  folder: FolderType;
  createdBy: string;
  onClick: () => void;
}

export const DraggableFolderItem = ({
  folder,
  createdBy,
  onClick,
}: DraggableFolderItemProps) => {
  return (
    <Card
      draggable
      className={cn(
        'min-h-[120px] cursor-pointer transition-shadow hover:shadow-md',
      )}
      onClick={onClick}
    >
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <div className='flex items-center gap-2'>
          <Folder className='size-5 text-blue-600' />
          <h4 className='truncate font-medium'>{folder.name}</h4>
        </div>
        <div
          className='flex items-center gap-1'
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <ShareButton
            resourceType='folder'
            resourceId={folder._id}
            resourceTitle={folder.name}
            variant='ghost'
            size='sm'
            showText={false}
            className='size-6 p-0 text-gray-500 hover:text-gray-700'
          />
          <FolderContextMenu folder={folder} createdBy={createdBy} />
        </div>
      </CardHeader>
      <CardContent className='flex min-h-10 items-center'>
        <p className='truncate text-sm text-gray-600'>
          {folder.description ?? 'No description'}
        </p>
      </CardContent>
    </Card>
  );
};
