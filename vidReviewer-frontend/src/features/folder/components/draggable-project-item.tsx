import { Image } from 'lucide-react';
import FoldersMenu from '~components/custom/folders-menu';
import { Avatar, AvatarFallback, AvatarImage } from '~components/ui/avatar';
import { Card, CardContent, CardHeader } from '~components/ui/card';
import { cn } from '~lib/utils';

import { splitNameInitials } from '~features/comment/utils/split-name-initials';
import { type Project } from '~features/project/types/project';
import { ShareButton } from '~features/sharing/components/share-button';

interface DraggableProjectItemProps {
  project: Project;
  onClick: () => void;
}

export const DraggableProjectItem = ({
  project,
  onClick,
}: DraggableProjectItemProps) => {
  return (
    <Card
      draggable
      className={cn('w-full cursor-pointer rounded-3xl bg-zinc-900 text-white')}
    >
      <CardHeader className='relative m-auto p-3'>
        <div onClick={onClick} className='cursor-pointer'>
          <div className='relative mb-2 h-40 w-full overflow-hidden rounded-2xl bg-zinc-800'>
            {project.thumbnails.length > 0 ? (
              <div className='grid size-full grid-cols-2 grid-rows-2 gap-0.5'>
                {project.thumbnails.slice(0, 4).map((thumbnail, index) => (
                  <div
                    key={index}
                    className='relative size-full overflow-hidden'
                  >
                    {thumbnail.videoThumbnail ? (
                      <img
                        src={thumbnail.videoThumbnail}
                        alt={`${project.title} video ${index + 1}`}
                        className='size-full object-cover'
                      />
                    ) : (
                      <div className='flex size-full items-center justify-center bg-zinc-700'>
                        <Image className='size-6 text-zinc-500' />
                      </div>
                    )}
                  </div>
                ))}
                {/* Fill remaining slots if less than 4 thumbnails */}
                {Array.from({ length: 4 - project.thumbnails.length }).map(
                  (_, index) => (
                    <div
                      key={`empty-${index}`}
                      className='flex size-full items-center justify-center bg-zinc-700'
                    >
                      <Image className='size-6 text-zinc-500' />
                    </div>
                  ),
                )}
              </div>
            ) : (
              <div className='flex size-full items-center justify-center'>
                <Image className='size-12 text-zinc-600' />
              </div>
            )}
          </div>
          <div className='space-y-2'>
            <h3 className='text-lg font-semibold'>{project.title}</h3>
            <p className='line-clamp-2 text-sm text-zinc-400'>
              {project.description}
            </p>
          </div>
        </div>
        <div
          className='absolute right-3 top-3'
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <FoldersMenu project={project} />
        </div>
      </CardHeader>
      <CardContent className='p-3 pt-0'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Avatar className='size-6'>
              <AvatarImage src='' />
              <AvatarFallback className='text-xs'>
                {splitNameInitials(project.name)}
              </AvatarFallback>
            </Avatar>
            <span className='text-xs text-zinc-400'>{project.name}</span>
          </div>
          <div className='flex items-center gap-2'>
            <span className='text-xs text-zinc-400'>
              {project.videoCount} videos
            </span>
            <div
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <ShareButton
                resourceType='project'
                resourceId={project._id}
                resourceTitle={project.title}
                variant='ghost'
                size='sm'
                showText={false}
                className='size-6 p-0 text-zinc-400 hover:bg-zinc-700 hover:text-white'
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
