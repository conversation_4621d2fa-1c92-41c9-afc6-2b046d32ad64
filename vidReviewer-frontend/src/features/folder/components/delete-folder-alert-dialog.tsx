import { Loader2 } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~components/ui/alert-dialog';

import { useDeleteFolderMutation } from '~features/folder/api/delete-folder';
import { type Folder } from '~features/folder/types/folder';

interface DeleteFolderAlertDialogProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  folder: Folder;
}

export const DeleteFolderAlertDialog = ({ 
  isOpen, 
  setIsOpen, 
  folder 
}: DeleteFolderAlertDialogProps) => {
  const [deleteFolder, { isLoading }] = useDeleteFolderMutation();

  const handleDelete = async () => {
    try {
      await deleteFolder({ folderId: folder._id }).unwrap();
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to delete folder:', error);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Folder</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the folder "{folder.name}"? 
            This action will also delete all subfolders and projects within this folder. 
            This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Delete Folder
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
