import { Home } from 'lucide-react';
import { cn } from '~lib/utils';

interface RootDropZoneProps {
  isDragOver: boolean;
  onDragOver: (e: React.DragEvent, targetId: string | null) => void;
  onDragLeave: () => void;
  onDrop: (e: React.DragEvent, targetId: string | null) => void;
}

export const RootDropZone = ({
  isDragOver,
  onDragOver,
  onDragLeave,
  onDrop,
}: RootDropZoneProps) => {
  return (
    <div
      className={cn(
        'flex items-center gap-2 p-3 mb-4 border-2 border-dashed border-gray-300 rounded-lg transition-colors',
        isDragOver && 'border-blue-500 bg-blue-50'
      )}
      onDragOver={(e) => onDragOver(e, null)}
      onDragLeave={onDragLeave}
      onDrop={(e) => onDrop(e, null)}
    >
      <Home className="h-5 w-5 text-gray-500" />
      <span className="text-sm text-gray-600">
        {isDragOver ? 'Drop here to move to home folder' : 'Home Folder'}
      </span>
    </div>
  );
};
