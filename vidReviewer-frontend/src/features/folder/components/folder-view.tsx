import { useNavigate } from '@tanstack/react-router';
import { Folder, FolderPlus, Plus } from 'lucide-react';

import { type Folder as FolderType } from '~features/folder/types/folder';
import { type Project } from '~features/project/types/project';

import { DraggableFolderItem } from './draggable-folder-item';
import { DraggableProjectItem } from './draggable-project-item';

interface FolderViewProps {
  folders: Array<FolderType>;
  projects: Array<Project>;
  createdBy: string;
  isRootLevel?: boolean;
}

export const FolderView = ({
  folders,
  projects,
  createdBy,
  isRootLevel = false,
}: FolderViewProps) => {
  const navigate = useNavigate();

  const goToProject = (projectId: string): void => {
    void navigate({ to: '/folder/$folderId', params: { folderId: projectId } });
  };

  const goToFolder = (folderId: string): void => {
    void navigate({ to: '/folder/$folderId', params: { folderId } });
  };

  return (
    <div className='space-y-6'>
      {/* Content sections with improved spacing */}
      <div className='space-y-8'>
        {/* Folders - hidden at root level */}
        {!isRootLevel && folders.length > 0 && (
          <div>
            <div className='mb-4 flex items-center justify-between'>
              <h3 className='text-lg font-semibold text-gray-900'>
                Folders ({folders.length})
              </h3>
            </div>
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {folders.map((folder) => (
                <DraggableFolderItem
                  key={folder._id}
                  folder={folder}
                  createdBy={createdBy}
                  onClick={() => {
                    goToFolder(folder._id);
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Projects */}
        {projects.length > 0 && (
          <div>
            <div className='mb-4 flex items-center justify-between'>
              <h3 className='text-lg font-semibold text-gray-900'>
                Projects ({projects.length})
              </h3>
            </div>
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {projects.map((project) => (
                <DraggableProjectItem
                  key={project._id}
                  project={project}
                  onClick={() => {
                    goToProject(project._id);
                  }}
                />
              ))}
            </div>
          </div>
        )}
      </div>
      {/* Empty state - Simplified without duplicate buttons */}
      {folders.length === 0 && projects.length === 0 && (
        <div className='py-16 text-center'>
          <div className='mx-auto max-w-md'>
            <Folder className='mx-auto mb-6 size-16 text-gray-300' />
            <h3 className='mb-3 text-xl font-semibold text-gray-900'>
              This folder is empty
            </h3>
            <p className='mb-6 text-gray-500'>
              Organize your work by creating folders and projects. Use the
              buttons above to get started.
            </p>

            {/* Visual guide */}
            <div className='grid grid-cols-2 gap-4 text-sm text-gray-400'>
              <div className='flex flex-col items-center rounded-lg border border-dashed border-gray-200 p-4'>
                <FolderPlus className='mb-2 size-8 text-gray-300' />
                <span>Create folders using the button in the top right</span>
              </div>
              <div className='flex flex-col items-center rounded-lg border border-dashed border-gray-200 p-4'>
                <Plus className='mb-2 size-8 text-gray-300' />
                <span>Add projects to start collaborating</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
