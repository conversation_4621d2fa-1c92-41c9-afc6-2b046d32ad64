import { ChevronDown, ChevronRight, Folder, FolderOpen } from 'lucide-react';
import { useState } from 'react';
import { Button } from '~components/ui/button';
import { cn } from '~lib/utils';

import { type FolderTree as FolderTreeType } from '~features/folder/types/folder';

interface FolderTreeProps {
  folders: FolderTreeType[];
  onFolderSelect?: (folderId: string | null) => void;
  selectedFolderId?: string | null;
  className?: string;
}

interface FolderNodeProps {
  folder: FolderTreeType;
  level: number;
  onFolderSelect?: (folderId: string | null) => void;
  selectedFolderId?: string | null;
}

const FolderNode = ({ folder, level, onFolderSelect, selectedFolderId }: FolderNodeProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const hasChildren = folder.children && folder.children.length > 0;
  const isSelected = selectedFolderId === folder._id;

  const handleToggle = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleSelect = () => {
    onFolderSelect?.(folder._id);
  };

  return (
    <div>
      <div
        className={cn(
          'flex items-center gap-2 py-1 px-2 hover:bg-gray-100 cursor-pointer rounded',
          isSelected && 'bg-blue-100',
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
      >
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0"
          onClick={handleToggle}
          disabled={!hasChildren}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )
          ) : (
            <div className="h-3 w-3" />
          )}
        </Button>
        
        <div className="flex items-center gap-2 flex-1" onClick={handleSelect}>
          {isExpanded ? (
            <FolderOpen className="h-4 w-4 text-blue-600" />
          ) : (
            <Folder className="h-4 w-4 text-blue-600" />
          )}
          <span className="text-sm">{folder.name}</span>
        </div>
      </div>
      
      {isExpanded && hasChildren && (
        <div>
          {folder.children.map((child) => (
            <FolderNode
              key={child._id}
              folder={child}
              level={level + 1}
              onFolderSelect={onFolderSelect}
              selectedFolderId={selectedFolderId}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const FolderTree = ({ folders, onFolderSelect, selectedFolderId, className }: FolderTreeProps) => {
  return (
    <div className={cn('w-full', className)}>
      {/* Home folder option */}
      <div
        className={cn(
          'flex items-center gap-2 py-1 px-2 hover:bg-gray-100 cursor-pointer rounded mb-2',
          selectedFolderId === null && 'bg-blue-100',
        )}
        onClick={() => onFolderSelect?.(null)}
      >
        <div className="h-4 w-4" />
        <Folder className="h-4 w-4 text-blue-600" />
        <span className="text-sm font-medium">Home</span>
      </div>
      
      {folders.map((folder) => (
        <FolderNode
          key={folder._id}
          folder={folder}
          level={0}
          onFolderSelect={onFolderSelect}
          selectedFolderId={selectedFolderId}
        />
      ))}
    </div>
  );
};
