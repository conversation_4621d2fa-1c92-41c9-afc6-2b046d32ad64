import { baseAPI } from '~stores/base-api';

interface GetUserParams {
  locationId: string;
}

export interface User {
  _id: string;
  appId: string;
  locationId: string;
  companyId: string;
  userId: string;
  accessToken: string;
  refreshToken: string;
  createdAt: string;
  storageUsed: number;
  __v: number;
}

export const getUserAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getUser: build.query<User, GetUserParams>({
      query: ({ locationId }) => ({
        url: `/users/${locationId}`,
      }),
    }),
  }),
});

export const { useGetUserQuery } = getUserAPI;
