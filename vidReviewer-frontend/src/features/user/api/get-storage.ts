import { baseAPI, TAGS } from '~stores/base-api';

interface GetStorageParams {
  locationId: string;
}
interface GetStorageResponse {
  storage: {
    normal: number;
    archived: number;
    _id: string;
  };
}

export const getStorageAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getStorage: build.query<GetStorageResponse, GetStorageParams>({
      providesTags: [TAGS.STORAGE],
      query: ({ locationId }) => ({
        url: `/users/${locationId}/storage`,
      }),
    }),
  }),
});

export const { useGetStorageQuery } = getStorageAPI;
