import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type RootState } from '~stores/store';

import { type User } from '../api/get-user';

interface UserSliceState {
  user: User | null | undefined;
  accessToken: string | null;
  refreshToken: string | null;
  locationId: string | null;
}

const initialState: UserSliceState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  locationId: null,
};

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<{ user: User }>) => {
      const { user } = action.payload;
      const accessToken = user.accessToken;
      const refreshToken = user.refreshToken;
      const locationId = user.locationId;

      state.user = user;
      state.accessToken = accessToken;
      state.refreshToken = refreshToken;
      state.locationId = locationId;

      // localStorage.setItem(ACCESS_TOKEN, accessToken);
    },
    logout: (state) => {
      state.user = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.locationId = null;
      // localStorage.removeItem(ACCESS_TOKEN);
    },
  },
});

export const { setUser, logout } = userSlice.actions;

export const selectUser = (state: RootState) => state.user;

export default userSlice;
