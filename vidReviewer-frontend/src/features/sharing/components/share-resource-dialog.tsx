import { Ch<PERSON><PERSON>Down, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '~components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '~components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~components/ui/dropdown-menu';
import { Input } from '~components/ui/input';
import { useAppSelector } from '~stores/hooks';

import { useGetContactsMutation } from '~features/contacts/api/get-contacts';
import { useSendEmailNotificationMutation } from '~features/notification/api/send-email-notification';
import { useSendNotificationMutation } from '~features/notification/api/send-notification';
import { useCreateSharingTokenMutation } from '~features/sharing/api/create-sharing-token';
import { CopyLinkDialog } from '~features/sharing/components/copy-link-dialog';
import { type ShareDialogProps } from '~features/sharing/types/sharing';
import { selectUser } from '~features/user/store/user-slice';

const frontendUrl = 'https://review.vidlead.com';

interface SelectOption {
  label: string;
  value: string;
  email: string;
  firstName: string;
}

export const ShareResourceDialog = ({
  isOpen,
  setIsOpen,
  resourceType,
  resourceId,
  resourceTitle,
}: ShareDialogProps) => {
  const { locationId } = useAppSelector(selectUser);
  const [data, setData] = useState<Array<SelectOption>>([]);
  const [filteredData, setFilteredData] = useState<Array<SelectOption>>([]);
  const [selectedContacts, setSelectedContacts] = useState<Array<SelectOption>>(
    [],
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [shareableLink, setShareableLink] = useState('');
  const [linkCopyDialogOpen, setLinkCopyDialogOpen] = useState(false);

  const [getContacts] = useGetContactsMutation();
  const [sendNotification, { isLoading: isSendNotificationLoading }] =
    useSendNotificationMutation();
  const [sendEmailNotification, { isLoading: isSendEmailNotificationLoading }] =
    useSendEmailNotificationMutation();
  const [createSharingToken] = useCreateSharingTokenMutation();

  useEffect(() => {
    const fetchContacts = async () => {
      if (isOpen && locationId) {
        try {
          const response = await getContacts({ locationId }).unwrap();
          const formattedData = response.contacts.map((contact: any) => {
            // Format names from lowercase fields
            const firstName =
              contact.firstNameLowerCase &&
              contact.firstNameLowerCase.length > 1
                ? contact.firstNameLowerCase.substring(0, 1).toUpperCase() +
                  contact.firstNameLowerCase.substring(1)
                : (contact.firstNameLowerCase?.toUpperCase() ?? 'Unknown');

            const lastName =
              contact.lastNameLowerCase && contact.lastNameLowerCase.length > 1
                ? contact.lastNameLowerCase.substring(0, 1).toUpperCase() +
                  contact.lastNameLowerCase.substring(1)
                : (contact.lastNameLowerCase?.toUpperCase() ?? '');

            return {
              label: `${firstName} ${lastName}${contact.email ? ` • ${contact.email}` : ''}`,
              value: contact.id,
              email: contact.email || '',
              firstName,
            };
          });
          setData(formattedData);
          setFilteredData(formattedData);
        } catch (error: any) {
          console.error('Error fetching contacts:', error);

          // Handle authentication errors
          if (
            error?.status === 401 ||
            error?.data?.error === 'Re-authentication required.'
          ) {
            toast.error(
              'Authentication required. Please reconnect your GoHighLevel account.',
            );
          } else if (
            error?.status === 400 &&
            error?.data?.error === 'Access token not found'
          ) {
            toast.error(
              'Access token not found. Please reconnect your GoHighLevel account.',
            );
          } else {
            toast.error('Failed to load contacts');
          }
        }
      }
    };

    void fetchContacts();
  }, [isOpen, locationId, getContacts]);

  // Handle search functionality
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredData(data);
    } else {
      const filtered = data.filter(
        (contact) =>
          contact.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          contact.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          contact.firstName.toLowerCase().includes(searchQuery.toLowerCase()),
      );
      setFilteredData(filtered);
    }
  }, [searchQuery, data]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);

    // If query is long enough, also search via API
    if (query.length >= 3 && locationId) {
      try {
        const response = await getContacts({
          locationId,
          query,
        }).unwrap();

        const formattedData = response.contacts.map((contact: any) => {
          const firstName =
            contact.firstNameLowerCase && contact.firstNameLowerCase.length > 1
              ? contact.firstNameLowerCase.substring(0, 1).toUpperCase() +
                contact.firstNameLowerCase.substring(1)
              : (contact.firstNameLowerCase?.toUpperCase() ?? 'Unknown');

          const lastName =
            contact.lastNameLowerCase && contact.lastNameLowerCase.length > 1
              ? contact.lastNameLowerCase.substring(0, 1).toUpperCase() +
                contact.lastNameLowerCase.substring(1)
              : (contact.lastNameLowerCase?.toUpperCase() ?? '');

          return {
            label: `${firstName} ${lastName}${contact.email ? ` • ${contact.email}` : ''}`,
            value: contact.id,
            email: contact.email || '',
            firstName,
          };
        });

        setFilteredData(formattedData);
      } catch (error) {
        console.error('Error searching contacts:', error);
        // Fall back to local filtering if API search fails
      }
    }
  };

  const getResourceTypeLabel = () => {
    switch (resourceType) {
      case 'project':
        return 'Project';
      case 'folder':
        return 'Folder';
      case 'video':
        return 'Video';
      default:
        return 'Resource';
    }
  };

  const onSendNotification = async (type: 'Email' | 'SMS') => {
    try {
      const notificationPromises = selectedContacts.map(async (contact) => {
        try {
          // Create sharing token
          const tokenResponse = await createSharingToken({
            resourceType,
            resourceId,
            name: contact.label.split(' • ')[0],
            email: contact.email,
            locationId: locationId!,
          }).unwrap();

          // Send notification using the sharing token
          const sharingLink = `${frontendUrl}/shared/${tokenResponse.token}`;

          if (type === 'Email') {
            // Use /notifications for emails
            await sendEmailNotification({
              locationId: locationId!,
              contactId: contact.value,
              emailTo: contact.email,
              firstName: contact.firstName,
              link: sharingLink,
              type: 'Email',
            }).unwrap();
          } else {
            // Use /notifications for SMS
            await sendNotification({
              locationId: locationId!,
              contactId: contact.value,
              emailTo: contact.email,
              firstName: contact.firstName,
              link: sharingLink,
              type,
            }).unwrap();
          }

          return { contact, success: true };
        } catch (error: any) {
          console.error('Error processing contact:', contact, error);

          // Provide specific error messages
          let errorMessage = 'Failed to create sharing link';
          if (error?.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error?.message) {
            errorMessage = error.message;
          }

          return { contact, success: false, error: errorMessage };
        }
      });

      const results = await Promise.all(notificationPromises);

      // Check overall success and notify user
      const successCount = results.filter((result) => result.success).length;
      if (successCount === selectedContacts.length) {
        toast.success(
          `All ${type === 'Email' ? 'emails' : 'SMS'} sent successfully`,
        );
        setIsOpen(false);
        setSelectedContacts([]);
        setSearchQuery('');
      } else {
        toast.error('Some notifications failed. Check logs for details.');
      }
    } catch (globalError) {
      console.error(
        'Unexpected error during notification process:',
        globalError,
      );
      toast.error('An unexpected error occurred. Please try again.');
    }
  };

  const onCopyLink = async () => {
    try {
      // Use generic info for copy link functionality - no specific contact needed
      const contactInfo = {
        name: 'Shared User',
        email: `shared-${Date.now()}@example.com`, // Make email unique to avoid conflicts
      };

      if (!locationId) {
        console.error('❌ LocationId is missing!');
        toast.error('Location ID is required. Please try again.');
        return;
      }

      const tokenResponse = await createSharingToken({
        resourceType,
        resourceId,
        name: contactInfo.name,
        email: contactInfo.email,
        locationId: locationId,
      }).unwrap();

      // Generate sharing link
      const sharingLink = `${frontendUrl}/shared/${tokenResponse.token}`;
      console.log('🔗 Generated sharing link:', sharingLink);

      // Show success message regardless of whether it's a new or existing token
      const successMessage = tokenResponse.message?.includes('already exists')
        ? 'Link Retrieved Successfully!'
        : 'Link Created Successfully!';

      console.log('📢 Showing success message:', successMessage);
      toast.success(successMessage);

      // Set the shareable link and open the copy dialog
      console.log('🎯 Setting shareable link and opening dialog:', sharingLink);
      setShareableLink(sharingLink);
      setLinkCopyDialogOpen(true);

      // Close the main dialog
      setIsOpen(false);
      setSelectedContacts([]);
      setSearchQuery('');
    } catch (error: any) {
      console.error('❌ Error creating sharing token or copying link:', error);
      console.error('❌ Error details:', {
        message: error?.message,
        response: error?.response?.data,
        status: error?.response?.status,
        data: error?.data,
      });

      // Log the full error object for debugging
      console.error('❌ Full error object:', error);

      // Provide specific error messages
      let errorMessage = 'Failed to copy link. Please try again.';
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.data?.message) {
        errorMessage = error.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    }
  };

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={() => {
          setIsOpen(false);
          setSelectedContacts([]);
          setSearchQuery('');
        }}
      >
        <DialogContent className='w-[calc(100vw-2rem)] max-w-[800px]'>
          <DialogHeader className='mb-4'>
            <DialogTitle>
              Share {getResourceTypeLabel()}: {resourceTitle}
            </DialogTitle>
          </DialogHeader>

          <div className='space-y-4'>
            <div className='space-y-2'>
              <label className='text-sm font-medium'>
                Select contacts to share with:
              </label>
              <div className='flex min-h-[40px] flex-wrap gap-2 rounded-md border p-2'>
                {selectedContacts.length === 0 ? (
                  <div className='py-1 text-sm text-gray-500'>
                    Please select at least one contact to share with
                  </div>
                ) : (
                  selectedContacts.map((contact) => (
                    <div
                      key={contact.value}
                      className='flex items-center gap-1 rounded bg-blue-100 px-2 py-1 text-sm text-blue-800'
                    >
                      {contact.firstName}
                      <button
                        onClick={() => {
                          setSelectedContacts((prev) =>
                            prev.filter((c) => c.value !== contact.value),
                          );
                        }}
                        className='ml-1 text-blue-600 hover:text-blue-800'
                      >
                        ×
                      </button>
                    </div>
                  ))
                )}
              </div>
            </div>

            <div className='space-y-2'>
              <label className='text-sm font-medium'>Available contacts:</label>
              <Input
                type='text'
                placeholder='Search contacts...'
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className='mb-2'
              />
              <div className='max-h-40 overflow-y-auto rounded-md border'>
                {filteredData.filter(
                  (contact) =>
                    !selectedContacts.some(
                      (selected) => selected.value === contact.value,
                    ),
                ).length === 0 ? (
                  <div className='p-4 text-center text-sm text-gray-500'>
                    {searchQuery
                      ? 'No contacts found matching your search'
                      : 'No contacts available'}
                  </div>
                ) : (
                  filteredData
                    .filter(
                      (contact) =>
                        !selectedContacts.some(
                          (selected) => selected.value === contact.value,
                        ),
                    )
                    .map((contact) => (
                      <div
                        key={contact.value}
                        className='cursor-pointer border-b p-2 last:border-b-0 hover:bg-gray-50'
                        onClick={() => {
                          setSelectedContacts((prev) => [...prev, contact]);
                        }}
                      >
                        {contact.label}
                      </div>
                    ))
                )}
              </div>
            </div>
          </div>

          <DialogFooter className='flex justify-between'>
            <Button
              variant='outline'
              onClick={() => {
                setIsOpen(false);
                setSelectedContacts([]);
                setSearchQuery('');
              }}
            >
              Cancel
            </Button>

            <div className='flex gap-2'>
              {/* Share dropdown - Always available with Copy Link option */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    disabled={
                      isSendNotificationLoading ||
                      isSendEmailNotificationLoading
                    }
                    className='flex items-center gap-2'
                  >
                    {(isSendNotificationLoading ||
                      isSendEmailNotificationLoading) && (
                      <Loader2 className='size-4 animate-spin' />
                    )}
                    Share{' '}
                    {selectedContacts.length > 0 &&
                      `(${selectedContacts.length})`}
                    <ChevronDown className='size-4' />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className='w-[120px] gap-2 rounded-md border'>
                  <DropdownMenuGroup>
                    {/* Copy Link option - Always available */}
                    <DropdownMenuItem onClick={onCopyLink}>
                      <span className='text-sm font-normal text-[#1D1D1B]'>
                        Copy Link
                      </span>
                    </DropdownMenuItem>

                    {/* Show separator and contact sharing options only when contacts are selected */}
                    {selectedContacts.length > 0 && (
                      <>
                        <DropdownMenuSeparator className='bg-[#D9D9D9]' />

                        <DropdownMenuItem
                          onClick={() => onSendNotification('Email')}
                        >
                          <span className='text-sm font-normal text-[#1D1D1B]'>
                            Share Email
                          </span>
                        </DropdownMenuItem>

                        <DropdownMenuItem
                          onClick={() => onSendNotification('SMS')}
                        >
                          <span className='text-sm font-normal text-[#1D1D1B]'>
                            Share SMS
                          </span>
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <CopyLinkDialog
        isOpen={linkCopyDialogOpen}
        onClose={() => {
          setLinkCopyDialogOpen(false);
          setShareableLink('');
        }}
        shareableLink={shareableLink}
      />
    </>
  );
};
