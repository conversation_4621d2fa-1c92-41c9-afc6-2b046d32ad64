import React from 'react';
import { toast } from 'sonner';
import { Button } from '~components/ui/button';

interface ManualCopyModalProps {
  isOpen: boolean;
  onClose: () => void;
  shareableLink: string;
}

export const ManualCopyModal: React.FC<ManualCopyModalProps> = ({
  isOpen,
  onClose,
  shareableLink,
}) => {
  console.log('🎯 ManualCopyModal render - isOpen:', isOpen, 'link:', shareableLink);

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareableLink);
      toast.success('Link copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      toast.error('Failed to copy link');
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 className="text-lg font-semibold mb-4">Copy your shareable link!</h2>
        
        <div className="flex space-x-2 mb-4">
          <input
            readOnly
            value={shareableLink}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md font-mono text-sm"
          />
          <Button onClick={handleCopyToClipboard}>
            Copy
          </Button>
        </div>

        <div className="flex justify-end">
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};
