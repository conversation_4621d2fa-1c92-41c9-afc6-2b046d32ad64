import { Button } from '~components/ui/button';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~components/ui/dialog';
import { Input } from '~components/ui/input';

interface CopyLinkDialogProps {
  isOpen: boolean;
  onClose: () => void;
  shareableLink: string;
}

export const CopyLinkDialog: React.FC<CopyLinkDialogProps> = ({
  isOpen,
  onClose,
  shareableLink,
}) => {
  // const handleCopyToClipboard = async () => {
  //   try {
  //     await navigator.clipboard.writeText(shareableLink);
  //     toast.success('Link copied to clipboard!');
  //   } catch (error) {
  //     console.error('Failed to copy to clipboard:', error);
  //     toast.error('Failed to copy link');
  //   }
  // };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Copy your shareable link!</DialogTitle>
        </DialogHeader>

        <div className='flex space-x-2'>
          <Input
            readOnly
            value={shareableLink}
            className='font-mono text-sm ring-1 ring-ring'
          />
          {/* <Button onClick={handleCopyToClipboard}>Copy</Button> */}
        </div>

        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
