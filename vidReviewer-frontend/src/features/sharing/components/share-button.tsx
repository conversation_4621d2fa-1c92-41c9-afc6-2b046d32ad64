import { Share2 } from 'lucide-react';
import { useState } from 'react';
import { Button } from '~components/ui/button';

import { type ResourceType } from '~features/sharing/types/sharing';

import { ShareResourceDialog } from './share-resource-dialog';

interface ShareButtonProps {
  resourceType: ResourceType;
  resourceId: string;
  resourceTitle: string;
  variant?: 'default' | 'ghost' | 'outline' | 'secondary';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showText?: boolean;
  className?: string;
}

export const ShareButton = ({
  resourceType,
  resourceId,
  resourceTitle,
  variant = 'secondary',
  size = 'default',
  showText = true,
  className = '',
}: ShareButtonProps) => {
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);

  return (
    <>
      <Button
        type='button'
        variant={variant}
        size={size}
        className={className}
        onClick={(e) => {
          e.stopPropagation(); // Prevent triggering parent click events
          setIsShareDialogOpen(true);
        }}
      >
        <Share2 className='size-4' />
        {showText && (
          <span className='ml-2'>
            Share{' '}
            {resourceType === 'project'
              ? 'Project'
              : resourceType === 'folder'
                ? 'Folder'
                : 'Video'}
          </span>
        )}
      </Button>

      <ShareResourceDialog
        isOpen={isShareDialogOpen}
        setIsOpen={setIsShareDialogOpen}
        resourceType={resourceType}
        resourceId={resourceId}
        resourceTitle={resourceTitle}
      />
    </>
  );
};
