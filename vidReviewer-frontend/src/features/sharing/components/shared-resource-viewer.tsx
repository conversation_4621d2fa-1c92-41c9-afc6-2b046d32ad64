import { useNavigate, useParams } from '@tanstack/react-router';
import {
  ArrowLeft,
  Download,
  Folder,
  FolderOpen,
  Loader2,
  Play,
  Video,
  X,
} from 'lucide-react';
import { useState } from 'react';
import { Button } from '~components/ui/button';
import { Card, CardContent, CardHeader } from '~components/ui/card';

import { useGetSharedResourceQuery } from '~features/sharing/api/get-shared-resource';
import { getEffectiveThumbnailUrl } from '~features/video/utils/thumbnail-utils';

// Video Card Component for shared views
const VideoCard = ({
  video,
  onVideoClick,
}: {
  video: any;
  onVideoClick?: (video: any) => void;
}) => {
  const thumbnailUrl = getEffectiveThumbnailUrl(video);

  const handleClick = () => {
    if (onVideoClick) {
      onVideoClick(video);
    }
  };

  return (
    <Card
      className='cursor-pointer transition-shadow hover:shadow-md'
      onClick={handleClick}
    >
      <CardHeader className='p-0'>
        <div className='relative aspect-video overflow-hidden rounded-t-lg bg-gray-100'>
          {thumbnailUrl ? (
            <img
              src={thumbnailUrl}
              alt={video.title}
              className='size-full object-cover'
            />
          ) : (
            <div className='flex size-full items-center justify-center bg-gray-200'>
              <Video className='size-8 text-gray-400' />
            </div>
          )}
          <div className='absolute bottom-2 right-2 flex items-center gap-1 rounded bg-black/75 px-2 py-1 text-xs text-white'>
            <Play className='size-3' />
            {video.duration
              ? `${Math.floor(video.duration / 60)}:${(video.duration % 60).toString().padStart(2, '0')}`
              : '0:00'}
          </div>
        </div>
      </CardHeader>
      <CardContent className='p-4'>
        <h4 className='mb-2 truncate font-medium'>{video.title}</h4>
        <p className='mb-2 line-clamp-2 text-sm text-gray-600'>
          {video.description || 'No description'}
        </p>
        <div className='flex items-center justify-between text-xs text-gray-500'>
          <span>By {video.uploadedBy?.userName || 'Unknown'}</span>
          <span>{new Date(video.createdAt).toLocaleDateString()}</span>
        </div>
      </CardContent>
    </Card>
  );
};

export const SharedResourceViewer = () => {
  const { token } = useParams({ from: '/shared/$token' });
  const navigate = useNavigate();
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [currentFolder, setCurrentFolder] = useState<any>(null);

  const {
    data: sharedResource,
    isLoading,
    error,
  } = useGetSharedResourceQuery(token);

  const handleVideoClick = (video: any) => {
    void navigate({
      to: '/shared/$token/$videoId',
      params: {
        token: token,
        videoId: video._id,
      },
    });
  };

  const handleFolderClick = (folder: any) => {
    // Add videos that belong to this folder
    const folderVideos =
      sharedResource?.resource.videos?.filter(
        (video: any) => video.projectFolderId === folder._id,
      ) || [];

    const folderWithVideos = {
      ...folder,
      videos: folderVideos,
    };

    setCurrentFolder(folderWithVideos);
  };

  const handleCloseVideo = () => {
    setSelectedVideo(null);
  };

  // Download function
  const handleDownload = async (video: any) => {
    try {
      const response = await fetch(video.videoUrl);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download =
        video.title + '.' + (video.videoUrl.split('.').pop() ?? 'mp4');
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  // Video Modal Component
  const VideoModal = ({
    video,
    onClose,
  }: {
    video: any;
    onClose: () => void;
  }) => (
    <div className='fixed inset-0 z-50 flex items-center justify-center bg-black/75 p-4'>
      <div className='max-h-[90vh] w-full max-w-4xl overflow-hidden rounded-lg bg-white'>
        <div className='flex items-center justify-between border-b p-4'>
          <h3 className='text-lg font-semibold'>{video.title}</h3>
          <div className='flex items-center gap-2'>
            {/* Download button - only show if video is downloadable */}
            {video.isDownloadable && (
              <Button
                variant='outline'
                size='sm'
                onClick={() => handleDownload(video)}
                className='flex items-center gap-2'
              >
                <Download className='size-4' />
                Download
              </Button>
            )}
            <Button variant='ghost' size='sm' onClick={onClose}>
              <X className='size-4' />
            </Button>
          </div>
        </div>
        <div className='p-4'>
          {video.hlsPlaylistUrl ? (
            <div className='mb-4 aspect-video overflow-hidden rounded-lg bg-black'>
              <video
                controls
                controlsList='nodownload'
                disablePictureInPicture
                className='size-full'
                poster={video.videoThumbnail || video.customThumbnail}
                autoPlay
              >
                <source
                  src={video.hlsPlaylistUrl}
                  type='application/x-mpegURL'
                />
                <source src={video.videoUrl} type='video/mp4' />
                Your browser does not support the video tag.
              </video>
            </div>
          ) : (
            <div className='mb-4 flex aspect-video items-center justify-center rounded-lg bg-gray-100'>
              <p className='text-gray-600'>Video not available</p>
            </div>
          )}
          <div className='space-y-2'>
            <p className='text-gray-600'>
              {video.description || 'No description'}
            </p>
            <div className='flex items-center justify-between text-sm text-gray-500'>
              <span>Uploaded by {video.uploadedBy?.userName || 'Unknown'}</span>
              <span>{new Date(video.createdAt).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className='flex h-screen w-full items-center justify-center'>
        <Loader2 size={48} className='animate-spin' />
      </div>
    );
  }

  if (error || !sharedResource) {
    return (
      <div className='flex h-screen w-full items-center justify-center'>
        <div className='text-center'>
          <h2 className='mb-2 text-2xl font-bold text-gray-900'>
            Invalid or Expired Link
          </h2>
          <p className='mb-4 text-gray-600'>
            This sharing link is invalid or has expired.
          </p>
          <Button onClick={() => navigate({ to: '/folders' })}>Go Home</Button>
        </div>
      </div>
    );
  }

  const renderResourceContent = () => {
    // If viewing a specific folder within a project
    if (currentFolder && sharedResource.resourceType === 'project') {
      return (
        <FolderView resource={currentFolder} onVideoClick={handleVideoClick} />
      );
    }

    switch (sharedResource.resourceType) {
      case 'project':
        return (
          <ProjectView
            resource={sharedResource.resource}
            onVideoClick={handleVideoClick}
            onFolderClick={handleFolderClick}
          />
        );
      case 'folder':
        return (
          <FolderView
            resource={sharedResource.resource}
            onVideoClick={handleVideoClick}
          />
        );
      case 'video':
        return <VideoView resource={sharedResource.resource} />;
      default:
        return <div>Unknown resource type</div>;
    }
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      <header className='border-b bg-white px-6 py-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            {/* Show back button only for projects and folders, not for single videos */}
            {sharedResource.resourceType !== 'video' && (
              <Button
                variant='ghost'
                size='sm'
                onClick={() => navigate({ to: '/folders' })}
              >
                <ArrowLeft className='mr-2 size-4' />
                Back
              </Button>
            )}
            <div>
              <h1 className='text-xl font-semibold'>
                Shared{' '}
                {sharedResource.resourceType.charAt(0).toUpperCase() +
                  sharedResource.resourceType.slice(1)}
                {currentFolder && ` / ${currentFolder.name}`}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className='p-4'>{renderResourceContent()}</main>

      {/* Video Modal */}
      {selectedVideo && (
        <VideoModal video={selectedVideo} onClose={handleCloseVideo} />
      )}
    </div>
  );
};

const ProjectView = ({
  resource,
  onVideoClick,
  onFolderClick,
}: {
  resource: any;
  onVideoClick?: (video: any) => void;
  onFolderClick?: (folder: any) => void;
}) => {
  const folders = resource.folders || [];
  const videos = resource.videos || [];

  return (
    <div className='space-y-4'>
      {/* Project Header */}
      <Card className='max-w-4xl'>
        <CardHeader className='pb-2'>
          <div className='flex items-center gap-3'>
            {/* Project Thumbnail */}
            <div className='size-10 shrink-0 overflow-hidden rounded-md bg-gray-100'>
              {resource.thumbnails && resource.thumbnails.length > 0 ? (
                <div className='grid size-full grid-cols-2 gap-0.5'>
                  {resource.thumbnails
                    .slice(0, 4)
                    .map((thumbnail: any, index: number) => (
                      <div key={index} className='relative overflow-hidden'>
                        {thumbnail.videoThumbnail ? (
                          <img
                            src={thumbnail.videoThumbnail}
                            alt={`${resource.title} video ${index + 1}`}
                            className='size-full object-cover'
                          />
                        ) : (
                          <div className='flex size-full items-center justify-center bg-gray-200'>
                            <FolderOpen className='size-2 text-blue-600' />
                          </div>
                        )}
                      </div>
                    ))}
                  {/* Fill remaining slots if less than 4 thumbnails */}
                  {Array.from({ length: 4 - resource.thumbnails.length }).map(
                    (_, index) => (
                      <div
                        key={`empty-${index}`}
                        className='flex size-full items-center justify-center bg-gray-200'
                      >
                        <FolderOpen className='size-2 text-blue-600' />
                      </div>
                    ),
                  )}
                </div>
              ) : (
                <div className='flex size-full items-center justify-center'>
                  <FolderOpen className='size-6 text-blue-600' />
                </div>
              )}
            </div>
            <div>
              <h2 className='text-lg font-bold'>{resource.title}</h2>
              <p className='text-sm text-gray-600'>
                {resource.description || 'No description'}
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className='pb-3 pt-0'>
          <div className='mb-1 grid grid-cols-2 gap-2 text-xs'>
            <div>
              <span className='font-medium'>Created by:</span>{' '}
              {resource.createdBy?.userName || 'Unknown'}
            </div>
            <div>
              <span className='font-medium'>Created:</span>{' '}
              {new Date(resource.createdAt).toLocaleDateString()}
            </div>
          </div>
          <div className='flex items-center justify-end'>
            <div className='text-xs text-gray-500'>
              {folders.length} folder{folders.length !== 1 ? 's' : ''},{' '}
              {videos.length} video{videos.length !== 1 ? 's' : ''}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Folders Section */}
      {folders.length > 0 && (
        <div>
          <h2 className='mb-4 text-lg font-semibold text-gray-900'>Folders</h2>
          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
            {folders.map((folder: any) => (
              <Card
                key={folder._id}
                className='cursor-pointer transition-shadow hover:shadow-md'
                onClick={() => onFolderClick?.(folder)}
              >
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <div className='flex items-center gap-2'>
                    <Folder className='size-5 text-blue-600' />
                    <h4 className='truncate font-medium'>{folder.name}</h4>
                  </div>
                </CardHeader>
                <CardContent className='pt-0'>
                  <p className='mb-2 line-clamp-2 text-sm text-gray-600'>
                    {folder.description || 'No description'}
                  </p>
                  <div className='text-xs text-gray-500'>
                    Created {new Date(folder.createdAt).toLocaleDateString()}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Videos Section */}
      {videos.length > 0 && (
        <div>
          <h2 className='mb-4 text-lg font-semibold text-gray-900'>Videos</h2>
          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
            {videos.map((video: any) => (
              <VideoCard
                key={video._id}
                video={video}
                onVideoClick={onVideoClick}
              />
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {folders.length === 0 && videos.length === 0 && (
        <div className='py-12 text-center'>
          <FolderOpen className='mx-auto mb-4 size-12 text-gray-400' />
          <p className='text-gray-600'>This project is empty.</p>
        </div>
      )}
    </div>
  );
};

const FolderView = ({
  resource,
  onVideoClick,
}: {
  resource: any;
  onVideoClick?: (video: any) => void;
}) => {
  const subfolders = resource.subfolders || [];
  const videos = resource.videos || [];

  return (
    <div className='space-y-4'>
      {/* Folder Header */}
      <Card className='max-w-4xl'>
        <CardHeader className='pb-2'>
          <div className='flex items-center gap-3'>
            <Folder className='size-6 text-yellow-600' />
            <div>
              <h2 className='text-lg font-bold'>{resource.name}</h2>
              <p className='text-sm text-gray-600'>
                {resource.description || 'No description'}
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className='pb-3 pt-0'>
          {resource.contextId && (
            <div className='mb-1 text-xs'>
              <span className='font-medium'>Project:</span>{' '}
              {resource.contextId?.title || 'Unknown'}
            </div>
          )}
          <div className='mb-1 text-xs'>
            <span className='font-medium'>Created:</span>{' '}
            {new Date(resource.createdAt).toLocaleDateString()}
          </div>
          <div className='flex items-center justify-end'>
            <div className='text-xs text-gray-500'>
              {subfolders.length} subfolder{subfolders.length !== 1 ? 's' : ''},{' '}
              {videos.length} video{videos.length !== 1 ? 's' : ''}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subfolders Section */}
      {subfolders.length > 0 && (
        <div>
          <h2 className='mb-4 text-lg font-semibold text-gray-900'>
            Subfolders
          </h2>
          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
            {subfolders.map((subfolder: any) => (
              <Card
                key={subfolder._id}
                className='cursor-pointer transition-shadow hover:shadow-md'
              >
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <div className='flex items-center gap-2'>
                    <Folder className='size-5 text-yellow-600' />
                    <h4 className='truncate font-medium'>{subfolder.name}</h4>
                  </div>
                </CardHeader>
                <CardContent className='pt-0'>
                  <p className='mb-2 line-clamp-2 text-sm text-gray-600'>
                    {subfolder.description || 'No description'}
                  </p>
                  <div className='text-xs text-gray-500'>
                    Created {new Date(subfolder.createdAt).toLocaleDateString()}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Videos Section */}
      {videos.length > 0 && (
        <div>
          <h2 className='mb-4 text-lg font-semibold text-gray-900'>Videos</h2>
          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
            {videos.map((video: any) => (
              <VideoCard
                key={video._id}
                video={video}
                onVideoClick={onVideoClick}
              />
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {subfolders.length === 0 && videos.length === 0 && (
        <div className='py-12 text-center'>
          <Folder className='mx-auto mb-4 size-12 text-gray-400' />
          <p className='text-gray-600'>This folder is empty.</p>
        </div>
      )}
    </div>
  );
};

const VideoView = ({ resource }: { resource: any }) => {
  return (
    <div className='space-y-4'>
      <Card className='max-w-4xl'>
        <CardHeader className='pb-2'>
          <div className='flex items-center gap-3'>
            <Video className='size-6 text-red-600' />
            <div>
              <h2 className='text-lg font-bold'>{resource.title}</h2>
              <p className='text-sm text-gray-600'>
                {resource.description || 'No description'}
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className='mb-6 grid grid-cols-2 gap-4 text-sm'>
            <div>
              <span className='font-medium'>Uploaded by:</span>{' '}
              {resource.uploadedBy?.userName || 'Unknown'}
            </div>
            <div>
              <span className='font-medium'>Project:</span>{' '}
              {resource.projectId?.title || 'Unknown'}
            </div>
            <div>
              <span className='font-medium'>Duration:</span>{' '}
              {resource.duration
                ? `${Math.floor(resource.duration / 60)}:${(resource.duration % 60).toString().padStart(2, '0')}`
                : 'Unknown'}
            </div>
            <div>
              <span className='font-medium'>Uploaded:</span>{' '}
              {new Date(resource.uploadedOn).toLocaleDateString()}
            </div>
          </div>

          {/* Video Player */}
          {resource.hlsPlaylistUrl ? (
            <div className='aspect-video overflow-hidden rounded-lg bg-black'>
              <video
                controls
                className='size-full'
                poster={resource.videoThumbnail || resource.customThumbnail}
              >
                <source
                  src={resource.hlsPlaylistUrl}
                  type='application/x-mpegURL'
                />
                <source src={resource.videoUrl} type='video/mp4' />
                Your browser does not support the video tag.
              </video>
            </div>
          ) : (
            <div className='flex aspect-video items-center justify-center rounded-lg bg-gray-100'>
              <p className='text-gray-600'>Video not available</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
