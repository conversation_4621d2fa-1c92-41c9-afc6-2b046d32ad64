export type ResourceType = 'project' | 'folder' | 'video';

export interface SharingInvitation {
  _id: string;
  name: string;
  email: string;
  resourceType: ResourceType;
  resourceId: string;
  resourceModel: string;
  token: string;
  status: 'pending' | 'accepted' | 'revoked';
  contactId?: string;
  expiresAt: string;
  createdAt: string;
}

export interface ShareDialogProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  resourceType: ResourceType;
  resourceId: string;
  resourceTitle: string;
}

export interface SharedResource {
  resourceType: ResourceType;
  resource: any;
  sharedBy: string;
  sharedEmail: string;
}
