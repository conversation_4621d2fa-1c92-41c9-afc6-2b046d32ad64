import { baseAPI } from '~stores/base-api';

export interface SharedVideo {
  projectFolderId: null | string;
  duration: number;
  _id: string;
  title: string;
  description: string;
  projectId: string;
  status: string;
  uploadedBy: UploadedBy;
  videoUrl: string;
  thumbnailType: string;
  comments: Array<unknown>;
  isDownloadable: boolean;
  isCommentsEnabled: boolean;
  height: number;
  width: number;
  size: number;
  storageClass: string;
  uploadedOn: Date;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
  videoThumbnail: string;
  hlsPlaylistUrl: string;
  transcodingJobName: string;
  processingError?: string;
}

export interface UploadedBy {
  _id: string;
  email: string;
  userName: string;
}

export interface SharedResourceResponse {
  resourceType: 'project' | 'folder' | 'video';
  resource: any; // Will be typed based on resourceType
  sharedBy: string;
  sharedEmail: string;
  sharedTo: string;
  sharedToId: string;
}

export const getSharedResourceAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getSharedResource: build.query<SharedResourceResponse, string>({
      query: (token) => ({
        url: `/sharing/shared/${token}`,
        method: 'GET',
      }),
    }),
  }),
});

export const { useGetSharedResourceQuery } = getSharedResourceAPI;
