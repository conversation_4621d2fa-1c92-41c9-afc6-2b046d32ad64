import { baseAPI, TAGS } from '~stores/base-api';

export interface CreateSharingTokenParams {
  resourceType: 'project' | 'folder' | 'video';
  resourceId: string;
  name: string;
  email: string;
  locationId: string;
}

export interface CreateSharingTokenResponse {
  token: string;
  resourceType: string;
  resourceId: string;
  message?: string;
}

export const createSharingTokenAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    createSharingToken: build.mutation<
      CreateSharingTokenResponse,
      CreateSharingTokenParams
    >({
      invalidatesTags: (_result, _error, { resourceType, resourceId }) => [
        { type: TAGS.COLLABORATORS, id: resourceId },
        // {
        //   type: TAGS.PROJECTS,
        //   id: resourceType === 'project' ? resourceId : 'LIST',
        // },
        // {
        //   type: TAGS.FOLDERS,
        //   id: resourceType === 'folder' ? resourceId : 'LIST',
        // },
        {
          type: TAGS.VIDEOS,
          id: resourceType === 'video' ? resourceId : 'LIST',
        },
      ],
      query: ({ resourceType, resourceId, name, email, locationId }) => ({
        url: `/sharing/${resourceType}/${resourceId}/token`,
        method: 'POST',
        body: { name, email, locationId },
      }),
    }),
  }),
});

export const { useCreateSharingTokenMutation } = createSharingTokenAPI;
