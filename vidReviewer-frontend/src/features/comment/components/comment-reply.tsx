import { Loader2, SendH<PERSON>zon<PERSON> } from 'lucide-react';
import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '~components/ui/avatar';
import { Button } from '~components/ui/button';
import {
  Dialog,
  DialogContent,
  // DialogContentWithoutOverlay,
  DialogTrigger,
} from '~components/ui/dialog';
import { ScrollArea } from '~components/ui/scroll-area';
import { Textarea } from '~components/ui/textarea';
import { cn } from '~lib/utils';
import { useAppSelector } from '~stores/hooks';

import { formatDate } from '~features/comment/utils/format-date';
import { splitNameInitials } from '~features/comment/utils/split-name-initials';
import { selectUser } from '~features/user/store/user-slice';

import { useAddCommentMutation } from '../api/add-comment';
import { type Comment } from '../types/comment';

interface CommentReplyProps {
  comment: Comment;
  isExternalUser?: boolean;
}

export const CommentReply = ({
  comment,
  isExternalUser,
}: CommentReplyProps) => {
  const user = useAppSelector(selectUser);

  const userDetails = localStorage.getItem('userDetails')
    ? JSON.parse(localStorage.getItem('userDetails') || '{}')
    : null;

  const externalUser = localStorage.getItem('externalUser')
    ? JSON.parse(localStorage.getItem('externalUser') || '{}')
    : null;

  const [reply, setReply] = useState('');
  const [isActive, setIsActive] = useState(false);

  const [addComment, { isLoading }] = useAddCommentMutation();

  const handleAddComment = async () => {
    if (reply) {
      try {
        const response = await addComment({
          videoId: comment.videoId,
          userId: isExternalUser ? externalUser._id : user.user?._id,
          content: reply,
          parentCommentId: comment._id,
          time: comment.time,
          addedBy: isExternalUser
            ? externalUser.name
            : userDetails
              ? userDetails.userName
              : 'Anonymous',
        });
        console.log('🚀 ~ handleAddComment ~ response:', response);
        setReply('');
      } catch (error) {
        console.error(error);
      }
    }
  };

  return (
    <Dialog
      onOpenChange={() => {
        setIsActive(!isActive);
      }}
    >
      <DialogTrigger asChild>
        <Button variant={'destructive'}>Reply</Button>
      </DialogTrigger>
      <DialogContent className='w-96'>
        <ScrollArea className='h-[400px]'>
          <div className='mb-2 bg-[#F9F9F9] p-2'>
            <div className='flex justify-between'>
              <div className='mb-1 flex items-center gap-2'>
                <div className='flex items-center gap-2'>
                  <Avatar className='size-6'>
                    <AvatarImage
                      src='/placeholder.svg'
                      alt='User Avatar'
                      className='bg-[#1FAE47]'
                    />
                    <AvatarFallback className='bg-[#1FAE47] text-xs text-white'>
                      {splitNameInitials(comment.addedBy)}
                    </AvatarFallback>
                  </Avatar>
                  <h3 className='text-sm font-semibold'>{comment.addedBy}</h3>
                </div>
                <p className='text-sm font-medium text-muted-foreground'>
                  {formatDate(comment.createdAt)}
                </p>
              </div>
              {/* <CommentMenu /> */}
            </div>
            <div className='flex gap-2'>
              <p className='mb-2'>{comment.content}</p>
            </div>
          </div>

          {comment.replies.map((reply, index) => (
            <div
              key={reply._id}
              className={cn(
                comment.replies.length - 1 === index ? '' : 'mb-2',
                'bg-[#F9F9F9] p-2',
              )}
            >
              <div className='flex justify-between'>
                <div className='mb-1 flex items-center gap-2'>
                  <div className='flex items-center gap-2'>
                    <Avatar className='size-6'>
                      <AvatarImage
                        src='/placeholder.svg'
                        alt='User Avatar'
                        className='bg-[#1FAE47]'
                      />
                      <AvatarFallback className='bg-[#1FAE47] text-xs text-white'>
                        {splitNameInitials(reply.addedBy)}
                      </AvatarFallback>
                    </Avatar>
                    <h3 className='text-sm font-semibold'>{reply.addedBy}</h3>
                  </div>
                  <p className='text-sm font-medium text-muted-foreground'>
                    {formatDate(reply.createdAt)}
                  </p>
                </div>
                {/* <CommentMenu /> */}
              </div>
              <div className='flex gap-2'>
                <p className='mb-2'>{reply.content}</p>
                {/* <button>
              <ThumbsUp />
            </button> */}
              </div>
            </div>
          ))}
        </ScrollArea>

        <div className='relative'>
          <Textarea
            placeholder='Reply'
            rows={4}
            className='mt-2 resize-none border-none bg-[#F6F8FA]'
            value={reply}
            onChange={(e) => {
              setReply(e.target.value);
            }}
          />
          <Button
            variant={'ghost'}
            className='absolute bottom-2 right-2 h-fit p-0'
            onClick={handleAddComment}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className='mr-2 size-4 animate-spin' />
            ) : (
              <SendHorizontal
                // className='text-[#8A8A8A]'
                size={24}
                // fill='#8A8A8A'
              />
            )}
          </Button>
          {/* {reply.length < 1 ? (
          ) : (
            <div className='bottom-0 flex gap-1'>
              <Button variant='outline'>Cancel</Button>
              <Button
                className='bg-[#2B5DE6] hover:bg-[#2B5DE6]'
                onClick={handleAddComment}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 size-4 animate-spin' />
                    Sending
                  </>
                ) : (
                  'Send'
                )}
              </Button>
            </div>
          )} */}
        </div>
      </DialogContent>
    </Dialog>
  );
};
