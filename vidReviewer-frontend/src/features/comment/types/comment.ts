export interface Comment {
  videoId: string;
  userId: string;
  content: string;
  status: string;
  addedBy: string;
  parentCommentId: string | null;
  replies: Array<RepliedComment>;
  time: string;
  _id: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface RepliedComment {
  videoId: string;
  userId: {
    _id: string;
  };
  content: string;
  status: string;
  addedBy: string;
  parentCommentId: string | null;
  replies: Array<RepliedComment>;
  time: string;
  _id: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}
