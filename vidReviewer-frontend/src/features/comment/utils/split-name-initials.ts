export const splitNameInitials = (name: string) => {
  if (!name || name.trim() === '') return 'A';

  const trimmedName = name.trim();
  const nameParts = trimmedName.split(' ').filter(part => part.length > 0);

  if (nameParts.length === 0) return 'A';

  if (nameParts.length === 1) {
    return nameParts[0][0].toUpperCase();
  }

  // Take first letter of first name and first letter of last name
  return nameParts[0][0].toUpperCase() + nameParts[nameParts.length - 1][0].toUpperCase();
};
