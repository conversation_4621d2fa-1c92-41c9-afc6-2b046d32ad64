// Import required functions from date-fns
import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  format,
} from 'date-fns';

// Function to format the date based on your conditions
export function formatDate(dateString: string) {
  const date = new Date(dateString); // Convert the string to a Date object
  const now = new Date(); // Current date and time

  const minutesDifference = differenceInMinutes(now, date);
  const hoursDifference = differenceInHours(now, date);
  const daysDifference = differenceInDays(now, date);

  if (minutesDifference < 60) {
    // Show as minutes (e.g., "20 min", "30 min")
    return `${minutesDifference} min ago`;
  } else if (hoursDifference < 24) {
    // Show as hours (e.g., "1 hour", "10 hours")
    return `${hoursDifference} hour${hoursDifference > 1 ? 's' : ''} ago`;
  } else if (daysDifference === 1) {
    // Show as "yesterday"
    return `yesterday at ${format(date, 'HH:mm')}`;
  } else if (daysDifference > 1) {
    // Show as formatted date and time (e.g., "Jan 09, 2025 HH:MM")
    return `on ${format(date, 'MMM dd, yyyy')} at ${format(date, 'HH:mm')}`;
  }
  return 'invalid date';
}
