import { baseAPI, TAGS } from '~stores/base-api';

import { type Comment } from '~features/comment/types/comment';

interface GetCommentsParams {
  videoId: string;
}

interface GetCommentsResponse {
  comments: Array<Comment>;
}

export const getCommentsAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getComments: build.query<GetCommentsResponse, GetCommentsParams>({
      providesTags: (_result, _error, { videoId }) => [
        { type: TAGS.COMMENTS, id: videoId },
      ],
      query: ({ videoId }) => ({
        url: `/comments/${videoId}`,
      }),
    }),
  }),
});

export const { useGetCommentsQuery } = getCommentsAPI;
