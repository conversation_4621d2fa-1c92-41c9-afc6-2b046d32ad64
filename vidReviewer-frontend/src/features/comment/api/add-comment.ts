import { baseAPI, TAGS } from '~stores/base-api';

import { type Comment } from '~features/comment/types/comment';

interface AddCommentParams {
  videoId: string;
  userId: string;
  content: string;
  parentCommentId: string | null;
  time: string;
  addedBy?: string;
}

export const addCommentAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    addComment: build.mutation<Comment, AddCommentParams>({
      invalidatesTags: (_result, _error, { videoId }) => [
        { type: TAGS.COMMENTS, id: videoId },
        TAGS.VIDEOS,
      ],
      query: (body) => ({
        url: '/comments',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const { useAddCommentMutation } = addCommentAPI;
