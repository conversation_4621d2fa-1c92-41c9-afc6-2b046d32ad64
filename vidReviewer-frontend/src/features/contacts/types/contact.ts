export interface Contact {
  id: string;
  phoneLabel: null | string;
  country: string;
  address: null | string;
  source: null;
  type: string;
  locationId: string;
  website: null;
  dnd: boolean;
  state: null | string;
  businessName: null;
  customFields: Array<CustomField>;
  tags: Array<string>;
  dateAdded: string;
  additionalEmails: Array<string>;
  phone: string | null;
  companyName: null;
  additionalPhones: Array<string>;
  dateUpdated: string;
  city: null | string;
  dateOfBirth: number | null;
  firstNameLowerCase: null | string;
  lastNameLowerCase: null | string;
  email: null | string;
  assignedTo: null;
  followers: Array<unknown>;
  validEmail: null;
  postalCode: null | string;
  businessId: null;
  searchAfter: Array<number | string>;
  opportunities?: Array<Opportunity>;
}

export interface CustomField {
  id: string;
  value: string;
}

export interface Opportunity {
  pipelineId: string;
  id: string;
  monetaryValue: number;
  pipelineStageId: string;
  status: string;
}
