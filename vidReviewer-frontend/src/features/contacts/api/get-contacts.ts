import { baseAPI } from '~stores/base-api';

import { type Contact } from '~features/contacts/types/contact';

interface GetContactsParams {
  locationId: string;
  query?: string;
}

interface GetContactsResponse {
  contacts: Array<Contact>;
  total: number;
  traceId: string;
}

export const getContactsAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getContacts: build.mutation<GetContactsResponse, GetContactsParams>({
      // providesTags: (_result, _error, { videoId }) => [
      //   { type: TAGS.COMMENTS, id: videoId },
      // ],
      query: ({ locationId, query }) => ({
        url: `/contacts/${locationId}`,
        method: 'POST',
        body: { query },
      }),
    }),
  }),
});

export const { useGetContactsMutation } = getContactsAPI;
