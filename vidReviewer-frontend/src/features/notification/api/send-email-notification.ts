import { baseAPI } from '~stores/base-api';

interface SendEmailNotificationParams {
  locationId: string;
  contactId: string;
  emailTo: string;
  firstName: string;
  link: string;
  type: 'Email';
}

interface SendEmailNotificationResponse {
  message: string;
}

export const sendEmailNotificationAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    sendEmailNotification: build.mutation<
      SendEmailNotificationResponse,
      SendEmailNotificationParams
    >({
      query: ({ locationId, contactId, emailTo, firstName, link, type }) => ({
        url: `/notifications`,
        method: 'POST',
        body: {
          locationId,
          contactId,
          emailTo,
          firstName,
          link,
          type,
        },
      }),
    }),
  }),
});

export const { useSendEmailNotificationMutation } = sendEmailNotificationAPI;
