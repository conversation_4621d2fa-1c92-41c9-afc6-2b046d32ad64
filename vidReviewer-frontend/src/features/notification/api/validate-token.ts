import { baseAPI } from '~stores/base-api';

import { type ExternalUser } from '~features/notification/types/external-user.type';

interface ValidateCustomTokenParams {
  customToken: string;
}
interface ValidateCustomTokenResponse {
  invitation: ExternalUser;
  message: string;
}

export const validateCustomTokenAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    validateCustomToken: build.mutation<
      ValidateCustomTokenResponse,
      ValidateCustomTokenParams
    >({
      // providesTags: (_result, _error, { videoId }) => [
      //   { type: TAGS.COMMENTS, id: videoId },
      // ],
      query: ({ customToken }) => ({
        url: `/invitations/validate/${customToken}`,
        method: 'POST',
      }),
    }),
  }),
});

export const { useValidateCustomTokenMutation } = validateCustomTokenAPI;
