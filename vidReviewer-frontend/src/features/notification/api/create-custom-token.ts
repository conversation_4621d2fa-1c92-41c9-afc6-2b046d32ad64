import { baseAPI, TAGS } from '~stores/base-api';

interface CreateCustomTokenParams {
  projectId: string;
  name: string;
  email: string;
}

interface CreateCustomTokenResponse {
  token: string;
  message?: string;
}

export const createCustomTokenAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    createCustomToken: build.mutation<
      CreateCustomTokenResponse,
      CreateCustomTokenParams
    >({
      invalidatesTags: (_result, _error, { projectId }) => [
        { type: TAGS.COLLABORATORS, id: projectId },
      ],
      query: ({ projectId, name, email }) => ({
        url: `/invitations/customToken/${projectId}`,
        method: 'POST',
        body: { name, email },
      }),
    }),
  }),
});

export const { useCreateCustomTokenMutation } = createCustomTokenAPI;
