import { baseAPI } from '~stores/base-api';

import { type Notification } from '../types/notification.type';

interface SendNotificationParams {
  locationId: string;
  contactId: string;
  emailTo: string;
  firstName: string;
  link: string;
  type: 'Email' | 'SMS';
}

export const sendNotificationAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    sendNotification: build.mutation<Notification, SendNotificationParams>({
      // providesTags: (_result, _error, { videoId }) => [
      //   { type: TAGS.COMMENTS, id: videoId },
      // ],
      query: ({ locationId, contactId, emailTo, firstName, link, type }) => ({
        url: `/notifications`,
        method: 'POST',
        body: { locationId, contactId, emailTo, firstName, link, type },
      }),
    }),
  }),
});

export const { useSendNotificationMutation } = sendNotificationAPI;
