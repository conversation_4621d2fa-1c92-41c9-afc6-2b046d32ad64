import { baseAPI } from '~stores/base-api';

interface NotifyCollaboratorsParams {
  videoId: string;
  link: string;
  collaboratorName: string;
}

interface NotifyCollaboratorsResponse {
  message: string;
}

export const notifyCollaboratorsAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    notifyCollaborators: build.mutation<
      NotifyCollaboratorsResponse,
      NotifyCollaboratorsParams
    >({
      // providesTags: (_result, _error, { videoId }) => [
      //   { type: TAGS.COMMENTS, id: videoId },
      // ],
      query: ({ videoId, link, collaboratorName }) => ({
        url: `/notifications/notifyCollaborators`,
        method: 'POST',
        body: { videoId, link, collaboratorName },
      }),
    }),
  }),
});

export const { useNotifyCollaboratorsMutation } = notifyCollaboratorsAPI;
