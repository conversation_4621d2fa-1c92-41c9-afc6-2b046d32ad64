import { type Video } from '../types/video';

/**
 * Get the effective thumbnail URL for a video
 * Returns custom thumbnail if available and type is 'custom', otherwise returns auto thumbnail
 */
export const getEffectiveThumbnailUrl = (video: Video): string => {
  if (video.thumbnailType === 'custom' && video.customThumbnail) {
    return video.customThumbnail;
  }
  return video.videoThumbnail;
};

/**
 * Check if a file type is a valid image type for thumbnails
 */
export const isValidThumbnailFileType = (fileType: string): boolean => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  return validTypes.includes(fileType.toLowerCase());
};

/**
 * Get file extension from file type
 */
export const getFileExtensionFromType = (fileType: string): string => {
  const typeMap: Record<string, string> = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/webp': 'webp',
  };
  return typeMap[fileType.toLowerCase()] || 'jpg';
};

/**
 * Validate thumbnail file size (max 5MB)
 */
export const isValidThumbnailFileSize = (fileSize: number): boolean => {
  const maxSize = 5 * 1024 * 1024; // 5MB in bytes
  return fileSize <= maxSize;
};

/**
 * Format file size for display
 */
export const formatThumbnailFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

/**
 * Create a preview URL for a file
 */
export const createFilePreviewUrl = (file: File): string => {
  return URL.createObjectURL(file);
};

/**
 * Clean up preview URL to prevent memory leaks
 */
export const cleanupFilePreviewUrl = (url: string): void => {
  URL.revokeObjectURL(url);
};

/**
 * Check if video has custom thumbnail
 */
export const hasCustomThumbnail = (video: Video): boolean => {
  return video.thumbnailType === 'custom' && !!video.customThumbnail;
};

/**
 * Get thumbnail type display name
 */
export const getThumbnailTypeDisplayName = (video: Video): string => {
  return video.thumbnailType === 'custom' ? 'Custom' : 'Auto-generated';
};
