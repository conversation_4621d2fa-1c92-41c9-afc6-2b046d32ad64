import { baseAPI, TAGS } from '~stores/base-api';

import { type Video } from '~features/video/types/video';

interface GetVideosParams {
  projectId: string;
}

export const getVideosAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getVideos: build.query<Array<Video>, GetVideosParams>({
      // providesTags: (_result, _error, { projectId }) => [
      //   { type: TAGS.VIDEOS, id: projectId },
      // ],
      providesTags: [TAGS.VIDEOS],
      query: ({ projectId }) => ({
        url: `/videos/project/${projectId}`,
      }),
    }),
  }),
});

export const { useGetVideosQuery } = getVideosAPI;
