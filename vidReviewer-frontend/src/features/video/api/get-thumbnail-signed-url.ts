import { baseAPI } from '~stores/base-api';

interface GetThumbnailSignedURLRequest {
  fileName: string;
  fileType: string;
  videoId: string;
}

export interface GetThumbnailSignedURLResponse {
  url: string;
  fileName: string;
  videoId: string;
}

export const getThumbnailSignedUrlAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getThumbnailSignedUrl: build.mutation<GetThumbnailSignedURLResponse, GetThumbnailSignedURLRequest>({
      query: (body) => ({
        url: '/videos/getThumbnailSignedUrl',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const { useGetThumbnailSignedUrlMutation } = getThumbnailSignedUrlAPI;
