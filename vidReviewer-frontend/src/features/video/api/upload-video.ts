import { baseAPI, TAGS } from '~stores/base-api';

import { type Video } from '../types/video';

interface UploadVideoRequest {
  description?: string;
  gcpFileName: string;
  projectId: string;
  projectFolderId?: string | null;
  title: string;
  uploadedBy: string;
  isDownloadable: boolean;
  isCommentsEnabled: boolean;
  height?: number;
  width?: number;
  size?: number;
}

export const uploadVideoAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    uploadVideo: build.mutation<Video, UploadVideoRequest>({
      invalidatesTags: (result) => [
        { type: TAGS.VIDEOS, id: result?.projectId },
        TAGS.VIDEOS,
        TAGS.PROJECTS,
        TAGS.STORAGE,
      ],
      query: (body) => ({
        url: '/videos',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const { useUploadVideoMutation } = uploadVideoAPI;
