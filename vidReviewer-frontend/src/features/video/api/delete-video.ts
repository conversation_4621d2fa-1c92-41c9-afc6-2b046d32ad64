import { baseAPI, TAGS } from '~stores/base-api';

import { type Video } from '../types/video';

interface DeleteVideoRequest {
  videoId: string;
}

interface DeleteVideoResponse {
  message: string;
  deletedVideo: Video;
}

export const deleteVideoAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    deleteVideo: build.mutation<DeleteVideoResponse, DeleteVideoRequest>({
      // invalidatesTags: (result) => [
      //   { type: TAGS.VIDEOS, id: result?.deletedVideo.projectId },
      //   TAGS.PROJECTS,
      // ],
      invalidatesTags: [TAGS.VIDEOS, TAGS.STORAGE],
      query: ({ videoId }) => ({
        url: `/videos/${videoId}`,
        method: 'DELETE',
      }),
    }),
  }),
});

export const { useDeleteVideoMutation } = deleteVideoAPI;
