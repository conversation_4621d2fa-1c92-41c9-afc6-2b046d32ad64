import { baseAPI, TAGS } from '~stores/base-api';

import { type Video } from '../types/video';

interface UpdateVideoThumbnailRequest {
  videoId: string;
  gcpFileName: string;
}

interface UpdateVideoThumbnailResponse {
  message: string;
  video: Video;
  thumbnailUrl: string;
}

interface ResetVideoThumbnailResponse {
  message: string;
  video: Video;
}

export const updateVideoThumbnailAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    updateVideoThumbnail: build.mutation<UpdateVideoThumbnailResponse, UpdateVideoThumbnailRequest>({
      invalidatesTags: (result) => [
        { type: TAGS.VIDEOS, id: result?.video.projectId },
        TAGS.VIDEOS,
      ],
      query: ({ videoId, gcpFileName }) => ({
        url: `/videos/${videoId}/thumbnail`,
        method: 'PUT',
        body: { gcpFileName },
      }),
    }),
    
    resetVideoThumbnail: build.mutation<ResetVideoThumbnailResponse, { videoId: string }>({
      invalidatesTags: (result) => [
        { type: TAGS.VIDEOS, id: result?.video.projectId },
        TAGS.VIDEOS,
      ],
      query: ({ videoId }) => ({
        url: `/videos/${videoId}/thumbnail`,
        method: 'DELETE',
      }),
    }),
  }),
});

export const { 
  useUpdateVideoThumbnailMutation, 
  useResetVideoThumbnailMutation 
} = updateVideoThumbnailAPI;
