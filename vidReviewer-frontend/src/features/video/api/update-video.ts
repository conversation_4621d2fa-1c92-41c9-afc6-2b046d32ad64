import { baseAPI, TAGS } from '~stores/base-api';

import { type Video } from '../types/video';

interface UpdateVideoRequest {
  videoId: string;
  description?: string;
  title: string;
  status: string;
  isDownloadable: boolean;
  isCommentsEnabled: boolean;
}

export const updateVideoAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    updateVideo: build.mutation<Video, UpdateVideoRequest>({
      invalidatesTags: (result) => [
        { type: TAGS.VIDEOS, id: result?.projectId },
        TAGS.VIDEOS,
      ],
      query: ({ videoId, description, title, status, isDownloadable, isCommentsEnabled }) => ({
        url: `/videos/${videoId}`,
        method: 'PUT',
        body: { description, title, status, isDownloadable, isCommentsEnabled },
      }),
    }),
  }),
});

export const { useUpdateVideoMutation } = updateVideoAPI;
