import { baseAPI, TAGS } from '~stores/base-api';

import { type Video } from '../types/video';

interface ArchiveVideoRequest {
  videoId: string;
}

export const archiveVideoAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    archiveVideo: build.mutation<Video, ArchiveVideoRequest>({
      invalidatesTags: () => [TAGS.VIDEOS, TAGS.STORAGE],
      query: ({ videoId }) => ({
        url: '/videos/archive',
        method: 'POST',
        body: { videoId },
      }),
    }),

    unArchiveVideo: build.mutation<Video, ArchiveVideoRequest>({
      invalidatesTags: () => [TAGS.VIDEOS, TAGS.STORAGE],
      query: (body) => ({
        url: '/videos/unarchive',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const { useArchiveVideoMutation, useUnArchiveVideoMutation } =
  archiveVideoAPI;
