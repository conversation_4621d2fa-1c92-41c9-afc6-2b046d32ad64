import { baseAPI } from '~stores/base-api';

import { type Video } from '~features/video/types/video';

interface GetVideoParams {
  videoId: string;
}

export const getVideoAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getVideo: build.query<Video, GetVideoParams>({
      // providesTags: [TAGS.PROJECTS],
      query: ({ videoId }) => ({
        url: `/videos/${videoId}`,
      }),
    }),
  }),
});

export const { useGetVideoQuery } = getVideoAPI;
