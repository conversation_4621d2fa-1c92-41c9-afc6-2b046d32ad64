import { baseAPI } from '~stores/base-api';

interface DownloadVideoRequest {
  videoPath: string;
}

interface DownloadVideoResponse {
  signedUrl: string;
}

export const downloadVideoAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    downloadVideo: build.mutation<DownloadVideoResponse, DownloadVideoRequest>({
      query: ({ videoPath }) => ({
        url: `/videos/download`,
        method: 'POST',
        body: { videoPath },
      }),
    }),
  }),
});

export const { useDownloadVideoMutation } = downloadVideoAPI;
