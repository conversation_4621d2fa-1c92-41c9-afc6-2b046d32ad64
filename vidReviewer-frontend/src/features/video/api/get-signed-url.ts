import { baseAPI } from '~stores/base-api';

interface GetSignedURLRequest {
  fileName: string;
  fileType: string;
}

export interface GetSignedURLResponse {
  url: string;
  fileName: string;
}

export const getSignedUrlAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getSignedUrl: build.mutation<GetSignedURLResponse, GetSignedURLRequest>({
      query: (body) => ({
        url: '/videos/getSignedUrl',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const { useGetSignedUrlMutation } = getSignedUrlAPI;
