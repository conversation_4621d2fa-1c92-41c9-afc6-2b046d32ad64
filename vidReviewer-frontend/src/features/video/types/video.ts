export interface Video {
  _id: string;
  title: string;
  description: string;
  projectId: string;
  projectFolderId?: string | null;
  status: string;
  uploadedBy: string;
  videoUrl: string;
  comments: Array<unknown>;
  isDownloadable: boolean;
  isCommentsEnabled: boolean;
  height: number;
  width: number;
  size: number;
  storageClass: string;
  uploadedOn: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  videoThumbnail: string;
  hlsPlaylistUrl: string;
  customThumbnail?: string;
  thumbnailType?: 'auto' | 'custom';
  duration?: number;
}
