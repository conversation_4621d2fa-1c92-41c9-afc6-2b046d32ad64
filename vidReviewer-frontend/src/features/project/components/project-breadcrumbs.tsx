import { ChevronRight, Home } from 'lucide-react';
import { Button } from '~components/ui/button';
import { cn } from '~lib/utils';

interface BreadcrumbItem {
  id: string | null;
  name: string;
  type: 'home' | 'folder' | 'project' | 'sub-project';
}

interface ProjectBreadcrumbsProps {
  breadcrumbs: BreadcrumbItem[];
  onNavigate: (id: string | null, type: string) => void;
  className?: string;
}

export const ProjectBreadcrumbs = ({ 
  breadcrumbs, 
  onNavigate, 
  className 
}: ProjectBreadcrumbsProps) => {
  return (
    <nav className={cn('flex items-center space-x-1 text-base text-gray-600', className)}>
      <Button
        variant="ghost"
        size="sm"
        className="h-10 px-3 text-base text-gray-600 hover:text-gray-900"
        onClick={() => onNavigate(null, 'home')}
      >
        <Home className="h-5 w-5 mr-2" />
        Home
      </Button>
      
      {breadcrumbs.map((item, index) => (
        <div key={item.id || 'root'} className="flex items-center">
          <ChevronRight className="h-5 w-5 text-gray-400" />
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              'h-10 px-3 text-base text-gray-600 hover:text-gray-900',
              index === breadcrumbs.length - 1 && 'text-gray-900 font-medium'
            )}
            onClick={() => onNavigate(item.id, item.type)}
            disabled={index === breadcrumbs.length - 1}
          >
            {item.name}
          </Button>
        </div>
      ))}
    </nav>
  );
};
