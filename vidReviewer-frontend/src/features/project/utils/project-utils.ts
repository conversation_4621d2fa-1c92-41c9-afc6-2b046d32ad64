import { type Project } from '~features/project/types/project';
import { type Folder } from '~features/folder/types/folder';
import { type SubProject } from '~features/sub-project/types/sub-project';

export interface ProjectBreadcrumbItem {
  id: string | null;
  name: string;
  type: 'home' | 'folder' | 'project' | 'sub-project';
}

/**
 * Build breadcrumb items for project navigation
 */
export const buildProjectBreadcrumbs = (
  project: Project | null,
  parentFolder: Folder | null = null,
  allFolders: Folder[] = []
): ProjectBreadcrumbItem[] => {
  if (!project) {
    return [];
  }

  const breadcrumbs: ProjectBreadcrumbItem[] = [];

  // If project is in a folder, build folder breadcrumbs first
  if (project.folderId && parentFolder) {
    // Build folder path breadcrumbs
    const folderBreadcrumbs = buildFolderBreadcrumbs(parentFolder, allFolders);
    breadcrumbs.push(...folderBreadcrumbs);
  }

  // Add the project itself
  breadcrumbs.push({
    id: project._id,
    name: project.title,
    type: 'project',
  });

  return breadcrumbs;
};

/**
 * Build breadcrumb items for sub-project navigation
 */
export const buildSubProjectBreadcrumbs = (
  project: Project | null,
  subProject: SubProject | null,
  parentFolder: Folder | null = null,
  allFolders: Folder[] = [],
  allSubProjects: SubProject[] = []
): ProjectBreadcrumbItem[] => {
  if (!project || !subProject) {
    return [];
  }

  const breadcrumbs: ProjectBreadcrumbItem[] = [];

  // Start with project breadcrumbs
  const projectBreadcrumbs = buildProjectBreadcrumbs(project, parentFolder, allFolders);
  breadcrumbs.push(...projectBreadcrumbs);

  // Build sub-project path
  const subProjectPath = buildSubProjectPath(subProject, allSubProjects);
  breadcrumbs.push(...subProjectPath);

  return breadcrumbs;
};

/**
 * Build folder breadcrumbs for projects in folders
 */
const buildFolderBreadcrumbs = (
  currentFolder: Folder,
  allFolders: Folder[]
): ProjectBreadcrumbItem[] => {
  const breadcrumbs: ProjectBreadcrumbItem[] = [];
  const pathParts = currentFolder.path.split('/').filter(Boolean);
  
  // Build breadcrumbs by traversing the path
  let currentPath = '';
  for (const part of pathParts) {
    currentPath += `/${part}`;
    const folder = allFolders.find(f => f.path === currentPath);
    if (folder) {
      breadcrumbs.push({
        id: folder._id,
        name: folder.name,
        type: 'folder',
      });
    }
  }

  return breadcrumbs;
};

/**
 * Build sub-project path breadcrumbs
 */
const buildSubProjectPath = (
  currentSubProject: SubProject,
  allSubProjects: SubProject[]
): ProjectBreadcrumbItem[] => {
  const breadcrumbs: ProjectBreadcrumbItem[] = [];
  const pathParts = currentSubProject.path.split('/').filter(Boolean);

  console.log('🔍 buildSubProjectPath Debug:');
  console.log('Current sub-project path:', currentSubProject.path);
  console.log('Path parts:', pathParts);
  console.log('All sub-projects:', allSubProjects.map(sp => ({ id: sp._id, name: sp.name, path: sp.path })));

  // Build breadcrumbs by traversing the sub-project path
  let currentPath = '';
  for (const part of pathParts) {
    currentPath += `/${part}`;
    console.log('Looking for sub-project with path:', currentPath);
    const subProject = allSubProjects.find(sp => sp.path === currentPath);
    console.log('Found sub-project:', subProject ? { id: subProject._id, name: subProject.name, path: subProject.path } : 'NOT FOUND');
    if (subProject) {
      breadcrumbs.push({
        id: subProject._id,
        name: subProject.name,
        type: 'sub-project',
      });
    }
  }

  console.log('Final breadcrumbs from buildSubProjectPath:', breadcrumbs);
  return breadcrumbs;
};
