import { baseAPI, TAGS } from '~stores/base-api';

import { type Project } from '~features/project/types/project';

interface UpdateProjectParams {
  projectId: string;
  title: string;
  description: string;
}

export const updateProjectAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    updateProject: build.mutation<Project, UpdateProjectParams>({
      invalidatesTags: [TAGS.PROJECTS],
      query: ({ projectId, title, description }) => ({
        url: `/projects/${projectId}`,
        method: 'PUT',
        body: { title, description },
      }),
    }),
  }),
});

export const { useUpdateProjectMutation } = updateProjectAPI;
