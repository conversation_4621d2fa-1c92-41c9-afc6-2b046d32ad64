import { baseAPI, TAGS } from '~stores/base-api';

import { type Project } from '~features/project/types/project';

interface GetProjectParams {
  projectId: string;
}

export const getProjectAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getProject: build.query<Project, GetProjectParams>({
      providesTags: (_result, _error, { projectId }) => [
        { type: TAGS.PROJECTS, id: projectId },
      ],
      query: ({ projectId }) => ({
        url: `/projects/projectId/${projectId}`,
      }),
    }),
  }),
});

export const { useGetProjectQuery } = getProjectAPI;
