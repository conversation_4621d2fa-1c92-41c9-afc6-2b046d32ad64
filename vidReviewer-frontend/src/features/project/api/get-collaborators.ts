import { baseAPI, TAGS } from '~stores/base-api';

interface GetCollaboratorsParams {
  projectId: string;
}

interface Collaborator {
  name: string;
}

export const getCollaboratorsAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getCollaborators: build.query<Array<Collaborator>, GetCollaboratorsParams>({
      providesTags: (_result, _error, { projectId }) => [
        { type: TAGS.COLLABORATORS, id: projectId },
      ],
      query: ({ projectId }) => ({
        url: `projects/collaborators/${projectId}`,
      }),
    }),
  }),
});

export const { useGetCollaboratorsQuery } = getCollaboratorsAPI;
