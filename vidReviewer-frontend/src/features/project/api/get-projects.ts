import { baseAPI, TAGS } from '~stores/base-api';

import { type Project } from '~features/project/types/project';

interface GetProjectsParams {
  userId: string;
  folderId?: string | null;
}

interface GetProjectsResponse {
  projects: Array<Project>;
}

export const getProjectsAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    getProjects: build.query<GetProjectsResponse, GetProjectsParams>({
      providesTags: [TAGS.PROJECTS],
      query: ({ userId, folderId }) => ({
        url: `/projects/${userId}`,
        params: folderId !== undefined ? { folderId: folderId === null ? 'null' : folderId } : {},
      }),
    }),
  }),
});

export const { useGetProjectsQuery } = getProjectsAPI;
