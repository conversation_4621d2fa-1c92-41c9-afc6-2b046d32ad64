import { baseAPI, TAGS } from '~stores/base-api';

import { type Project } from '~features/project/types/project';

interface MoveProjectParams {
  projectId: string;
  folderId?: string | null;
}

export const moveProjectAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    moveProject: build.mutation<Project, MoveProjectParams>({
      invalidatesTags: [TAGS.PROJECTS],
      query: ({ projectId, folderId }) => ({
        url: `/projects/${projectId}/move`,
        method: 'PATCH',
        body: { folderId },
      }),
    }),
  }),
});

export const { useMoveProjectMutation } = moveProjectAPI;
