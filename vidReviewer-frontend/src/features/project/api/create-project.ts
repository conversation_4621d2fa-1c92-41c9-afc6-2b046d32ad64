import { baseAPI, TAGS } from '~stores/base-api';

import { type Project } from '~features/project/types/project';

interface CreateProjectParams {
  title: string;
  description: string;
  createdBy: string;
  name: string;
  email: string;
  folderId?: string;
}

export const createProjectAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    createProject: build.mutation<Project, CreateProjectParams>({
      invalidatesTags: [TAGS.PROJECTS],
      query: (body) => ({
        url: '/projects',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const { useCreateProjectMutation } = createProjectAPI;
