import { baseAPI, TAGS } from '~stores/base-api';

interface DeleteProjectParams {
  projectId: string;
}

export interface DeleteProjectResponse {
  message: string;
  deletedProject: DeletedProject;
  deletedVideosCount: number;
}

export interface DeletedProject {
  _id: string;
  createdBy: string;
  title: string;
  description: string;
  createdAt: string;
  collaborators: unknown;
  __v: number;
}

export const deleteProjectAPI = baseAPI.injectEndpoints({
  endpoints: (build) => ({
    deleteProject: build.mutation<DeleteProjectResponse, DeleteProjectParams>({
      invalidatesTags: [TAGS.PROJECTS],
      query: ({ projectId }) => ({
        url: `/projects/${projectId}`,
        method: 'DELETE',
      }),
    }),
  }),
});

export const { useDeleteProjectMutation } = deleteProjectAPI;
