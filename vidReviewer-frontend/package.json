{"name": "vidReviewer-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc -b && vite build", "dev": "vite", "format": "prettier --write .", "lint": "eslint . --cache", "lint-fix": "eslint . --fix --cache", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@reduxjs/toolkit": "^2.5.0", "@tanstack/react-router": "^1.112.11", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/eslint-config-prettier": "^6.11.3", "async-mutex": "^0.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.1", "input-otp": "^1.4.1", "lucide-react": "^0.468.0", "next-themes": "^0.4.4", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.0", "react-player": "^2.16.0", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.7", "react-select": "^5.10.0", "recharts": "^2.14.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.15.0", "@tanstack/router-devtools": "^1.87.9", "@tanstack/router-plugin": "^1.112.11", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/eslint-config-prettier": "~6.11.3", "@types/eslint-plugin-tailwindcss": "~3.17.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/parser": "^8.20.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import-x": "^4.5.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.12.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.16", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.3", "vite-aliases": "^0.11.7"}}